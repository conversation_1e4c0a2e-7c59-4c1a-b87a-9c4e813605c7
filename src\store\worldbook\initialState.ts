import type { 
  Worldbook, 
  WorldbookEntry, 
  SearchParams,
  BulkOperationResult,
  ImportResultDetail 
} from '@/types/worldbook';

/**
 * 世界书Store状态接口
 * 基于新的类型系统重新设计
 */
export interface WorldbookStoreState {
  // ====== 世界书管理 ======
  worldbooks: Worldbook[];
  currentWorldbook: Worldbook | null;
  selectedWorldbookId: string | null;
  
  // ====== 条目管理 ======
  entries: WorldbookEntry[];
  currentEntry: WorldbookEntry | null;
  selectedEntryIds: string[];
  
  // ====== 分页和搜索 ======
  searchParams: SearchParams;
  entriesTotal: number;
  entriesPage: number;
  entriesPageSize: number;
  entriesHasMore: boolean;
  
  // ====== 加载状态 ======
  loading: boolean;                    // 世界书列表加载状态
  entriesLoading: boolean;             // 条目列表加载状态
  importing: boolean;                  // 导入状态
  entryLoadingIds: string[];           // 正在加载的条目ID
  entryUpdatingIds: string[];          // 正在更新的条目ID
  worldbookLoadingIds: string[];       // 正在操作的世界书ID
  
  // ====== 批量操作状态 ======
  bulkOperationLoading: boolean;       // 批量操作加载状态
  lastBulkOperationResult: BulkOperationResult | null; // 最后一次批量操作结果
  
  // ====== 导入导出状态 ======
  lastImportResult: ImportResultDetail | null; // 最后一次导入结果
  
  // ====== UI状态 ======
  sidebarCollapsed: boolean;           // 侧边栏折叠状态
  entryEditorVisible: boolean;         // 条目编辑器可见状态
  worldbookManagerVisible: boolean;    // 世界书管理器可见状态
  
  // ====== 过滤和排序 ======
  filterEnabled: boolean | null;       // 启用状态过滤 (null=全部, true=启用, false=禁用)
  sortBy: 'order' | 'title' | 'createdAt' | 'updatedAt' | 'probability';
  sortOrder: 'asc' | 'desc';
  
  // ====== 统计信息 ======
  stats: {
    totalWorldbooks: number;
    enabledWorldbooks: number;
    totalEntries: number;
    worldbooksWithAgent: number;
  } | null;
}

/**
 * 初始状态
 */
export const initialState: WorldbookStoreState = {
  // 世界书管理
  worldbooks: [],
  currentWorldbook: null,
  selectedWorldbookId: null,
  
  // 条目管理
  entries: [],
  currentEntry: null,
  selectedEntryIds: [],
  
  // 分页和搜索
  searchParams: {
    page: 1,
    pageSize: 20,
    query: '',
    enabled: undefined,
    activationMode: undefined,
    position: undefined,
    groupName: undefined,
    hasKeys: undefined,
    hasSecondaryKeys: undefined,
    selectiveLogic: undefined,
    orderMin: undefined,
    orderMax: undefined,
    probabilityMin: undefined,
    probabilityMax: undefined,
    sortBy: 'order',
    sortOrder: 'asc',
  },
  entriesTotal: 0,
  entriesPage: 1,
  entriesPageSize: 20,
  entriesHasMore: false,
  
  // 加载状态
  loading: false,
  entriesLoading: false,
  importing: false,
  entryLoadingIds: [],
  entryUpdatingIds: [],
  worldbookLoadingIds: [],
  
  // 批量操作状态
  bulkOperationLoading: false,
  lastBulkOperationResult: null,
  
  // 导入导出状态
  lastImportResult: null,
  
  // UI状态
  sidebarCollapsed: false,
  entryEditorVisible: false,
  worldbookManagerVisible: false,
  
  // 过滤和排序
  filterEnabled: null,
  sortBy: 'order',
  sortOrder: 'asc',
  
  // 统计信息
  stats: null,
};
