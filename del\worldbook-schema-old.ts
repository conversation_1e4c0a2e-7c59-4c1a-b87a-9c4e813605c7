/* eslint-disable sort-keys-fix/sort-keys-fix  */
import {
  boolean,
  index,
  integer,
  pgTable,
  text,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { createInsertSchema } from 'drizzle-zod';

import { idGenerator } from '@/database/utils/idGenerator';

import { createdAt, updatedAt } from './_helpers';
import { chunks } from './rag';
import { users } from './user';

export const worldbooks = pgTable(
  'worldbooks',
  {
    id: text('id')
      .$defaultFn(() => idGenerator('worldbooks'))
      .primaryKey(),

    name: text('name').notNull(),
    description: text('description'),
    
    userId: text('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),
    
    enabled: boolean('enabled').default(true),
    clientId: text('client_id'),

    createdAt: createdAt(),
    updatedAt: updatedAt(),
  },
  (t) => ({
    userIdIdx: index('worldbooks_user_id_idx').on(t.userId),
    clientIdUnique: uniqueIndex('worldbooks_client_id_user_id_unique').on(
      t.clientId,
      t.userId,
    ),
  }),
);

export const worldbookChunks = pgTable(
  'worldbook_chunks',
  {
    // 基础标识字段
    id: text('id')
      .$defaultFn(() => idGenerator('worldbookChunks'))
      .primaryKey(),
    title: text('title').notNull(),
    enabled: boolean('enabled').default(true),

    // 关联字段
    worldbookId: text('worldbook_id')
      .references(() => worldbooks.id, { onDelete: 'cascade' })
      .notNull(),
    chunkId: uuid('chunk_id')
      .references(() => chunks.id, { onDelete: 'cascade' })
      .notNull(),

    // 激活规则字段
    keys: text('keys').array().default([]),
    keysSecondary: text('keys_secondary').array().default([]),
    selectiveLogic: integer('selective_logic').default(0), // 0=AND_ANY, 1=NOT_ALL, 2=NOT_ANY, 3=AND_ALL

    // 行为控制字段
    order: integer('order').default(100),
    constant: boolean('constant').default(false),
    probability: integer('probability').default(100),
    position: integer('position').default(0), // 0=系统消息后，1=历史消息中

    // 高级功能字段
    scanDepth: integer('scan_depth'),
    groupName: text('group_name'),
    sticky: integer('sticky'),
    cooldown: integer('cooldown'),
    delay: integer('delay'),
    excludeRecursion: boolean('exclude_recursion').default(false),
    delayUntilRecursion: integer('delay_until_recursion').default(0),
    groupWeight: integer('group_weight').default(100),

    // 匹配选项字段
    caseSensitive: boolean('case_sensitive'),
    matchWholeWords: boolean('match_whole_words'),
    useRegex: boolean('use_regex').default(false),

    // 用户关联字段
    userId: text('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),
    clientId: text('client_id'),

    // 时间戳字段
    createdAt: createdAt(),
    updatedAt: updatedAt(),
  },
  (t) => ({
    worldbookIdIdx: index('worldbook_chunks_worldbook_id_idx').on(t.worldbookId),
    userIdIdx: index('worldbook_chunks_user_id_idx').on(t.userId),
    enabledIdx: index('worldbook_chunks_enabled_idx').on(t.enabled),
    orderIdx: index('worldbook_chunks_order_idx').on(t.order),
    chunkIdIdx: index('worldbook_chunks_chunk_id_idx').on(t.chunkId),
    clientIdUnique: uniqueIndex('worldbook_chunks_client_id_user_id_unique').on(
      t.clientId,
      t.userId,
    ),
  }),
);

export const insertWorldbooksSchema = createInsertSchema(worldbooks);
export const insertWorldbookChunksSchema = createInsertSchema(worldbookChunks);

export type NewWorldbook = typeof worldbooks.$inferInsert;
export type WorldbookItem = typeof worldbooks.$inferSelect;
export type NewWorldbookChunk = typeof worldbookChunks.$inferInsert;
export type WorldbookChunkItem = typeof worldbookChunks.$inferSelect;
