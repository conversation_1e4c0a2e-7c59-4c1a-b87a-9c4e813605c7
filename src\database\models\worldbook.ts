import { and, count, desc, eq } from 'drizzle-orm';

import { LobeChatDatabase, Transaction } from '@/database/type';
import type {
  CreateWorldbookData,
  UpdateWorldbookData,
  Worldbook,
} from '@/types/worldbook';

import { NewWorldbook, WorldbookItem, worldbookChunks, worldbooks } from '../schemas';
import { agents, agentsWorldbooks } from '../schemas/agent';
import { WorldbookChunkModel } from './worldbookChunk';

export class WorldbookModel {
  private userId: string;
  private db: LobeChatDatabase;

  constructor(db: LobeChatDatabase, userId: string) {
    this.userId = userId;
    this.db = db;
  }

  // ====== CRUD Operations ======

  /**
   * 创建世界书
   */
  create = async (params: CreateWorldbookData): Promise<Worldbook> => {
    const [result] = await this.db
      .insert(worldbooks)
      .values({
        name: params.name,
        description: params.description,
        enabled: true,
        userId: this.userId,
      } as NewWorldbook)
      .returning();

    return this.transformToWorldbook(result);
  };

  /**
   * 更新世界书
   */
  update = async (id: string, params: UpdateWorldbookData): Promise<Worldbook | null> => {
    const [result] = await this.db
      .update(worldbooks)
      .set({
        ...params,
        updatedAt: new Date(),
      })
      .where(and(eq(worldbooks.id, id), eq(worldbooks.userId, this.userId)))
      .returning();

    if (!result) return null;
    return this.transformToWorldbook(result);
  };

  /**
   * 删除世界书及其所有相关数据
   */
  delete = async (id: string, trx?: Transaction): Promise<void> => {
    const executeDelete = async (tx: Transaction) => {
      // 1. 先删除所有相关的chunks（这会级联删除embeddings和worldbook_chunks）
      const worldbookChunkModel = new WorldbookChunkModel(tx, this.userId);
      await worldbookChunkModel.deleteByWorldbookId(id);

      // 2. 删除世界书记录
      await tx
        .delete(worldbooks)
        .where(and(eq(worldbooks.id, id), eq(worldbooks.userId, this.userId)));
    };

    // 如果已经在事务中，直接执行；否则创建新事务
    if (trx) {
      await executeDelete(trx);
    } else {
      await this.db.transaction(executeDelete);
    }
  };

  /**
   * 删除用户的所有世界书
   */
  deleteAll = async (trx?: Transaction): Promise<void> => {
    const executeDeleteAll = async (tx: Transaction) => {
      // 1. 获取所有世界书ID
      const worldbookList = await this.findByUserId(this.userId);

      // 2. 删除每个世界书的chunks
      const worldbookChunkModel = new WorldbookChunkModel(tx, this.userId);
      for (const worldbook of worldbookList) {
        await worldbookChunkModel.deleteByWorldbookId(worldbook.id);
      }

      // 3. 删除所有世界书记录
      await tx.delete(worldbooks).where(eq(worldbooks.userId, this.userId));
    };

    // 如果已经在事务中，直接执行；否则创建新事务
    if (trx) {
      await executeDeleteAll(trx);
    } else {
      await this.db.transaction(executeDeleteAll);
    }
  };

  // ====== Query Operations ======

  /**
   * 查询用户的所有世界书
   */
  query = async (): Promise<Worldbook[]> => {
    const data = await this.db
      .select({
        id: worldbooks.id,
        name: worldbooks.name,
        description: worldbooks.description,
        enabled: worldbooks.enabled,
        userId: worldbooks.userId,
        clientId: worldbooks.clientId,
        createdAt: worldbooks.createdAt,
        updatedAt: worldbooks.updatedAt,
        // 条目计数
        entryCount: count(worldbookChunks.id),
        // 专属agent信息
        primaryAgentId: agentsWorldbooks.agentId,
        primaryAgentTitle: agents.title,
      })
      .from(worldbooks)
      .leftJoin(
        worldbookChunks,
        and(
          eq(worldbookChunks.worldbookId, worldbooks.id),
          eq(worldbookChunks.enabled, true)
        )
      )
      .leftJoin(
        agentsWorldbooks,
        and(
          eq(agentsWorldbooks.worldbookId, worldbooks.id),
          eq(agentsWorldbooks.isPrimary, true),
          eq(agentsWorldbooks.userId, this.userId)
        )
      )
      .leftJoin(agents, eq(agents.id, agentsWorldbooks.agentId))
      .where(eq(worldbooks.userId, this.userId))
      .groupBy(
        worldbooks.id,
        worldbooks.name,
        worldbooks.description,
        worldbooks.enabled,
        worldbooks.userId,
        worldbooks.clientId,
        worldbooks.createdAt,
        worldbooks.updatedAt,
        agentsWorldbooks.agentId,
        agents.title
      )
      .orderBy(desc(worldbooks.updatedAt));

    return data.map(item => ({
      id: item.id,
      name: item.name,
      description: item.description || undefined,
      enabled: item.enabled,
      entryCount: item.entryCount || 0,
      lastActivated: undefined, // TODO: 可以后续添加激活时间追踪
      userId: item.userId,
      clientId: item.clientId || undefined,
      createdAt: item.createdAt.toISOString(),
      updatedAt: item.updatedAt.toISOString(),
      // 专属agent信息
      primaryAgent: item.primaryAgentId ? {
        id: item.primaryAgentId,
        title: item.primaryAgentTitle || '未命名Agent'
      } : undefined,
    }));
  };

  /**
   * 根据ID查找世界书
   */
  findById = async (id: string): Promise<Worldbook | null> => {
    const data = await this.db.query.worldbooks.findFirst({
      where: and(eq(worldbooks.id, id), eq(worldbooks.userId, this.userId)),
    });

    if (!data) return null;
    return this.transformToWorldbook(data);
  };

  /**
   * 根据用户ID查找世界书（内部使用）
   */
  findByUserId = async (userId: string): Promise<WorldbookItem[]> => {
    return this.db.query.worldbooks.findMany({
      where: eq(worldbooks.userId, userId),
      orderBy: desc(worldbooks.updatedAt),
    });
  };

  // ====== Import/Export Operations ======

  /**
   * 导入世界书（创建世界书和条目）
   */
  importWorldbook = async (data: {
    worldbook: CreateWorldbookData;
    entries: any[]; // 使用any[]，具体类型由WorldbookChunkModel处理
  }) => {
    return this.db.transaction(async (trx) => {
      // 1. 创建世界书
      console.log('📚 [WorldbookModel] Creating worldbook...');
      const worldbook = await this.create(data.worldbook);

      console.log('✅ [WorldbookModel] Worldbook created:', worldbook);

      // 2. 批量创建条目
      console.log('📝 [WorldbookModel] Creating entries...');
      const entriesWithWorldbookId = data.entries.map(entry => ({
        ...entry,
        worldbookId: worldbook.id,
      }));

      const worldbookChunkModel = new WorldbookChunkModel(trx, this.userId);
      const createdEntries = await worldbookChunkModel.bulkCreate(entriesWithWorldbookId);

      console.log('✅ [WorldbookModel] Entries created:', createdEntries.length);

      return {
        worldbookId: worldbook.id,
        worldbookName: worldbook.name,
        totalEntries: data.entries.length,
        successfulEntries: createdEntries.length,
        failedEntries: data.entries.length - createdEntries.length,
        errors: [], // 如果有错误，可以在这里记录
      };
    });
  };

  // ====== Helper Methods ======

  /**
   * 将数据库记录转换为前端类型
   */
  private transformToWorldbook = (item: WorldbookItem): Worldbook => {
    return {
      id: item.id,
      name: item.name,
      description: item.description || undefined,
      enabled: item.enabled,
      entryCount: 0, // TODO: 可以后续添加条目计数查询
      lastActivated: undefined, // TODO: 可以后续添加激活时间追踪
      userId: item.userId,
      clientId: item.clientId || undefined,
      createdAt: item.createdAt.toISOString(),
      updatedAt: item.updatedAt.toISOString(),
    };
  };

  // ====== Static Methods ======

  /**
   * 跨用户访问的静态方法（管理员功能）
   */
  static findById = async (db: LobeChatDatabase, id: string): Promise<WorldbookItem | undefined> =>
    db.query.worldbooks.findFirst({
      where: eq(worldbooks.id, id),
    });
}