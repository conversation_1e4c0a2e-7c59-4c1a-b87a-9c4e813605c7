import { devtools } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import { createWithEqualityFn } from 'zustand/traditional';

import { type WorldbookStoreState, initialState } from './initialState';
import { worldbookSelectors, entrySelectors, combinedSelectors } from './selectors';
import { type WorldbookEntryAction, createWorldbookEntrySlice } from './slices/entry/action';
import { entryManagerSelectors } from './slices/entry/selectors';
import { type WorldbookAction, createWorldbookSlice } from './slices/worldbook/action';
import { worldbookManagerSelectors } from './slices/worldbook/selectors';

export type WorldbookStore = WorldbookStoreState & WorldbookAction & WorldbookEntryAction;

// 导出所有selectors以便使用
export {
  worldbookSelectors,
  entrySelectors,
  combinedSelectors,
  worldbookManagerSelectors,
  entryManagerSelectors,
};

// 创建store实例

export const useWorldbookStore = createWithEqualityFn<WorldbookStore>()(
  devtools(
    (...args) => ({
      ...initialState,
      ...createWorldbookSlice(...args),
      ...createWorldbookEntrySlice(...args),
    }),
    { name: 'LobeChat_Worldbook' },
  ),
  shallow,
);
