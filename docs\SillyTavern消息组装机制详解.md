# SillyTavern 消息组装机制详解

## 概述

SillyTavern的消息组装是一个复杂而精巧的系统，它将用户消息、角色设定、记忆系统、扩展功能等多个组件整合成最终发送给AI的提示词。本文档详细说明这个过程的工作原理。

## 消息组装的整体架构

### 核心组件结构

```
最终提示词 = beforeScenarioAnchor + 
            storyString + 
            afterScenarioAnchor + 
            mesExmString + 
            mesSendString + 
            generatedPromptCache
```

### 各组件说明

- **beforeScenarioAnchor**: 场景前锚点，包含扩展在提示词前的内容
- **storyString**: 角色描述、人格设定、场景描述等核心信息
- **afterScenarioAnchor**: 场景后锚点，包含扩展在提示词中的内容
- **mesExmString**: 示例对话内容
- **mesSendString**: 实际聊天消息（包含扩展提示词）
- **generatedPromptCache**: 生成缓存内容

## 扩展提示词注入机制

### 注入方式

SillyTavern提供统一的扩展注入接口：`setExtensionPrompt(key, value, position, depth, scan, role)`

**参数说明**：
- `key`: 扩展标识符
- `value`: 要注入的内容
- `position`: 注入位置（IN_PROMPT/IN_CHAT/BEFORE_PROMPT）
- `depth`: 注入深度（从最新消息向前计算）
- `scan`: 是否扫描世界书
- `role`: 消息角色（SYSTEM/USER/ASSISTANT）

### 注入位置类型

1. **BEFORE_PROMPT (2)**: 在整个提示词之前（beforeScenarioAnchor位置）
2. **IN_PROMPT (0)**: 在提示词中间（afterScenarioAnchor位置）
3. **IN_CHAT (1)**: 在聊天消息中

## 三种记忆系统的插入位置和机制

### 1. Memory Extension (自动摘要记忆)

**原生版本**：
- **插入方式**：通过`setExtensionPrompt`注入
- **默认位置**：IN_PROMPT (0) - 在`afterScenarioAnchor`位置
- **存储位置**：消息的`extra.memory`字段中
- **注入位置**：系统消息中，作为背景知识

**社区增强版本**：
- **插入方式**：通过世界书系统注入到`storyString`中的`worldInfoBefore`或`worldInfoAfter`部分
- **存储位置**：聊天记录世界书中作为独立条目
- **注入位置**：角色设定附近，作为背景知识

**分层摘要架构**：
- **小总结**：对10-20条消息进行局部总结
- **大总结**：将多个小总结合并为更高层次的压缩摘要
- **楼层隐藏**：已总结的消息被隐藏，减少上下文占用
- **世界书集成**：总结内容自动存储到聊天记录世界书中

**详细工作流程**：

*小总结阶段*：
1. 检测未隐藏楼层范围
2. 切换到专用总结预设
3. 对指定范围消息进行总结
4. 隐藏已总结的楼层
5. 将总结存储到世界书缓存

*大总结阶段*：
1. 当小总结累积到阈值时触发
2. 将多个小总结合并处理
3. 生成更高层次的压缩摘要
4. 替换原有的小总结内容

**多个总结的组装方式**：
1. 小总结和大总结作为独立的世界书条目存储
2. 每个条目有自己的优先级（order）和位置（position）
3. 世界书系统按优先级排序后用换行符连接
4. 最终形成统一的`worldInfoBefore`或`worldInfoAfter`字符串

**在最终提示词中的位置**：
```
最终提示词 = beforeScenarioAnchor +
            storyString {
              角色设定 +
              worldInfoBefore + [多个总结内容] +
              场景描述 +
              worldInfoAfter + [其他世界书内容]
            } +
            afterScenarioAnchor +
            mesExmString +
            mesSendString
```

### 2. Vectors Extension (向量化记忆)

#### 2.1 原生Vectors插件 (public/scripts/extensions/vectors/)

**插入方式**：通过`setExtensionPrompt`注入
**默认位置**：IN_PROMPT (0) - 在`afterScenarioAnchor`位置
**默认模板**：`Past events:\n{{text}}`

**关键特点**：
- **聊天记录向量化**：将历史消息转换为向量存储
- **文件支持**：支持DataBank文件向量化
- **世界书集成**：可向量化世界书条目
- **简单配置**：基础的向量检索和注入功能

#### 2.2 vectors-enhanced插件 (other/vectors-enhanced-main/)

**插入方式**：通过`setExtensionPrompt`注入
**默认位置**：IN_PROMPT (0) - 在`afterScenarioAnchor`位置
**默认模板**：`<must_know>以下是从相关背景知识库，包含重要的上下文、设定或细节：\n{{text}}</must_know>`

**增强特性**：
- **多任务管理**：同一聊天可创建多个独立向量化任务
- **高级标签筛选**：支持复杂的内容提取和排除规则
- **智能重复检测**：基于内容哈希的重复检测机制
- **隐藏消息管理**：批量隐藏/显示消息范围
- **多后端支持**：Transformers、vLLM、Ollama
- **自定义注入控制**：灵活的模板和标签配置

**两种插件的共同特点**：
- 都通过`setExtensionPrompt`注入到`afterScenarioAnchor`位置
- 都支持IN_PROMPT和IN_CHAT模式（通过position参数控制）
- 都进行实时向量检索和相似度匹配

### 3. st-memory-enhancement (结构化记忆)

**插入方式**：直接修改`eventData.chat`数组
**插入位置**：聊天消息数组中的指定深度
**角色类型**：可配置为system/user/assistant

**深度注入机制**：
```javascript
if (deep === 0) {
    eventData.chat.push({ role: getMesRole(), content: promptContent });
} else {
    eventData.chat.splice(-deep, 0, { role: getMesRole(), content: promptContent });
}
```

**关键特点**：
- **真正的深度注入**：确实插入到对话历史中间
- **角色可配置**：可以选择以system、user或assistant身份注入
- **时间点控制**：通过deep参数精确控制在对话的哪个时间点提供信息

### 三种记忆系统的位置对比

| 记忆系统 | 注入位置 | 配置方式 | 实际效果 |
|---------|----------|----------|----------|
| **Memory Extension (原生)** | afterScenarioAnchor位置 | position/depth参数 | 作为背景信息，在系统消息中 |
| **Memory Extension (社区增强)** | storyString中的worldInfo部分 | 世界书优先级和位置 | 作为背景信息，紧邻角色设定 |
| **Vectors Extension (原生)** | afterScenarioAnchor位置 | position参数控制 | 基础向量检索，模板简单 |
| **vectors-enhanced插件** | afterScenarioAnchor位置 | position参数控制 | 增强功能，多任务管理 |
| **结构化记忆** | eventData.chat数组中间 | deep参数控制深度 | 在特定时间点提供上下文 |

## 完整的消息组装示例

### 场景设置
- 角色：小红（活泼的女孩）
- 用户：张三
- 同时启用三种记忆系统
- 当前对话已进行15轮

### 三种记忆系统的具体内容

**Memory Extension摘要**：
```
Summary: 张三和小红讨论了兴趣爱好，小红喜欢画画和音乐。他们还聊了天气，小红喜欢晴天。
```

**vectors-enhanced插件检索**：
```
<must_know>以下是从相关背景知识库，包含重要的上下文、设定或细节：
小红: 我最喜欢的颜色是粉色，让我感觉很温暖。
张三: 你平时都画些什么呢？
小红: 我喜欢画风景和小动物，特别是小猫咪。
</must_know>
```

**结构化记忆表格**：
```
当前场景信息：
角色状态表：
| 角色 | 心情 | 位置 | 活动 |
|------|------|------|------|
| 小红 | 开心 | 家里 | 聊天 |
| 张三 | 好奇 | 家里 | 提问 |
```

### 实际的OpenAI消息组装示例

#### 场景1：社区增强版Memory Extension + vectors-enhanced插件

```json
[
  {
    "role": "system",
    "content": "你是小红，一个活泼开朗的女孩。你喜欢和朋友聊天，总是充满正能量。\n\n[小总结1] 张三询问了小红的兴趣爱好，小红表示喜欢画画，特别是风景和小动物。\n\n[小总结2] 他们讨论了天气话题，小红说喜欢晴天因为可以出去玩。\n\n[大总结] 张三和小红进行了友好的交流，涉及兴趣爱好和天气偏好，展现了小红活泼开朗的性格。\n\n<must_know>以下是从相关背景知识库，包含重要的上下文、设定或细节：\n小红: 我最喜欢的颜色是粉色，让我感觉很温暖。\n张三: 你平时都画些什么呢？\n小红: 我喜欢画风景和小动物，特别是小猫咪。\n</must_know>"
  },
  {
    "role": "user",
    "content": "张三: 你今天心情怎么样？"
  },
  {
    "role": "assistant",
    "content": "小红: 今天心情特别好！阳光明媚的，让人忍不住想要出去走走呢~"
  },
  {
    "role": "user",
    "content": "张三: 那我们一起去公园走走吧？"
  }
]
```

#### 场景2：原生Vectors Extension (IN_CHAT模式) + 结构化记忆

```json
[
  {
    "role": "system",
    "content": "你是小红，一个活泼开朗的女孩。你喜欢和朋友聊天，总是充满正能量。"
  },
  {
    "role": "user",
    "content": "张三: 你今天心情怎么样？"
  },
  {
    "role": "system",
    "content": "Past events:\n小红: 我最喜欢的颜色是粉色，让我感觉很温暖。\n张三: 你平时都画些什么呢？"
  },
  {
    "role": "assistant",
    "content": "小红: 今天心情特别好！阳光明媚的，让人忍不住想要出去走走呢~"
  },
  {
    "role": "system",
    "content": "当前场景信息：\n| 角色 | 心情 | 位置 |\n|------|------|------|\n| 小红 | 开心 | 家里 |\n| 张三 | 好奇 | 家里 |"
  },
  {
    "role": "user",
    "content": "张三: 那我们一起去公园走走吧？"
  }
]
```



### 最佳实践建议

1. **Memory Extension (社区增强版)**：
   - 适合长期对话的分层记忆管理
   - 通过楼层隐藏有效减少token消耗
   - 世界书集成确保AI能持续访问历史摘要
   - 总结内容出现在角色设定附近，作为背景知识

2. **原生Vectors Extension**：适合基础的向量检索和历史背景提供
3. **vectors-enhanced插件**：适合复杂场景的多任务向量管理和高级内容筛选
4. **结构化记忆**：适合提供结构化的状态信息和数据

### 社区增强版本的优势

**相比原生版本的改进**：
- **多层摘要**：小总结→大总结的递进式压缩，信息保真度更高
- **楼层管理**：自动隐藏机制，显著减少上下文长度
- **世界书集成**：无缝集成到SillyTavern生态，AI可直接读取
- **错误恢复**：失败保护机制，便于调试和重试
- **正则处理**：多层内容清理，提升摘要质量

**实际应用效果**：
- 支持超长对话（数百轮以上）
- 保持对话连贯性和重要信息
- 显著提升响应速度和稳定性

## 消息处理的关键特性

### Token管理
- 自动计算各组件的Token消耗
- 当接近上下文限制时自动裁剪历史消息
- 优先保留重要信息（角色设定、记忆摘要等）

### 动态调整
- 根据不同API（OpenAI、Claude等）调整消息格式
- 支持指令模式和聊天模式的切换
- 自动处理特殊字符和格式

### 扩展兼容性
- 统一的扩展接口确保各插件协同工作
- 支持扩展优先级和冲突处理
- 提供丰富的钩子函数供扩展使用

## 技术优势

1. **模块化设计**：各组件独立工作，易于维护和扩展
2. **灵活配置**：用户可以自定义各种参数和模板
3. **高效处理**：智能的Token管理和缓存机制
4. **兼容性强**：支持多种AI服务和消息格式

这种精心设计的消息组装机制使得SillyTavern能够为用户提供丰富、连贯、个性化的AI对话体验。
