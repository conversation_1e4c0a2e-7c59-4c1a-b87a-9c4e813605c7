/**
 * 世界书Store模块导出
 * 提供完整的世界书状态管理功能
 */

// 主要Store和类型
export {
  useWorldbookStore,
  type WorldbookStore,
} from './store';

// 状态类型
export type { WorldbookStoreState } from './initialState';

// 操作类型
export type { WorldbookAction } from './slices/worldbook/action';
export type { WorldbookEntryAction } from './slices/entry/action';

// 选择器
export {
  worldbookSelectors,
  entrySelectors,
  combinedSelectors,
} from './selectors';

// 组合Hook
export {
  useWorldbookManager,
  useEntryManager,
  useSearchAndFilter,
  useWorldbookUI,
} from './store';

/**
 * 使用指南
 * 
 * 1. 基础使用
 * ```tsx
 * import { useWorldbookStore, worldbookSelectors } from '@/store/worldbook';
 * 
 * const MyComponent = () => {
 *   const worldbooks = useWorldbookStore(worldbookSelectors.worldbooks);
 *   const fetchWorldbooks = useWorldbookStore(s => s.fetchWorldbooks);
 *   
 *   useEffect(() => {
 *     fetchWorldbooks();
 *   }, [fetchWorldbooks]);
 *   
 *   return <div>{worldbooks.length} worldbooks</div>;
 * };
 * ```
 * 
 * 2. 使用组合Hook
 * ```tsx
 * import { useWorldbookManager } from '@/store/worldbook';
 * 
 * const WorldbookList = () => {
 *   const {
 *     worldbooks,
 *     loading,
 *     fetchWorldbooks,
 *     createWorldbook,
 *   } = useWorldbookManager();
 *   
 *   // 组件逻辑...
 * };
 * ```
 * 
 * 3. 条目管理
 * ```tsx
 * import { useEntryManager } from '@/store/worldbook';
 * 
 * const EntryList = () => {
 *   const {
 *     entries,
 *     selectedEntryIds,
 *     bulkDeleteEntries,
 *     toggleEntrySelection,
 *   } = useEntryManager();
 *   
 *   // 组件逻辑...
 * };
 * ```
 * 
 * 4. 搜索和过滤
 * ```tsx
 * import { useSearchAndFilter } from '@/store/worldbook';
 * 
 * const SearchBar = () => {
 *   const {
 *     searchParams,
 *     setSearchParams,
 *     hasActiveSearch,
 *   } = useSearchAndFilter();
 *   
 *   // 组件逻辑...
 * };
 * ```
 * 
 * 5. UI状态管理
 * ```tsx
 * import { useWorldbookUI } from '@/store/worldbook';
 * 
 * const Layout = () => {
 *   const {
 *     sidebarCollapsed,
 *     toggleSidebar,
 *     entryEditorVisible,
 *     setEntryEditorVisible,
 *   } = useWorldbookUI();
 *   
 *   // 组件逻辑...
 * };
 * ```
 */
