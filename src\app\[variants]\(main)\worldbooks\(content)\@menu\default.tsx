'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import CreateWorldbookModal from '@/features/WorldbookManager/CreateWorldbookModal';
import EditWorldbookModal from '@/features/WorldbookManager/EditWorldbookModal';
import ImportModal from '@/features/WorldbookManager/ImportModal';
import SettingsModal from '@/features/WorldbookManager/SettingsModal';
import WorldbookList from '@/features/WorldbookManager/WorldbookList';
import WorldbookPanelTitle from '@/features/WorldbookManager/components/WorldbookPanelTitle';
import { useWorldbookStore } from '@/store/worldbook';

const WorldbookMenuPage = () => {
  const { t } = useTranslation('worldbook');

  // 模态框状态管理
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editWorldbookData, setEditWorldbookData] = useState<any>(null);
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [settingsModalOpen, setSettingsModalOpen] = useState(false);

  const { fetchWorldbooks } = useWorldbookStore();

  const handleCreateWorldbook = () => {
    setCreateModalOpen(true);
  };

  const handleImportWorldbook = () => {
    setImportModalOpen(true);
  };

  const handleOpenSettings = () => {
    setSettingsModalOpen(true);
  };

  const handleEditWorldbook = (worldbook: any) => {
    setEditWorldbookData(worldbook);
    setEditModalOpen(true);
  };

  return (
    <Flexbox gap={16} height={'100%'}>
      <Flexbox paddingInline={8} height={'100%'} style={{ display: 'flex', flexDirection: 'column' }}>
        <WorldbookPanelTitle
          desc={t('description')}
          title={t('title')}
          onCreateWorldbook={handleCreateWorldbook}
          onImportWorldbook={handleImportWorldbook}
          onOpenSettings={handleOpenSettings}
        />
        <div style={{ flex: 1, overflow: 'hidden' }}>
          <WorldbookList onEditWorldbook={handleEditWorldbook} />
        </div>
      </Flexbox>

      {/* 模态框 */}
      <CreateWorldbookModal
        open={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        onSuccess={() => {
          setCreateModalOpen(false);
          fetchWorldbooks();
        }}
      />

      <ImportModal
        open={importModalOpen}
        onClose={() => setImportModalOpen(false)}
        onSuccess={() => {
          setImportModalOpen(false);
          fetchWorldbooks();
        }}
      />

      <EditWorldbookModal
        open={editModalOpen}
        worldbook={editWorldbookData}
        onClose={() => {
          setEditModalOpen(false);
          setEditWorldbookData(null);
        }}
        onSuccess={() => {
          setEditModalOpen(false);
          setEditWorldbookData(null);
          fetchWorldbooks();
        }}
      />

      <SettingsModal
        open={settingsModalOpen}
        onClose={() => setSettingsModalOpen(false)}
        onSuccess={() => {
          setSettingsModalOpen(false);
        }}
      />
    </Flexbox>
  );
};

export default WorldbookMenuPage;
