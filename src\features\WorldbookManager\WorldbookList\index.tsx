'use client';

import { Button, Input } from '@lobehub/ui';
import { createStyles } from 'antd-style';
import { RefreshCw, Search } from 'lucide-react';
import { memo, useEffect, useMemo, useState } from 'react';
import { debounce } from 'lodash-es';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';
import { usePathname } from 'next/navigation';
import { parseAsString, useQueryState } from 'nuqs';

import { useWorldbookStore } from '@/store/worldbook';
import VirtualizedList from '../components/VirtualizedList';

import EmptyStatus from './EmptyStatus';
import SkeletonList from './SkeletonList';
import WorldbookItem from './WorldbookItem';

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  `,
  header: css`
    padding: 16px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
    flex-shrink: 0;
  `,
  searchContainer: css`
    margin-bottom: 12px;
  `,

  listContainer: css`
    flex: 1;
    overflow: hidden;
    height: 0; /* 确保Virtuoso能正确计算高度 */
  `,
}));

interface WorldbookListProps {
  className?: string;
  onEditWorldbook?: (worldbook: any) => void;
}

const WorldbookList = memo<WorldbookListProps>(({ className, onEditWorldbook }) => {
  const { t } = useTranslation('worldbook');
  const { styles } = useStyles();
  const pathname = usePathname();

  // 使用 URL 参数管理搜索状态
  const [searchTerm, setSearchTerm] = useQueryState(
    'q',
    parseAsString.withDefault('').withOptions({
      history: 'replace'
    })
  );

  // 本地输入状态，避免输入延迟
  const [inputValue, setInputValue] = useState(searchTerm);

  // 防抖搜索，等用户停止输入后再搜索
  const debouncedSearch = useMemo(
    () => debounce((value: string) => {
      setSearchTerm(value);
    }, 500),
    [setSearchTerm]
  );

  // 同步 searchTerm 变化到 inputValue（比如从 URL 初始化）
  useEffect(() => {
    setInputValue(searchTerm);
  }, [searchTerm]);
  
  const {
    worldbooks,
    loading,
    fetchWorldbooks,
    setCurrentWorldbook,
    currentWorldbook,
  } = useWorldbookStore();

  useEffect(() => {
    // 只有在没有数据时才加载
    if (worldbooks.length === 0) {
      fetchWorldbooks();
    }
  }, [fetchWorldbooks, worldbooks.length]);

  const filteredWorldbooks = useMemo(() => {
    if (!searchTerm) return worldbooks;
    return worldbooks.filter(wb => 
      wb.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      wb.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [worldbooks, searchTerm]);



  const handleWorldbookSelect = (id: string) => {
    setCurrentWorldbook(id);
  };

  const handleRefresh = () => {
    fetchWorldbooks();
  };

  return (
    <div className={`${styles.container} ${className}`}>
      <div className={styles.header}>
        <div className={styles.searchContainer}>
          <Input
            placeholder={t('search.placeholder')}
            prefix={<Search size={16} />}
            suffix={
              <Button
                icon={<RefreshCw size={16} />}
                loading={loading}
                onClick={handleRefresh}
                size="small"
                type="text"
                title="刷新世界书列表"
              />
            }
            value={inputValue}
            onChange={(e) => {
              const value = e.target.value;
              setInputValue(value);
              debouncedSearch(value);
            }}
          />
        </div>
      </div>

      <div className={styles.listContainer}>
        {loading ? (
          <SkeletonList />
        ) : (
          <VirtualizedList
            items={filteredWorldbooks}
            keyExtractor={(worldbook) => worldbook.id}
            renderItem={(worldbook) => (
              <WorldbookItem
                worldbook={worldbook}
                isActive={worldbook.id === currentWorldbook?.id}
                onClick={() => handleWorldbookSelect(worldbook.id)}
                onEdit={onEditWorldbook}
              />
            )}
            emptyComponent={<EmptyStatus searchQuery={searchTerm} />}
            defaultItemHeight={80}
            overscan={5}
            style={{ height: '100%' }}
          />
        )}
      </div>
    </div>
  );
});

WorldbookList.displayName = 'WorldbookList';

export default WorldbookList;
