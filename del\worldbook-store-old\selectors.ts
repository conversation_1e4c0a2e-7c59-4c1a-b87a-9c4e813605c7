import type { WorldbookStore } from './store';

// Worldbook selectors
export const worldbookSelectors = {
  // 基础选择器
  worldbooks: (s: WorldbookStore) => s.worldbooks,
  isLoading: (s: WorldbookStore) => s.isLoading,
  activeWorldbookId: (s: WorldbookStore) => s.activeWorldbookId,
  
  // 计算选择器
  activeWorldbook: (s: WorldbookStore) => 
    s.worldbooks.find(wb => wb.id === s.activeWorldbookId),
  
  enabledWorldbooks: (s: WorldbookStore) => 
    s.worldbooks.filter(wb => wb.enabled),
  
  worldbookById: (id: string) => (s: WorldbookStore) =>
    s.worldbooks.find(wb => wb.id === id),
  
  worldbookCount: (s: WorldbookStore) => s.worldbooks.length,
  
  enabledWorldbookCount: (s: WorldbookStore) => 
    s.worldbooks.filter(wb => wb.enabled).length,
};

// Entry selectors
export const entrySelectors = {
  // 基础选择器
  entries: (s: WorldbookStore) => s.entries,
  isEntriesLoading: (s: WorldbookStore) => s.isEntriesLoading,
  activeEntryId: (s: WorldbookStore) => s.activeEntryId,
  
  // 计算选择器
  activeEntry: (s: WorldbookStore) => 
    s.entries.find(entry => entry.id === s.activeEntryId),
  
  enabledEntries: (s: WorldbookStore) => 
    s.entries.filter(entry => entry.enabled),
  
  entryById: (id: string) => (s: WorldbookStore) =>
    s.entries.find(entry => entry.id === id),
  
  entriesByWorldbookId: (worldbookId: string) => (s: WorldbookStore) =>
    s.entries.filter(entry => entry.worldbookId === worldbookId),
  
  entryCount: (s: WorldbookStore) => s.entries.length,
  
  enabledEntryCount: (s: WorldbookStore) => 
    s.entries.filter(entry => entry.enabled).length,
  
  entriesByKeys: (keys: string[]) => (s: WorldbookStore) =>
    s.entries.filter(entry => 
      entry.keys?.some(key => 
        keys.some(searchKey => 
          key.toLowerCase().includes(searchKey.toLowerCase())
        )
      )
    ),
};

// Combined selectors
export const combinedSelectors = {
  // 获取当前世界书的条目
  currentWorldbookEntries: (s: WorldbookStore) => {
    if (!s.activeWorldbookId) return [];
    return s.entries.filter(entry => entry.worldbookId === s.activeWorldbookId);
  },
  
  // 获取当前世界书的启用条目
  currentWorldbookEnabledEntries: (s: WorldbookStore) => {
    if (!s.activeWorldbookId) return [];
    return s.entries.filter(entry => 
      entry.worldbookId === s.activeWorldbookId && entry.enabled
    );
  },
  
  // 获取世界书统计信息
  worldbookStats: (worldbookId: string) => (s: WorldbookStore) => {
    const entries = s.entries.filter(entry => entry.worldbookId === worldbookId);
    return {
      totalEntries: entries.length,
      enabledEntries: entries.filter(entry => entry.enabled).length,
      constantEntries: entries.filter(entry => entry.constant).length,
    };
  },
};
