@startuml SillyTavern世界书扫描状态机
!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontSize 10

title SillyTavern 世界书扫描状态转换图

[*] --> INITIAL : 开始扫描

state INITIAL {
    INITIAL : 初始扫描状态
    INITIAL : - 扫描深度: world_info_depth
    INITIAL : - 处理所有类型的条目
    INITIAL : - 检查常驻、关键词、外部激活
    INITIAL : - 应用所有过滤条件
}

state RECURSION {
    RECURSION : 递归扫描状态  
    RECURSION : - 扫描激活条目的内容
    RECURSION : - 使用递归缓冲区内容
    RECURSION : - 检查递归控制字段
    RECURSION : - 处理延迟递归级别
}

state MIN_ACTIVATIONS {
    MIN_ACTIVATIONS : 最小激活扫描状态
    MIN_ACTIVATIONS : - 增加扫描深度
    MIN_ACTIVATIONS : - 尝试激活更多条目
    MIN_ACTIVATIONS : - 不使用递归缓冲区
    MIN_ACTIVATIONS : - 检查深度限制
}

state NONE {
    NONE : 扫描结束状态
    NONE : - 构建最终提示
    NONE : - 设置时间效果
    NONE : - 清理外部激活
    NONE : - 返回结果
}

' 状态转换条件
INITIAL --> RECURSION : world_info_recursive = true\n&& 有新的递归条目\n&& !token_budget_overflowed

INITIAL --> MIN_ACTIVATIONS : 激活数 < world_info_min_activations\n&& !token_budget_overflowed\n&& 深度未超限

INITIAL --> NONE : 无更多激活条件\n|| token_budget_overflowed\n|| 达到最大递归步数

RECURSION --> RECURSION : 有新的递归条目\n&& !token_budget_overflowed\n&& 未达到最大递归步数

RECURSION --> MIN_ACTIVATIONS : 无新递归条目\n&& 激活数 < world_info_min_activations\n&& !token_budget_overflowed

RECURSION --> NONE : 无新递归条目\n&& (激活数足够 || token_budget_overflowed)\n&& 无延迟递归级别

MIN_ACTIVATIONS --> RECURSION : world_info_recursive = true\n&& buffer.hasRecurse()\n&& !token_budget_overflowed

MIN_ACTIVATIONS --> MIN_ACTIVATIONS : 激活数 < world_info_min_activations\n&& 深度未超限\n&& !token_budget_overflowed

MIN_ACTIVATIONS --> NONE : 激活数足够\n|| 深度超限\n|| token_budget_overflowed

NONE --> [*] : 扫描完成

' 特殊转换：延迟递归级别
RECURSION --> RECURSION : 有延迟递归级别\n&& availableRecursionDelayLevels.length > 0
note on link : 切换到下一个延迟递归级别\ncurrentRecursionDelayLevel = availableRecursionDelayLevels.shift()

' 循环控制
note top of INITIAL
**循环控制变量**
- count: 循环计数器
- world_info_max_recursion_steps: 最大递归步数
- token_budget_overflowed: 预算溢出标志
- allActivatedEntries.size: 已激活条目数
- world_info_min_activations: 最小激活数
end note

note top of RECURSION  
**递归控制逻辑**
- successfulNewEntriesForRecursion: 可递归条目
- excludeRecursion: 排除递归的条目
- preventRecursion: 阻止递归的条目
- delayUntilRecursion: 延迟递归的条目
- currentRecursionDelayLevel: 当前递归延迟级别
- availableRecursionDelayLevels: 可用递归延迟级别
end note

note top of MIN_ACTIVATIONS
**最小激活控制**
- world_info_min_activations: 最小激活数设置
- world_info_min_activations_depth_max: 最大深度限制
- buffer.getDepth(): 当前扫描深度
- buffer.advanceScan(): 增加扫描深度
end note

note top of NONE
**结束条件**
- 所有激活条件都不满足
- Token预算溢出
- 达到最大递归步数
- 达到最大扫描深度
- 无更多可激活条目
end note

' 状态内部处理
state INITIAL {
    [*] --> ProcessEntries
    ProcessEntries --> CheckActivation : 遍历排序条目
    CheckActivation --> ApplyFilters : 条目符合基本条件
    ApplyFilters --> KeywordMatch : 通过所有过滤器
    KeywordMatch --> AddToActivated : 匹配成功
    AddToActivated --> ProcessEntries : 继续下一条目
    CheckActivation --> ProcessEntries : 条目不符合条件
    ApplyFilters --> ProcessEntries : 被过滤器排除
    KeywordMatch --> ProcessEntries : 匹配失败
    ProcessEntries --> PostProcess : 所有条目处理完成
    PostProcess --> [*] : 完成当前轮次
    
    state ApplyFilters {
        ApplyFilters : 应用过滤器
        ApplyFilters : - disable检查
        ApplyFilters : - characterFilter检查  
        ApplyFilters : - 时间效果检查
        ApplyFilters : - 递归控制检查
        ApplyFilters : - 装饰器检查
    }
    
    state KeywordMatch {
        KeywordMatch : 关键词匹配
        KeywordMatch : - 外部激活检查
        KeywordMatch : - constant模式检查
        KeywordMatch : - sticky效果检查
        KeywordMatch : - 主关键词匹配
        KeywordMatch : - 次关键词逻辑检查
    }
    
    state PostProcess {
        PostProcess : 后处理
        PostProcess : - 分组过滤
        PostProcess : - 概率检查
        PostProcess : - 预算检查
        PostProcess : - 递归准备
    }
}

state RECURSION {
    [*] --> AddRecurseContent
    AddRecurseContent --> ProcessEntries : 添加递归内容到缓冲区
    ProcessEntries --> CheckRecursionRules : 遍历条目
    CheckRecursionRules --> ApplyRecursionFilters : 检查递归控制字段
    ApplyRecursionFilters --> KeywordMatch : 通过递归过滤
    KeywordMatch --> AddToActivated : 匹配成功
    AddToActivated --> ProcessEntries : 继续下一条目
    CheckRecursionRules --> ProcessEntries : 递归规则不符合
    ApplyRecursionFilters --> ProcessEntries : 被递归过滤器排除
    KeywordMatch --> ProcessEntries : 匹配失败
    ProcessEntries --> PostProcess : 所有条目处理完成
    PostProcess --> [*] : 完成递归轮次
    
    state CheckRecursionRules {
        CheckRecursionRules : 递归规则检查
        CheckRecursionRules : - excludeRecursion检查
        CheckRecursionRules : - delayUntilRecursion检查
        CheckRecursionRules : - 递归级别检查
    }
}

state MIN_ACTIVATIONS {
    [*] --> AdvanceDepth
    AdvanceDepth --> ProcessEntries : buffer.advanceScan()
    ProcessEntries --> CheckDepthLimit : 遍历条目
    CheckDepthLimit --> ApplyFilters : 深度未超限
    ApplyFilters --> KeywordMatch : 通过过滤器
    KeywordMatch --> AddToActivated : 匹配成功
    AddToActivated --> ProcessEntries : 继续下一条目
    CheckDepthLimit --> PostProcess : 深度超限
    ApplyFilters --> ProcessEntries : 被过滤器排除
    KeywordMatch --> ProcessEntries : 匹配失败
    ProcessEntries --> PostProcess : 所有条目处理完成
    PostProcess --> [*] : 完成最小激活轮次
    
    state CheckDepthLimit {
        CheckDepthLimit : 深度限制检查
        CheckDepthLimit : - world_info_min_activations_depth_max
        CheckDepthLimit : - buffer.getDepth() > chat.length
        CheckDepthLimit : - 当前深度 vs 最大深度
    }
}

@enduml
