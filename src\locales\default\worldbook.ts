export default {
  // 激活规则
  activation: {
    afterSystem: '系统消息后',
    andAll: '主关键词 + 全部次关键词',
    andAny: '主关键词 + 任一次关键词',
    constant: '常驻激活',
    groupName: '分组名称',
    inHistory: '历史消息中',
    notAll: '主关键词 + 非全部次关键词',
    notAny: '主关键词 + 无任何次关键词',
    position: '注入位置',
    scanDepth: '扫描深度',
    selectiveLogic: '选择逻辑',
  },

  // 基础
  create: '创建世界书',
  selectWorldbook: '请选择一个世界书开始管理条目',

  // 创建世界书
  createWorldbook: {
    error: '创建失败，请重试',
    prompt: '请输入世界书名称',
    success: '创建成功',
    title: '创建世界书',
  },

  // 删除世界书
  delete: {
    confirm: '确认删除',
    confirmDescription: '确定要删除世界书"{{name}}"吗？此操作不可撤销。',
    confirmTitle: '删除世界书',
    error: '删除失败',
    success: '删除成功',
  },

  description: '管理角色设定和世界观',

  // 编辑世界书
  edit: {
    error: '更新失败',
    success: '更新成功',
    title: '编辑世界书',
  },

  // 基础字段
  name: '名称',

  // 空状态
  empty: {
    description: '创建您的第一个世界书来管理角色设定和世界观',
    title: '暂无世界书',
  },

  // 条目相关
  entry: {
    activation: {
      title: '激活规则',
    },
    basic: {
      title: '基本信息',
    },
    caseSensitive: '大小写敏感',
    constant: '常量',
    content: {
      placeholder: '请输入条目内容',
      required: '请输入条目内容',
      title: '条目内容',
    },
    create: {
      confirm: '创建',
      title: '创建条目',
    },
    delete: {
      confirmBatch: '确认删除选中的 {{count}} 个条目吗？',
      confirmDescription: '确认删除此条目吗？此操作不可撤销。',
      confirmTitle: '删除条目',
    },
    disable: '禁用',
    disabled: '已禁用',
    duplicate: {
      title: '复制新建',
    },
    edit: {
      title: '编辑条目',
    },
    empty: {
      description: '暂无条目，点击创建按钮添加第一个条目',
      title: '暂无条目',
    },

    enable: '启用',
    enabled: '启用',
    keys: {
      placeholder: '请输入主关键词，用逗号分隔',
      title: '主关键词',
    },
    keysSecondary: {
      placeholder: '请输入次关键词，用逗号分隔',
      title: '次关键词',
    },
    matchWholeWords: '全词匹配',
    matching: {
      title: '匹配选项',
    },
    notFound: '条目未找到',
    onDemand: '按需激活',
    order: '优先级',
    probability: '激活概率',

    search: {
      placeholder: '搜索条目...',
    },
    selected: '已选择 {{count}} 项',
    selectiveLogic: {
      andAll: '主关键词 + 全部次关键词',
      andAny: '主关键词 + 任一次关键词',
      notAll: '主关键词 + 非全部次关键词',
      notAny: '主关键词 + 无任何次关键词',
      title: '选择逻辑',
    },
    sort: {
      asc: '升序',
      date: '按日期',
      desc: '降序',
      name: '按名称',
      order: '按优先级',
      probability: '按概率',
    },
    title: '条目',
    titleField: {
      placeholder: '请输入条目标题',
      required: '请输入条目标题',
      title: '条目标题',
    },
    total: '总共 {{count}} 项',
    untitled: '无标题',
    useRegex: '使用正则表达式',

    // 新增字段
    activationMode: '激活模式',
    position: '注入位置',
    depth: '深度',
    role: '角色类型',
    useProbability: '使用概率控制',
    sticky: '粘性时间',
    cooldown: '冷却时间',
    delay: '延迟时间',
    excludeRecursion: '排除递归',
    preventRecursion: '防止递归',
    delayUntilRecursion: '递归延迟',
    groupWeight: '分组权重',
    groupOverride: '分组覆盖',
    useGroupScoring: '使用分组评分',
    scanDepth: '扫描深度',
    displayIndex: '显示索引',
    addMemo: '添加备注',
    decorators: '装饰器',
    characterFilterNames: '角色名称过滤',
    characterFilterTags: '角色标签过滤',
    characterFilterExclude: '排除模式',
  },



  // 过滤器
  filter: {
    all: '全部',
    apply: '应用',
    group: '分组',
    groupName: '分组名称',
    groupNamePlaceholder: '请输入分组名称',
    hasKeys: '有关键词',
    noKeys: '无关键词',
    ranges: '数值范围',
    reset: '重置',
    status: '状态',
    title: '过滤器',
  },

  // 表单
  form: {
    confirm: '确认',
    descriptionMaxLength: '描述不能超过200个字符',
    descriptionPlaceholder: '请输入世界书描述',
    nameMaxLength: '名称不能超过50个字符',
    namePlaceholder: '请输入世界书名称',
    nameRequired: '请输入世界书名称',
  },

  // 导入功能
  import: {
    completed: '导入完成',
    confirm: '确认导入',
    error: '导入失败',
    errorDetail: '错误详情',
    failed: '导入失败',
    importing: '导入中...',
    invalidFileType: '请选择JSON格式的文件',
    invalidFormat: '文件格式无效，请上传项目标准格式或SillyTavern格式的世界书文件',
    parseError: '文件解析失败，请检查文件格式是否正确',
    unsupportedFormat: '不支持的文件格式。支持的格式：项目标准格式、SillyTavern格式',
    parsing: '解析中...',
    partialSuccess: '部分导入成功：{{success}} 成功，{{failed}} 失败',
    preview: {
      description: '描述',
      keywords: '关键词',
      sampleEntries: '示例条目',
      title: '导入预览',
      totalEntries: '条目总数',
      worldbook: '世界书',
    },
    processing: '正在处理...',
    result: {
      errors: '错误信息',
      failed: '失败 {{count}} 项',
      imported: '成功导入 {{count}} 项',
    },
    selectFile: '选择文件',
    success: '成功导入世界书"{{name}}"，包含 {{count}} 个条目',
    title: '导入世界书',
    uploadHint: '支持项目格式和 SillyTavern 格式的 JSON 文件',
    uploadText: '点击或拖拽文件到此区域上传',
  },



  // 搜索功能
  search: {
    button: '搜索',
    noResults: '未找到匹配的结果',
    noResultsDesc: '尝试使用不同的关键词搜索 "{{query}}"',
    placeholder: '搜索世界书或条目...',
  },

  // 设置
  settings: {
    advanced: {
      budgetCap: {
        label: '预算上限',
        tooltip: '世界书占用的最大token数',
      },
      maxRecursionDepth: {
        label: '最大递归深度',
        tooltip: '递归扫描的最大深度',
      },
      minActivations: {
        label: '最小激活数',
        tooltip: '至少激活多少个条目',
      },
      minActivationsDepthMax: {
        label: '最小激活深度上限',
        tooltip: '最小激活深度的上限',
      },
      recursiveEnabled: {
        label: '启用递归扫描',
        tooltip: '是否启用递归扫描',
      },
      useGroupScoring: {
        label: '使用组评分机制',
        tooltip: '是否使用组评分机制',
      },
      title: '高级设置',
    },
    basic: {
      budgetPercent: {
        label: '预算百分比',
        tooltip: '世界书占用上下文的百分比',
      },
      enabled: {
        label: '启用世界书',
        tooltip: '是否启用世界书功能',
      },
      scanDepth: {
        label: '扫描深度',
        tooltip: '扫描多少条历史消息',
      },
      title: '基本设置',
    },
    buttons: {
      cancel: '取消',
      save: '保存设置',
      saving: '保存中...',
    },
    defaults: {
      caseSensitive: {
        label: '默认大小写敏感',
        tooltip: '新条目的默认大小写敏感设置',
      },
      characterStrategy: {
        label: '角色策略',
        tooltip: '如何处理角色相关的世界书 (0:平均分配, 1:角色优先, 2:全局优先)',
      },
      includeNames: {
        label: '包含发送者名称',
        tooltip: '是否在匹配中包含发送者名称',
      },
      matchWholeWords: {
        label: '默认全词匹配',
        tooltip: '新条目的默认全词匹配设置',
      },
      overflowAlert: {
        label: '溢出警告',
        tooltip: '是否启用溢出警告',
      },
      title: '默认值设置',
    },
    messages: {
      saveError: '保存失败，请重试',
      saveSuccess: '设置保存成功',
    },
    title: '世界书设置',
  },

  tab: {
    files: '知识库',
    worldbooks: '世界书',
  },
  title: '世界书',
  updatedAt: '更新时间',

  // 专属agent相关
  exclusive: {
    label: '专属',
    tooltip: '此世界书已绑定为专属世界书',
  },
};