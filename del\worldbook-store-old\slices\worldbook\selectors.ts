import type { WorldbookStore } from '../../store';

// Worldbook Manager selectors
export const worldbookManagerSelectors = {
  // 基础状态
  worldbooks: (s: WorldbookStore) => s.worldbooks,
  loading: (s: WorldbookStore) => s.loading,
  importing: (s: WorldbookStore) => s.importing,
  exporting: (s: WorldbookStore) => s.exporting,
  
  // 当前选中的世界书
  currentWorldbook: (s: WorldbookStore) => s.currentWorldbook,
  selectedWorldbookId: (s: WorldbookStore) => s.selectedWorldbookId,
  
  // 过滤和搜索状态
  searchQuery: (s: WorldbookStore) => s.searchQuery,
  filterEnabled: (s: WorldbookStore) => s.filterEnabled,
  sortBy: (s: WorldbookStore) => s.sortBy,
  sortOrder: (s: WorldbookStore) => s.sortOrder,
  
  // UI状态
  sidebarOpen: (s: WorldbookStore) => s.sidebarOpen,
  
  // 计算选择器
  filteredWorldbooks: (s: WorldbookStore) => {
    let filtered = s.worldbooks;
    
    // 搜索过滤
    if (s.searchQuery) {
      const query = s.searchQuery.toLowerCase();
      filtered = filtered.filter(wb => 
        wb.name.toLowerCase().includes(query) ||
        wb.description?.toLowerCase().includes(query)
      );
    }
    
    // 启用状态过滤
    if (s.filterEnabled !== null) {
      filtered = filtered.filter(wb => wb.enabled === s.filterEnabled);
    }
    
    // 排序
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (s.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'date':
          comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime();
          break;
        case 'order':
          comparison = (a.order || 0) - (b.order || 0);
          break;
      }
      
      return s.sortOrder === 'desc' ? -comparison : comparison;
    });
    
    return filtered;
  },
  
  enabledWorldbooks: (s: WorldbookStore) => 
    s.worldbooks.filter(wb => wb.enabled),
  
  worldbookById: (id: string) => (s: WorldbookStore) =>
    s.worldbooks.find(wb => wb.id === id),
  
  worldbookCount: (s: WorldbookStore) => s.worldbooks.length,
  
  enabledWorldbookCount: (s: WorldbookStore) => 
    s.worldbooks.filter(wb => wb.enabled).length,
  
  // 状态检查
  isLoading: (s: WorldbookStore) => s.loading || s.importing || s.exporting,
  
  hasWorldbooks: (s: WorldbookStore) => s.worldbooks.length > 0,
  
  hasEnabledWorldbooks: (s: WorldbookStore) => 
    s.worldbooks.some(wb => wb.enabled),
};
