# Lobe-Chat 世界书激活功能实现路线图

## 🎯 总体目标

基于对 SillyTavern 世界书系统的深度分析，在 Lobe-Chat 中实现完整的世界书激活注入功能，包括：
- 三种激活模式（关键词、常驻、向量化）
- 完整的时间效果系统
- 递归扫描机制
- 与现有 RAG 系统的无缝集成

## 📋 实施阶段规划

### 阶段1：核心激活引擎 (5天)

#### 目标
实现基础的世界书激活引擎，支持关键词匹配和常驻激活模式。

#### 主要任务

**Day 1-2: 核心类架构**
```typescript
// 创建文件
src/services/worldbook/activation/
├── engine.ts              // WorldbookActivationEngine 核心引擎
├── buffer.ts              // WorldInfoBuffer 扫描缓冲区
├── matcher.ts             // KeywordMatcher 关键词匹配器
├── types.ts               // 类型定义
└── constants.ts           // 常量定义

// 实现核心功能
- WorldbookActivationEngine.checkWorldInfo() 主激活方法
- WorldInfoBuffer.get() 扫描文本构建
- KeywordMatcher.matchKeys() 关键词匹配算法
- 基础的激活循环逻辑
```

**Day 3: 匹配逻辑实现**
```typescript
// 实现匹配功能
- 主关键词匹配算法
- 次关键词逻辑检查 (AND_ANY, NOT_ALL, NOT_ANY, AND_ALL)
- 匹配选项支持 (caseSensitive, matchWholeWords, useRegex)
- 匹配源控制 (personaDescription, characterDescription 等)
```

**Day 4: 过滤器系统**
```typescript
// 实现过滤器
- 基础状态检查 (enabled/disabled)
- 角色过滤器 (characterFilterNames, characterFilterTags)
- 装饰器支持 (@@activate, @@dont_activate)
- 概率检查 (probability, useProbability)
```

**Day 5: 集成测试**
```typescript
// 集成到消息处理流程
- 扩展 ChatService.processMessagesPreset
- 实现基础的内容注入逻辑
- 单元测试和集成测试
- 错误处理和日志记录
```

#### 验收标准
- ✅ 支持关键词匹配激活
- ✅ 支持常驻激活模式
- ✅ 支持基础过滤器
- ✅ 能够注入到对话中
- ✅ 通过基础测试用例

### 阶段2：时间效果系统 (3天)

#### 目标
实现完整的时间效果管理系统，包括粘性、冷却和延迟效果。

#### 主要任务

**Day 1: 时间效果管理器**
```typescript
// 创建文件
src/services/worldbook/activation/
├── timedEffects.ts        // WorldInfoTimedEffects 时间效果管理器
└── sessionMetadata.ts     // Session 元数据管理

// 实现功能
- WorldInfoTimedEffects 类
- 时间效果状态存储 (session metadata)
- 效果检查逻辑 (isEffectActive)
```

**Day 2: 效果类型实现**
```typescript
// 实现三种时间效果
- Sticky 效果：持续激活指定轮数
- Cooldown 效果：冷却期内不激活
- Delay 效果：延迟指定轮数后激活
- 效果优先级处理
```

**Day 3: 集成和测试**
```typescript
// 集成到激活引擎
- 在激活检查中集成时间效果
- 在激活后设置时间效果
- 时间效果的持久化存储
- 完整测试覆盖
```

#### 验收标准
- ✅ 支持 sticky 粘性效果
- ✅ 支持 cooldown 冷却效果
- ✅ 支持 delay 延迟效果
- ✅ 效果状态正确持久化
- ✅ 通过时间效果测试用例

### 阶段3：向量化搜索集成 (3天)

#### 目标
实现向量化语义搜索激活模式，与现有 RAG 系统深度集成。

#### 主要任务

**Day 1: 向量化激活器**
```typescript
// 创建文件
src/services/worldbook/activation/
├── vectorized.ts          // VectorizedWorldbookActivator
└── integration.ts         // RAG 系统集成

// 实现功能
- VectorizedWorldbookActivator 类
- 语义搜索逻辑
- 外部激活机制
```

**Day 2: RAG 系统集成**
```typescript
// 扩展现有 RAG 功能
- 扩展 ChunkModel.semanticSearchForWorldbook
- 实现世界书专用的语义搜索
- 结果转换和格式化
- 相似度阈值控制
```

**Day 3: 双模式融合**
```typescript
// 实现关键词+语义搜索融合
- 并行执行两种搜索模式
- 结果去重和排序
- 激活原因标记
- 性能优化
```

#### 验收标准
- ✅ 支持向量化语义搜索
- ✅ 与 RAG 系统无缝集成
- ✅ 双模式结果正确融合
- ✅ 性能满足要求
- ✅ 通过语义搜索测试用例

### 阶段4：递归和高级功能 (4天)

#### 目标
实现递归扫描机制、位置插入系统和其他高级功能。

#### 主要任务

**Day 1: 递归扫描机制**
```typescript
// 实现递归功能
- 递归缓冲区管理
- 递归控制逻辑 (excludeRecursion, preventRecursion)
- 延迟递归处理 (delayUntilRecursion)
- 最大递归步数控制
```

**Day 2: 位置插入系统**
```typescript
// 创建文件
src/services/worldbook/activation/
└── positionInserter.ts    // PositionInserter 位置插入器

// 实现功能
- 7种插入位置支持
- 深度插入逻辑 (atDepth)
- 角色类型支持
- 内容排序和组织
```

**Day 3: 分组功能**
```typescript
// 实现分组功能
- 分组过滤器 (filterByInclusionGroups)
- 分组权重处理
- 分组优先级控制
- 分组冲突解决
```

**Day 4: 预算控制和优化**
```typescript
// 实现预算控制
- Token 预算计算
- 预算溢出处理
- 内容截断策略
- 性能监控和优化
```

#### 验收标准
- ✅ 支持递归扫描
- ✅ 支持 7 种插入位置
- ✅ 支持分组功能
- ✅ 支持预算控制
- ✅ 通过高级功能测试用例

### 阶段5：系统集成和优化 (3天)

#### 目标
完成系统集成，性能优化，错误处理和用户体验优化。

#### 主要任务

**Day 1: 消息流程集成**
```typescript
// 完整集成到对话生成流程
- 扩展 generateAIChat 支持世界书
- 实现 worldbookQAPrompts 格式化
- 与 RAG 内容合并策略
- 加载状态管理
```

**Day 2: 性能优化**
```typescript
// 性能优化
- 缓存机制实现
- 批量数据库查询
- 异步处理优化
- 内存使用优化
```

**Day 3: 错误处理和测试**
```typescript
// 完善错误处理
- 异常捕获和降级
- 错误日志记录
- 用户友好的错误提示
- 完整的端到端测试
```

#### 验收标准
- ✅ 完整集成到对话流程
- ✅ 性能满足生产要求
- ✅ 错误处理完善
- ✅ 通过所有测试用例
- ✅ 用户体验良好

## 🔧 技术实现细节

### 核心架构设计

```typescript
// 主要类结构
class WorldbookActivationEngine {
  async checkWorldInfo(messages, sessionId): Promise<ActivationResult>
  private processEntries(entries, buffer, timedEffects): Promise<ActivatedEntry[]>
  private determineNextScanState(activatedEntries, scanState): ScanState
}

class WorldInfoBuffer {
  get(entry, scanState): string
  matchKeys(haystack, needle, entry): boolean
  addRecurse(content): void
  getExternallyActivated(entry): ActivatedEntry | undefined
}

class WorldInfoTimedEffects {
  isEffectActive(effectType, entry): boolean
  setTimedEffects(activatedEntries): void
  checkTimedEffects(): void
}

class VectorizedWorldbookActivator {
  async activateVectorizedEntries(messages, sessionId): Promise<ActivatedEntry[]>
  private executeSemanticSearch(query, chunkIds): Promise<SearchResult[]>
}
```

### 数据流设计

```
用户消息输入
    ↓
消息预处理 (processMessagesPreset)
    ↓
并行执行:
├── RAG 语义搜索 (现有)
└── 世界书激活引擎 (新增)
    ├── 关键词匹配激活
    ├── 常驻激活
    ├── 向量化激活
    ├── 时间效果检查
    ├── 递归扫描
    └── 位置插入
    ↓
结果合并和格式化
    ↓
注入到消息中
    ↓
发送给 LLM
```

### 集成点设计

```typescript
// src/services/chat.ts (扩展)
private async processMessagesPreset(params) {
  let processedMessages = this.handleExistingPresets(messages, model);
  
  // 🚀 新增：世界书激活注入
  if (sessionId && await this.shouldUseWorldbook(sessionId)) {
    const activationEngine = new WorldbookActivationEngine(this.db, this.userId);
    const activationResult = await activationEngine.checkWorldInfo(
      processedMessages, 
      sessionId
    );
    
    if (activationResult.entries.length > 0) {
      const worldbookContext = worldbookQAPrompts(activationResult);
      processedMessages = this.injectWorldbookContext(
        processedMessages, 
        worldbookContext
      );
    }
  }
  
  return processedMessages;
}
```

## 📊 测试策略

### 单元测试
- **关键词匹配器测试**：各种匹配选项和边界情况
- **时间效果测试**：各种时间效果的正确性
- **递归逻辑测试**：递归控制和深度限制
- **位置插入测试**：各种插入位置的正确性

### 集成测试
- **激活引擎测试**：完整的激活流程
- **向量化搜索测试**：语义搜索的准确性
- **消息流程测试**：端到端的对话生成
- **性能测试**：大量条目的处理性能

### 兼容性测试
- **SillyTavern 导入测试**：各种格式的导入
- **功能对等测试**：与 SillyTavern 的行为一致性
- **边界情况测试**：异常输入和错误处理

## 📈 性能目标

### 响应时间
- **小型世界书** (< 100 条目)：< 100ms
- **中型世界书** (100-500 条目)：< 300ms
- **大型世界书** (500+ 条目)：< 500ms

### 内存使用
- **基础内存开销**：< 50MB
- **大型世界书内存**：< 200MB
- **缓存命中率**：> 80%

### 准确性
- **关键词匹配准确率**：100%
- **语义搜索相关性**：> 85%
- **时间效果正确性**：100%

## 🚀 发布计划

### Alpha 版本 (阶段1完成)
- 基础关键词匹配功能
- 简单的内容注入
- 内部测试和反馈

### Beta 版本 (阶段3完成)
- 完整的激活模式支持
- 时间效果系统
- 向量化搜索集成
- 公开测试和用户反馈

### 正式版本 (阶段5完成)
- 所有功能完整实现
- 性能优化完成
- 完整的测试覆盖
- 生产环境部署

## 📝 风险评估和缓解策略

### 技术风险
- **复杂度风险**：分阶段实现，渐进式交付
- **性能风险**：早期性能测试，持续优化
- **兼容性风险**：完整的测试覆盖，参考实现对比

### 时间风险
- **开发延期**：预留缓冲时间，关键路径管理
- **测试不足**：并行开发和测试，自动化测试

### 质量风险
- **功能缺失**：详细的需求分析，用户验收测试
- **性能问题**：性能基准测试，持续监控

## 🎯 成功标准

### 功能完整性
- ✅ 100% 覆盖 SillyTavern 核心功能
- ✅ 支持所有激活模式和高级功能
- ✅ 与现有系统无缝集成

### 性能表现
- ✅ 满足响应时间目标
- ✅ 内存使用在合理范围
- ✅ 支持大规模世界书

### 用户体验
- ✅ 操作简单直观
- ✅ 错误处理友好
- ✅ 功能文档完整

**通过这个详细的实施路线图，我们可以在 18 天内完成 Lobe-Chat 世界书激活功能的完整实现，达到与 SillyTavern 相当的功能水平。**
