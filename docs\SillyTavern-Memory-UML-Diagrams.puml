@startuml SillyTavern-Memory-System-Class-Diagram
!theme plain
title SillyTavern记忆系统核心类图

' 核心接口
interface IMemorySystem {
    +processMemory(context: ChatContext): Promise<void>
    +getMemory(chatId: string): Promise<string>
    +clearMemory(chatId: string): Promise<void>
}

interface ISummaryGenerator {
    +generateSummary(context: ChatContext, force: boolean): Promise<string>
    +validateInput(context: ChatContext): boolean
    +getTokenCount(text: string): Promise<number>
}

interface IEmbeddingProvider {
    +generateEmbedding(text: string): Promise<number[]>
    +getBatchSize(): number
    +isSupported(): boolean
}

' Memory扩展核心类
class MemoryExtension {
    -settings: MemorySettings
    -summaryGenerator: ISummaryGenerator
    -inApiCall: boolean
    -lastMessageHash: string
    -lastMessageId: number
    
    +constructor(settings: MemorySettings)
    +initialize(): void
    +processMemory(context: ChatContext): Promise<void>
    +forceSummarize(quiet: boolean): Promise<string>
    +setMemoryContext(value: string, saveToMessage: boolean, index?: number): void
    +getLatestMemory(chat: ChatMessage[]): string
    +formatMemoryValue(value: string): string
    -checkTriggerConditions(context: ChatContext): boolean
    -saveMemoryToMessage(memory: string, messageIndex: number): void
}

class MemorySettings {
    +memoryFrozen: boolean
    +source: SummarySource
    +prompt: string
    +template: string
    +position: PromptPosition
    +role: PromptRole
    +depth: number
    +promptWords: number
    +promptInterval: number
    +promptForceWords: number
    +overrideResponseLength: number
    +maxMessagesPerRequest: number
    
    +validate(): boolean
    +clone(): MemorySettings
    +merge(other: MemorySettings): MemorySettings
}

' 摘要生成器实现
class ExtrasSummaryGenerator {
    -apiUrl: string
    -timeout: number
    
    +generateSummary(context: ChatContext, force: boolean): Promise<string>
    +validateInput(context: ChatContext): boolean
    +getTokenCount(text: string): Promise<number>
    -callExtrasSummarizeAPI(text: string): Promise<string>
    -collectMemoryBuffer(chat: ChatMessage[]): string[]
    -getMemoryString(longMemory: string, buffer: string[]): string
}

class MainModelSummaryGenerator {
    -skipWIAN: boolean
    -promptBuilder: PromptBuilder
    
    +generateSummary(context: ChatContext, force: boolean): Promise<string>
    +validateInput(context: ChatContext): boolean
    +getTokenCount(text: string): Promise<number>
    -generateQuietPrompt(prompt: string): Promise<string>
    -generateRawPrompt(context: ChatContext, prompt: string): Promise<string>
    -getRawSummaryPrompt(context: ChatContext, prompt: string): Promise<RawPromptResult>
}

class WebLLMSummaryGenerator {
    -modelName: string
    -contextSize: number
    
    +generateSummary(context: ChatContext, force: boolean): Promise<string>
    +validateInput(context: ChatContext): boolean
    +getTokenCount(text: string): Promise<number>
    -generateWebLlmChatPrompt(prompt: string): Promise<ChatPrompt>
    -isWebLlmSupported(): boolean
}

' Vectors扩展核心类
class VectorsExtension {
    -settings: VectorSettings
    -vectorStorage: VectorStorage
    -embeddingProvider: IEmbeddingProvider
    
    +constructor(settings: VectorSettings, provider: IEmbeddingProvider)
    +initialize(): void
    +processVectors(context: ChatContext): Promise<void>
    +searchSimilarVectors(query: string, chatId: string, count: number): Promise<Vector[]>
    +injectVectorMemory(context: ChatContext): Promise<void>
    -vectorizeMessages(messages: ChatMessage[]): Promise<Vector[]>
    -calculateSimilarity(vector1: number[], vector2: number[]): number
    -filterByThreshold(vectors: Vector[], threshold: number): Vector[]
}

class VectorSettings {
    +source: EmbeddingSource
    +enabledChats: boolean
    +enabledFiles: boolean
    +template: string
    +depth: number
    +protect: number
    +insert: number
    +query: number
    +messageChunkSize: number
    +scoreThreshold: number
    +chunkSize: number
    +chunkCount: number
    +overlapPercent: number
    
    +validate(): boolean
    +getEffectiveChunkSize(): number
    +getEffectiveThreshold(): number
}

class VectorStorage {
    -chatVectors: Map<string, Vector[]>
    -fileVectors: Map<string, Vector[]>
    -databankVectors: Map<string, Vector[]>
    
    +storeVectors(chatId: string, vectors: Vector[]): Promise<void>
    +getVectors(chatId: string): Promise<Vector[]>
    +removeVectors(chatId: string): Promise<void>
    +saveToFile(chatId: string, vectors: Vector[]): Promise<void>
    +loadFromFile(chatId: string): Promise<Vector[]>
    +cleanup(maxAge: number): Promise<void>
    -generateStorageKey(chatId: string, type: string): string
}

class Vector {
    +text: string
    +vector: number[]
    +hash: number
    +index: number
    +timestamp: number
    +similarity?: number
    +metadata?: VectorMetadata
    
    +constructor(text: string, vector: number[], index: number)
    +calculateHash(): number
    +isExpired(maxAge: number): boolean
    +clone(): Vector
}

' 嵌入提供者实现
class TransformersEmbeddingProvider {
    -modelName: string
    -batchSize: number
    
    +generateEmbedding(text: string): Promise<number[]>
    +getBatchSize(): number
    +isSupported(): boolean
    -processBatch(texts: string[]): Promise<number[][]>
    -normalizeVector(vector: number[]): number[]
}

class OpenAIEmbeddingProvider {
    -apiKey: string
    -modelName: string
    -maxTokens: number
    
    +generateEmbedding(text: string): Promise<number[]>
    +getBatchSize(): number
    +isSupported(): boolean
    -callOpenAIAPI(text: string): Promise<number[]>
    -handleRateLimit(error: Error): Promise<void>
}

' 数据模型
class ChatContext {
    +chat: ChatMessage[]
    +currentUser: string
    +currentCharacter: string
    +sessionId: string
    +metadata: ContextMetadata
    
    +getLastMessage(): ChatMessage
    +getMessagesSince(timestamp: number): ChatMessage[]
    +getTokenCount(): Promise<number>
    +isValid(): boolean
}

class ChatMessage {
    +mes: string
    +name: string
    +isSystem: boolean
    +timestamp: number
    +index: number
    +extra: MessageExtra
    
    +constructor(content: string, sender: string, isSystem: boolean)
    +hasMemory(): boolean
    +getMemory(): string
    +setMemory(memory: string): void
    +clone(): ChatMessage
}

class MessageExtra {
    +memory?: string
    +vectors?: Vector[]
    +timestamp: number
    +metadata?: ExtraMetadata
    
    +hasMemory(): boolean
    +hasVectors(): boolean
    +addVector(vector: Vector): void
    +removeVector(hash: number): void
}

' 工厂类
class SummaryGeneratorFactory {
    +createGenerator(source: SummarySource, settings: MemorySettings): ISummaryGenerator
    -validateSource(source: SummarySource): boolean
}

class EmbeddingProviderFactory {
    +createProvider(source: EmbeddingSource, settings: VectorSettings): IEmbeddingProvider
    -validateSource(source: EmbeddingSource): boolean
}

' 枚举类型
enum SummarySource {
    EXTRAS
    MAIN
    WEBLLM
}

enum EmbeddingSource {
    TRANSFORMERS
    OPENAI
    COHERE
    OLLAMA
    GOOGLE
}

enum PromptPosition {
    IN_PROMPT
    AFTER_PROMPT
    BEFORE_PROMPT
}

enum PromptRole {
    SYSTEM
    USER
    ASSISTANT
}

' 关系定义
IMemorySystem <|.. MemoryExtension
IMemorySystem <|.. VectorsExtension

ISummaryGenerator <|.. ExtrasSummaryGenerator
ISummaryGenerator <|.. MainModelSummaryGenerator
ISummaryGenerator <|.. WebLLMSummaryGenerator

IEmbeddingProvider <|.. TransformersEmbeddingProvider
IEmbeddingProvider <|.. OpenAIEmbeddingProvider

MemoryExtension --> MemorySettings
MemoryExtension --> ISummaryGenerator
MemoryExtension --> ChatContext

VectorsExtension --> VectorSettings
VectorsExtension --> VectorStorage
VectorsExtension --> IEmbeddingProvider
VectorsExtension --> Vector

VectorStorage --> Vector
ChatContext --> ChatMessage
ChatMessage --> MessageExtra
MessageExtra --> Vector

SummaryGeneratorFactory --> ISummaryGenerator
EmbeddingProviderFactory --> IEmbeddingProvider

MemoryExtension ..> SummaryGeneratorFactory : 使用
VectorsExtension ..> EmbeddingProviderFactory : 使用

@enduml

@startuml Memory-Generation-Sequence
!theme plain
title 记忆生成序列图

participant User as U
participant SillyTavern as ST
participant MemoryExtension as ME
participant SummaryGenerator as SG
participant VectorStorage as VS
participant "AI Model" as AI

U -> ST: 发送消息
ST -> ME: onChatEvent(context)

ME -> ME: checkTriggerConditions()
alt 需要生成记忆
    ME -> SG: generateSummary(context, false)
    SG -> AI: 调用AI生成摘要
    AI --> SG: 返回摘要文本
    SG --> ME: 返回格式化摘要

    ME -> ME: formatMemoryValue(summary)
    ME -> VS: saveMemoryToMessage(summary, index)
    ME -> ST: setExtensionPrompt(formattedMemory)
else 跳过记忆生成
    ME --> ST: 无操作
end

ST -> AI: 发送包含记忆的提示词
AI --> ST: 生成回复
ST --> U: 返回AI回复

@enduml

@startuml Vector-Retrieval-Sequence
!theme plain
title 向量检索序列图

participant User as U
participant SillyTavern as ST
participant VectorsExtension as VE
participant EmbeddingProvider as EP
participant VectorStorage as VS
participant "AI Model" as AI

U -> ST: 发送查询消息
ST -> VE: processVectors(context)

VE -> EP: generateEmbedding(queryText)
EP --> VE: 返回查询向量

VE -> VS: getVectors(chatId)
VS --> VE: 返回历史向量

VE -> VE: calculateSimilarity(queryVector, historyVectors)
VE -> VE: filterByThreshold(similarities)
VE -> VE: sortAndLimit(filteredVectors)

VE -> ST: injectVectorMemory(similarVectors)
ST -> AI: 发送包含向量记忆的提示词
AI --> ST: 生成回复
ST --> U: 返回AI回复

note over ST,VE: 异步更新向量存储
ST -> VE: updateVectorStorage(newMessage)
VE -> EP: generateEmbedding(newMessage)
EP --> VE: 返回新向量
VE -> VS: storeVectors(chatId, newVectors)

@enduml

@startuml Memory-System-State-Diagram
!theme plain
title 记忆系统状态图

[*] --> 未初始化
未初始化 --> 初始化中 : initialize()
初始化中 --> 就绪 : 初始化完成
初始化中 --> 错误 : 初始化失败

就绪 --> 检查触发条件 : onChatEvent()
检查触发条件 --> 就绪 : 条件不满足
检查触发条件 --> 生成记忆 : 条件满足

生成记忆 --> 调用API : 选择摘要源
调用API --> 处理响应 : API调用成功
调用API --> 错误处理 : API调用失败

处理响应 --> 保存记忆 : 摘要有效
处理响应 --> 错误处理 : 摘要无效

保存记忆 --> 更新提示词 : 保存成功
保存记忆 --> 错误处理 : 保存失败

更新提示词 --> 就绪 : 更新完成

错误处理 --> 就绪 : 错误恢复
错误处理 --> 错误 : 无法恢复

就绪 --> 冻结 : freezeMemory()
冻结 --> 就绪 : unfreezeMemory()

错误 --> 初始化中 : reset()

@enduml

@startuml System-Component-Diagram
!theme plain
title 系统组件图

package "SillyTavern Core" {
    [SillyTavern主程序] as ST
    [事件控制器] as EC
    [提示词管理器] as PM
}

package "Memory Extension" {
    [MemoryExtension] as ME
    [MemorySettings] as MS
    [SummaryGenerator] as SG
    [SummaryGeneratorFactory] as SGF
}

package "Vectors Extension" {
    [VectorsExtension] as VE
    [VectorSettings] as VS
    [VectorStorage] as VST
    [EmbeddingProvider] as EP
    [EmbeddingProviderFactory] as EPF
}

package "External Services" {
    [Extras API] as EA
    [OpenAI API] as OA
    [Transformers] as TF
    [WebLLM] as WL
}

package "Storage Layer" {
    [文件系统] as FS
    [数据库] as DB
    [内存缓存] as MEM
}

ST --> EC
ST --> PM
EC --> ME
EC --> VE

ME --> MS
ME --> SG
ME --> SGF

VE --> VS
VE --> VST
VE --> EP
VE --> EPF

SG --> EA
SG --> OA
SG --> WL

EP --> OA
EP --> TF

VST --> FS
VST --> DB
VST --> MEM

ME --> PM
VE --> PM

@enduml

@startuml System-Deployment-Diagram
!theme plain
title 系统部署架构图

node "客户端环境" {
    node "浏览器" {
        [用户界面] as UI
        [JavaScript引擎] as JS
    }
}

node "SillyTavern服务器" {
    node "Node.js运行时" {
        [SillyTavern核心] as ST
        [Memory扩展] as ME
        [Vectors扩展] as VE
    }

    node "本地存储" {
        [文件系统] as FS
        [配置文件] as CFG
        [日志文件] as LOG
    }
}

cloud "外部服务" {
    node "AI服务" {
        [OpenAI API] as OAI
        [Anthropic API] as ANT
        [本地模型] as LOC
    }

    node "向量服务" {
        [嵌入服务] as EMB
        [向量数据库] as VDB
    }

    node "摘要服务" {
        [Extras API] as EXT
        [WebLLM] as WLM
    }
}

UI ..> ST : HTTP/WebSocket
JS ..> ST : HTTP/WebSocket

ST --> ME
ST --> VE

ME --> FS
VE --> FS
ST --> CFG
ST --> LOG

ME ..> EXT : HTTP
ME ..> OAI : HTTP
ME ..> WLM : Local

VE ..> EMB : HTTP
VE ..> VDB : HTTP
VE ..> OAI : HTTP

ST ..> OAI : HTTP
ST ..> ANT : HTTP
ST ..> LOC : Local

@enduml

@startuml Strategy-Pattern-Example
!theme plain
title 策略模式应用示例

class SummaryContext {
    -strategy: ISummaryGenerator
    +setStrategy(strategy: ISummaryGenerator): void
    +generateSummary(context: ChatContext): Promise<string>
}

interface ISummaryGenerator {
    +generateSummary(context: ChatContext, force: boolean): Promise<string>
}

class ExtrasSummaryGenerator {
    +generateSummary(context: ChatContext, force: boolean): Promise<string>
}

class MainModelSummaryGenerator {
    +generateSummary(context: ChatContext, force: boolean): Promise<string>
}

class WebLLMSummaryGenerator {
    +generateSummary(context: ChatContext, force: boolean): Promise<string>
}

SummaryContext --> ISummaryGenerator
ISummaryGenerator <|.. ExtrasSummaryGenerator
ISummaryGenerator <|.. MainModelSummaryGenerator
ISummaryGenerator <|.. WebLLMSummaryGenerator

@enduml
