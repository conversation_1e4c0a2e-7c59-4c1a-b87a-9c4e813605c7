<div align="center">
<h1>✨ SillyTavern 酒馆记忆增强插件 ✨</h1>

<p>
    <b>🧠 记忆增强插件</b> - 专为 <a href="https://github.com/SillyTavern/SillyTavern">SillyTavern酒馆</a> 设计，旨在显著提升角色扮演中 AI 的长期记忆能力！

<p>
    <a href="https://muyoo.com.cn/wiki/memory/">⚙️ 快速安装/更新</a>
    |
    <a href="https://muyoo.com.cn/wiki/memory/start.html">🔄 使用教程</a>
    |
    <a href="https://muyoo.com.cn/wiki/memory/installFaq.html">🔍 问题自查</a>
</p>

<p>
    <a href="https://github.com/muyoou/st-memory-enhancement/stargazers">
        <img src="https://img.shields.io/github/stars/muyoou/st-memory-enhancement?style=flat-square" alt="GitHub Stars">
    </a>
    <a href="https://github.com/muyoou/st-memory-enhancement/graphs/contributors">
        <img src="https://img.shields.io/github/contributors/muyoou/st-memory-enhancement?style=flat-square" alt="Contributors">
    </a>
    <a href="https://github.com/muyoou/st-memory-enhancement/issues">
        <img src="https://img.shields.io/github/issues/muyoou/st-memory-enhancement?style=flat-square" alt="GitHub Issues">
    </a>
    <a href="https://qm.qq.com/q/bBSIrwKty2">
      <img src="https://img.shields.io/badge/Join-QQ_Group-ff69b4">
    </a>
    <a href="https://github.com/SillyTavern/SillyTavern">
      <img src="https://img.shields.io/badge/SillyTavern-%3E=1.10.0-blue">
    </a>
</p>

<p>
    <b>🚀 最新版本: v2.0.3 🚀</b>
</p>
</div>

<hr>

## 🌟 插件简介

记忆增强插件为您的角色扮演体验注入强大的 **结构化长期记忆**，支持角色设定、关键事件、重要物品等自定义内容。它能有效帮助 AI 更好地理解和记住对话上下文，从而做出 **更连贯、更贴近情境** 的推演。

**插件优势：**

* 😊 **用户友好：**  通过直观的表格轻松查看和编辑记忆，掌控角色记忆。
* 🛠️ **创作者友好：**  便捷导出和分享配置，JSON 文件灵活定制表格结构，满足各种创作需求。

## ✨ 核心功能亮点

* 📅 **结构化记忆储存：** 基于表格的强大记忆系统，未来更将支持节点编辑器，自由定义表格类型和结构。
* ✏️ **灵活内容编辑：** 表格内容完全可编辑，随时浏览和修改，打造专属角色记忆库。
* 🗃️ **自由模板管理：** 可以管理保存多个作用域的模板，以灵活调整表格结构。
* 🤖 **智能提示词生成与注入：** 自动生成精准提示词，深度注入或全局宏，无缝集成世界书或预设，提升 AI 表现。
* 🖼️ **自定义数据推送展示：**  表格内容推送至聊天界面 DOM，自定义样式，重要信息醒目可见。
* 📦 **便捷配置导出与分享：**  提供丰富的自定义选项（提示词、注入方式等），预设轻松导出和分享表格结构和设置。
* 🚀 **分步操作：**  未来结合主副 API，实现任务智能分配（生成、整理、重建等），支持自定义触发时机，高效管理记忆。

<p align="center">
    <img src="https://github.com/user-attachments/assets/36997237-2c72-46b5-a8df-f5af3fa42171" alt="插件界面示例" style="max-width:80%; border-radius: 5px;">
</p>

## 🚀 快速上手指南

> \[!WARNING]
> **重要提示：** 本插件仅在 SillyTavern 的 **聊天补全模式** 下工作。

1. **安装插件：** 在 SillyTavern 页面，点击 `扩展` > `安装拓展`。

   <p align="center">
       <img src="https://github.com/user-attachments/assets/67904e14-dc8d-4d7c-a1a8-d24253b72621" alt="安装插件步骤 1" style="max-width:70%; border-radius: 5px;">
   </p>

2. **输入插件地址：** 在弹出的窗口中，输入插件的 GitHub 地址 `https://github.com/muyoou/st-memory-enhancement` ，然后选择 `Install for all users`

   <p align="center">
       <img src="https://github.com/user-attachments/assets/9f39015f-63bb-4741-bb7f-740c02f1de17" alt="安装插件步骤 2" style="max-width:70%; border-radius: 5px;">
   </p>

   **国内用户加速：**  如遇网络问题，可尝试国内 Gitee 源地址：`https://gitee.com/muyoou/st-memory-enhancement`

## 💖 支持与交流

**🤝 参与贡献：**  欢迎参与插件开发！请查阅 <a href="https://github.com/muyoou/st-memory-enhancement/blob/dev/README.md">贡献指南</a>，了解如何贡献代码和想法。

**💬 社群交流：**  加入插件交流 & BUG 反馈 QQ 群：<a href="#">**1030109849**</a>，与更多用户交流心得，解决问题。

## 🥰 贡献者们

感谢所有为本项目做出贡献的朋友们！

<p align="center">
    <a href="https://github.com/muyoou/st-memory-enhancement/graphs/contributors">
      <img src="https://contrib.rocks/image?repo=muyoou/st-memory-enhancement" style="max-width: 400px;" />
    </a>
</p>

**Dev 分支贡献统计：**
<p align="center">
    <img src="https://repobeats.axiom.co/api/embed/eb3c2af1bcdb84704bb9ff8f61379fe38d634884.svg" alt="Dev 分支代码分析" style="max-width: 80%; border-radius: 5px;">
</p>

## 🤗 感谢所有人的使用与支持
