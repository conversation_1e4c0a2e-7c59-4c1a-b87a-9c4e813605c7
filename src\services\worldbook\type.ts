import type {
  ActivationStatus,
  BulkOperationResult,
  CreateEntryData,
  CreateWorldbookData,
  ImportResultDetail,
  PaginatedResponse,
  SearchParams,
  UpdateEntryData,
  UpdateWorldbookData,
  Worldbook,
  WorldbookEntry,
} from '@/types/worldbook';

/* eslint-disable typescript-sort-keys/interface */
export interface IWorldbookService {
  // ====== Worldbook Operations ======
  getWorldbooks(): Promise<Worldbook[]>;
  getWorldbook(id: string): Promise<Worldbook>;
  createWorldbook(data: CreateWorldbookData): Promise<Worldbook>;
  updateWorldbook(id: string, data: UpdateWorldbookData): Promise<Worldbook>;
  deleteWorldbook(id: string): Promise<void>;
  deleteAllWorldbooks(): Promise<void>;
  bulkDeleteWorldbooks(ids: string[]): Promise<BulkOperationResult>;
  getWorldbookStats(): Promise<{
    totalWorldbooks: number;
    enabledWorldbooks: number;
    totalEntries: number;
    worldbooksWithAgent: number;
  }>;
  importWorldbook(data: {
    worldbook: CreateWorldbookData;
    entries: any[];
  }): Promise<ImportResultDetail>;

  // ====== Entry Operations ======
  getEntry(id: string): Promise<WorldbookEntry>;
  getEntriesByWorldbookId(worldbookId: string): Promise<WorldbookEntry[]>;
  searchEntries(worldbookId: string, params?: SearchParams): Promise<PaginatedResponse<WorldbookEntry>>;
  createEntry(worldbookId: string, data: CreateEntryData): Promise<WorldbookEntry>;
  updateEntry(id: string, data: UpdateEntryData & { content?: string }): Promise<WorldbookEntry>;
  deleteEntry(id: string): Promise<void>;
  bulkCreateEntries(worldbookId: string, entries: CreateEntryData[]): Promise<WorldbookEntry[]>;
  bulkUpdateEntries(ids: string[], data: Partial<UpdateEntryData>): Promise<BulkOperationResult>;
  bulkDeleteEntries(ids: string[]): Promise<BulkOperationResult>;
  bulkToggleEntries(ids: string[], enabled: boolean): Promise<BulkOperationResult>;
  getActivationStatus(worldbookId: string): Promise<ActivationStatus>;
}


