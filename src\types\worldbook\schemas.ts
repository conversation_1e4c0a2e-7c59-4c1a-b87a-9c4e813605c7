import { z } from 'zod';

import { ActivationMode, SelectiveLogic, WorldbookPosition } from './enums';

/**
 * 共享的Zod schemas，避免重复定义
 */

// MatchSources的Zod schema
export const matchSourcesSchema = z.object({
  personaDescription: z.boolean().default(false),
  characterDescription: z.boolean().default(false),
  characterPersonality: z.boolean().default(false),
  characterDepthPrompt: z.boolean().default(false),
  scenario: z.boolean().default(false),
  creatorNotes: z.boolean().default(false),
});

// 创建世界书条目的基础schema
export const createEntryBaseSchema = z.object({
  title: z.string().min(1, '条目标题不能为空'),
  content: z.string().min(1, '条目内容不能为空'),
  keys: z.array(z.string()).min(1, '至少需要一个关键词'),
  keysSecondary: z.array(z.string()).default([]),
  selectiveLogic: z.nativeEnum(SelectiveLogic).default(SelectiveLogic.AND_ANY),
  activationMode: z.nativeEnum(ActivationMode).default(ActivationMode.Keyword),
  position: z.nativeEnum(WorldbookPosition).default(WorldbookPosition.After),
  depth: z.number().min(0).max(20).optional(),
  role: z.enum(['system', 'user', 'assistant', 'tool']).optional(),
  order: z.number().min(0).max(1000).default(100),
  matchSources: matchSourcesSchema.default({}),
  caseSensitive: z.boolean().default(false),
  matchWholeWords: z.boolean().default(false),
  useRegex: z.boolean().default(false),
  probability: z.number().min(0).max(100).default(100),
  useProbability: z.boolean().default(true),
  sticky: z.number().min(0).optional(),
  cooldown: z.number().min(0).optional(),
  delay: z.number().min(0).optional(),
  excludeRecursion: z.boolean().default(false),
  preventRecursion: z.boolean().default(false),
  delayUntilRecursion: z.number().min(0).default(0),
  groupName: z.string().optional(),
  groupWeight: z.number().min(0).max(1000).default(100),
  groupOverride: z.boolean().default(false),
  useGroupScoring: z.boolean().default(false),
  scanDepth: z.number().min(0).optional(),
  displayIndex: z.number().min(0).default(0),
  addMemo: z.boolean().default(false),
  decorators: z.array(z.string()).default([]),
  characterFilterNames: z.array(z.string()).default([]),
  characterFilterTags: z.array(z.string()).default([]),
  characterFilterExclude: z.boolean().default(false),
});

// 带worldbookId的创建条目schema
export const createEntryWithWorldbookSchema = createEntryBaseSchema.extend({
  worldbookId: z.string().min(1, '世界书ID不能为空'),
});

// 更新世界书条目的schema（所有字段都是可选的）
export const updateEntryBaseSchema = createEntryBaseSchema.partial();

// 搜索参数的schema
export const searchParamsBaseSchema = z.object({
  query: z.string().optional(),
  enabled: z.boolean().optional(),
  activationMode: z.nativeEnum(ActivationMode).optional(),
  position: z.nativeEnum(WorldbookPosition).optional(),
  groupName: z.string().optional(),
  hasKeys: z.boolean().optional(),
  hasSecondaryKeys: z.boolean().optional(),
  selectiveLogic: z.nativeEnum(SelectiveLogic).optional(),
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(20),
  sortBy: z.enum(['title', 'order', 'createdAt', 'updatedAt']).default('order'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});
