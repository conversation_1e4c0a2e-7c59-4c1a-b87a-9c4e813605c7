# 世界书功能分段执行计划

## 🎯 总体目标

基于《世界书技术方案-完整版.md》，分9个阶段完成世界书功能的全面实现，确保每个阶段的代码质量、项目风格一致性和功能完整性。

## 📋 执行原则

1. **渐进式开发**: 每个阶段都有明确的输入、输出和验证标准
2. **质量优先**: 每个阶段完成后必须通过代码质量检查
3. **风格一致**: 严格遵循lobe-chat项目的代码风格和架构模式
4. **功能验证**: 每个阶段都要有可执行的验证方法

## 🔄 依赖关系图

```
类型系统 → 数据库Schema → 数据库Model → API层 → Store层 → 服务层 → UI组件
    ↓
导入处理器 (并行开发)
    ↓
激活引擎 (依赖API+Store)
```

---

## 🎯 阶段1: 类型系统设计 (1天)

### 📁 创建文件
- `src/types/worldbook/enums.ts`
- `src/types/worldbook/index.ts`

### ✅ 任务清单
- [ ] 创建5个字符串枚举类型
  - SelectiveLogic (4个值)
  - ActivationMode (3个值)
  - WorldbookPosition (7个值)
  - ScanState (4个值)
  - ExtensionPromptType (4个值)
- [ ] 定义MatchSources接口 (6个boolean字段)
- [ ] 定义Worldbook接口 (10个字段)
- [ ] 定义WorldbookEntry接口 (39个字段)
- [ ] 定义Create/Update数据接口 (4个接口)
- [ ] 定义搜索和分页接口 (3个接口)

### 🔍 质量检查清单
- [ ] **类型安全**: 所有字段都有明确的类型定义
- [ ] **命名规范**: 使用PascalCase命名接口，camelCase命名字段
- [ ] **注释完整**: 每个接口和重要字段都有JSDoc注释
- [ ] **枚举一致**: 枚举值与数据库枚举完全一致
- [ ] **导入导出**: 正确的import/export语句
- [ ] **无TypeScript错误**: 编译通过，无any类型

### 🧪 验证方法 - 逐行代码检查
#### 检查 `src/types/worldbook/enums.ts`
- [ ] 每个枚举都使用字符串值 (如 `Keyword = 'keyword'`)
- [ ] 枚举值与数据库枚举完全一致
- [ ] 每个枚举都有完整的JSDoc注释
- [ ] 导出语句正确 (`export enum`)

#### 检查 `src/types/worldbook/index.ts`
- [ ] 所有接口字段都有明确类型 (无any类型)
- [ ] 字段命名使用camelCase
- [ ] 可选字段正确使用 `?:` 语法
- [ ] 数组类型使用 `string[]` 而非 `Array<string>`
- [ ] 导入语句路径正确
- [ ] 接口继承关系正确

### 📤 阶段完成总结
#### 改造前状态
- ❌ 无世界书相关类型定义
- ❌ 缺少枚举类型支持
- ❌ 接口定义不完整

#### 改造后功能点
- ✅ 完整的5个枚举类型定义
- ✅ 39个字段的WorldbookEntry接口
- ✅ 完整的CRUD操作接口
- ✅ 搜索和分页接口支持
- ✅ 类型安全的导入导出接口

#### 风险降低措施
- 严格的TypeScript类型检查
- 与数据库枚举值一致性保证
- 完整的接口字段覆盖

---

## 🗄️ 阶段2: 数据库Schema (1天)

### 📁 创建文件
- `src/database/schemas/worldbook.ts`

### ✅ 任务清单
- [ ] 创建4个pgEnum定义 (与TypeScript枚举一致)
- [ ] 创建worldbooks表结构 (8个字段)
- [ ] 创建worldbook_chunks表结构 (39个字段)
- [ ] 添加基础索引 (5个单列索引)
- [ ] 添加复合索引 (3个多列索引)
- [ ] 添加唯一约束和外键约束
- [ ] 生成Zod schema和类型推导

### 🔍 质量检查清单
- [ ] **字段类型**: 数据库字段类型与TypeScript类型匹配
- [ ] **命名规范**: 使用snake_case命名数据库字段
- [ ] **约束完整**: 外键、唯一约束、非空约束正确
- [ ] **索引优化**: 查询相关字段都有适当索引
- [ ] **默认值**: 所有字段都有合理的默认值
- [ ] **注释清晰**: 表和字段都有描述性注释

### 🧪 验证方法 - 逐行代码检查
#### 检查 `src/database/schemas/worldbook.ts`
- [ ] 导入语句完整 (drizzle-orm, zod, helpers)
- [ ] 4个pgEnum定义值与TypeScript枚举一致
- [ ] worldbooks表字段类型正确 (text, boolean, timestamp)
- [ ] worldbook_chunks表39个字段全部定义
- [ ] 外键引用正确 (`references(() => table.field)`)
- [ ] 默认值设置合理
- [ ] 索引定义完整 (单列索引 + 复合索引)
- [ ] createInsertSchema调用正确
- [ ] 类型推导导出正确

### 📤 阶段完成总结
#### 改造前状态
- ❌ 无世界书数据库表结构
- ❌ 缺少枚举类型映射
- ❌ 无索引优化

#### 改造后功能点
- ✅ worldbooks表 (8个字段)
- ✅ worldbook_chunks表 (39个字段)
- ✅ 4个pgEnum定义
- ✅ 8个索引优化 (基础+复合)
- ✅ 完整的外键约束
- ✅ Zod验证Schema自动生成

#### 风险降低措施
- 字段类型与TypeScript接口严格对应
- 外键约束保证数据完整性
- 索引优化保证查询性能

---

## 🏗️ 阶段3: 数据库Model (1天)

### 📁 创建文件
- `src/database/models/worldbook.ts`
- `src/database/models/worldbookEntry.ts`

### ✅ 任务清单
- [ ] 实现WorldbookModel类
  - 基础CRUD操作 (create, findById, update, delete)
  - 查询方法 (query, findByUserId)
  - 统计方法 (getEntryCount)
- [ ] 实现WorldbookEntryModel类
  - 基础CRUD操作
  - 批量操作 (batchCreate, batchUpdate, batchDelete)
  - 搜索方法 (search, findByWorldbookId)
  - 分页方法 (paginate)

### 🔍 质量检查清单
- [ ] **错误处理**: 所有数据库操作都有try-catch
- [ ] **类型安全**: 使用严格的TypeScript类型
- [ ] **方法命名**: 遵循lobe-chat的命名约定
- [ ] **参数验证**: 输入参数有适当的验证
- [ ] **性能优化**: 使用批量操作和索引查询
- [ ] **事务处理**: 批量操作使用数据库事务
- [ ] **日志记录**: 重要操作有日志输出

### 🧪 验证方法 - 逐行代码检查
#### 检查 `src/database/models/worldbook.ts`
- [ ] 类定义使用正确的构造函数 (`constructor(db, userId)`)
- [ ] 所有CRUD方法都有正确的返回类型
- [ ] 错误处理使用try-catch包装
- [ ] 数据库查询使用正确的drizzle语法
- [ ] 用户权限检查 (userId过滤)
- [ ] 方法命名遵循lobe-chat约定
- [ ] 参数验证完整

#### 检查 `src/database/models/worldbookEntry.ts`
- [ ] 批量操作使用数据库事务
- [ ] 搜索方法支持分页参数
- [ ] 查询条件构建正确
- [ ] 关联查询 (join chunks表) 正确
- [ ] 排序和筛选逻辑正确
- [ ] 性能优化 (使用索引字段查询)

### 📤 阶段完成总结
#### 改造前状态
- ❌ 无数据库操作抽象层
- ❌ 缺少批量操作支持
- ❌ 无搜索和分页功能

#### 改造后功能点
- ✅ WorldbookModel类 (6个核心方法)
- ✅ WorldbookEntryModel类 (10个核心方法)
- ✅ 批量操作支持 (事务保证)
- ✅ 搜索和分页功能
- ✅ 用户权限隔离
- ✅ 完整的错误处理

#### 风险降低措施
- 数据库事务保证数据一致性
- 用户权限检查防止数据泄露
- 完整的错误处理和日志记录

---

## 🔌 阶段4: tRPC API层 (2天)

### 📁 创建文件
- `src/server/routers/lambda/worldbook.ts`
- `src/server/routers/lambda/worldbookEntry.ts`

### ✅ 任务清单

#### Day 1: Worldbook路由
- [ ] 实现worldbook路由 (6个API)
  - getWorldbooks (查询所有)
  - getWorldbookById (根据ID查询)
  - createWorldbook (创建)
  - updateWorldbook (更新)
  - deleteWorldbook (删除)
  - importWorldbook (导入)
- [ ] 添加Zod验证Schema
- [ ] 实现错误处理和中文错误信息

#### Day 2: WorldbookEntry路由
- [ ] 实现worldbookEntry路由 (8个API)
  - getEntriesByWorldbookId (分页查询)
  - getEntryById (根据ID查询)
  - createEntry (创建条目)
  - updateEntry (更新条目)
  - deleteEntry (删除条目)
  - batchUpdateEntries (批量更新)
  - batchDeleteEntries (批量删除)
  - searchEntries (搜索条目)

### 🔍 质量检查清单
- [ ] **API设计**: 遵循RESTful设计原则
- [ ] **参数验证**: 使用Zod进行严格的参数验证
- [ ] **错误处理**: 统一的错误处理和友好的错误信息
- [ ] **类型安全**: 输入输出类型完全匹配
- [ ] **性能优化**: 分页查询、批量操作
- [ ] **安全性**: 用户权限验证、数据隔离
- [ ] **文档注释**: 每个API都有详细的JSDoc注释

### 🧪 验证方法 - 逐行代码检查
#### 检查 `src/server/routers/lambda/worldbook.ts`
- [ ] 导入语句完整 (z, TRPCError, Model, schema)
- [ ] worldbookProcedure中间件正确设置
- [ ] 每个API方法都有input验证 (Zod schema)
- [ ] 错误处理使用TRPCError包装
- [ ] 返回类型与接口定义一致
- [ ] 用户权限验证正确
- [ ] 中文错误信息完整

#### 检查 `src/server/routers/lambda/worldbookEntry.ts`
- [ ] 分页查询参数验证正确
- [ ] 批量操作使用数组输入
- [ ] 搜索参数支持可选字段
- [ ] 关联查询返回完整数据
- [ ] 性能优化 (避免N+1查询)
- [ ] 数据转换逻辑正确

### 📤 阶段完成总结
#### 改造前状态
- ❌ 无世界书API接口
- ❌ 缺少参数验证
- ❌ 无批量操作支持

#### 改造后功能点
- ✅ worldbook路由 (6个API)
- ✅ worldbookEntry路由 (8个API)
- ✅ 完整的Zod参数验证
- ✅ 统一的错误处理
- ✅ 批量操作API
- ✅ 搜索和分页API
- ✅ 中文错误信息

#### 风险降低措施
- Zod验证防止无效输入
- TRPCError统一错误格式
- 用户权限验证保证安全性

---

## 🏪 阶段5: Zustand Store层 (2天)

### 📁 创建文件
- `src/store/worldbook/initialState.ts`
- `src/store/worldbook/slices/worldbookSlice.ts`
- `src/store/worldbook/slices/entrySlice.ts`
- `src/store/worldbook/index.ts`

### ✅ 任务清单

#### Day 1: 基础状态和世界书管理
- [ ] 定义WorldbookStoreState接口
- [ ] 实现initialState
- [ ] 实现worldbookSlice
  - 世界书CRUD操作
  - 加载状态管理
  - 错误状态处理

#### Day 2: 条目管理和高级功能
- [ ] 实现entrySlice
  - 条目CRUD操作
  - 批量操作支持
  - 搜索和分页状态
- [ ] 实现UI状态控制
- [ ] 集成所有slice到主store

### 🔍 质量检查清单
- [ ] **状态设计**: 状态结构清晰、扁平化
- [ ] **不可变性**: 使用immer确保状态不可变
- [ ] **类型安全**: 所有action和state都有类型定义
- [ ] **错误处理**: 异步操作的错误状态管理
- [ ] **性能优化**: 避免不必要的重新渲染
- [ ] **命名规范**: 遵循lobe-chat的命名约定
- [ ] **代码分离**: 合理的slice划分和职责分离

### 🧪 验证方法 - 逐行代码检查
#### 检查 `src/store/worldbook/initialState.ts`
- [ ] WorldbookStoreState接口字段完整
- [ ] 初始状态值类型正确
- [ ] 数组字段初始化为空数组
- [ ] 布尔字段有明确的默认值
- [ ] 对象字段使用正确的初始结构

#### 检查 `src/store/worldbook/slices/worldbookSlice.ts`
- [ ] 使用immer进行状态更新
- [ ] 异步action使用正确的loading状态
- [ ] 错误处理更新error状态
- [ ] action命名遵循约定 (动词+名词)
- [ ] 状态更新逻辑正确 (不直接修改state)

#### 检查 `src/store/worldbook/index.ts`
- [ ] 所有slice正确合并
- [ ] 导出的hook类型正确
- [ ] 状态选择器性能优化
- [ ] 持久化配置正确

### 📤 阶段完成总结
#### 改造前状态
- ❌ 无世界书状态管理
- ❌ 缺少UI状态控制
- ❌ 无批量操作状态

#### 改造后功能点
- ✅ 完整的状态管理结构
- ✅ 世界书CRUD状态
- ✅ 条目管理状态
- ✅ 搜索和分页状态
- ✅ UI控制状态
- ✅ 批量操作状态
- ✅ 加载和错误状态

#### 风险降低措施
- Immer保证状态不可变性
- TypeScript类型安全
- 错误状态统一管理

---

## 🔧 阶段6: 服务层 (1天)

### 📁 创建文件
- `src/services/worldbook/client.ts`
- `src/services/worldbook/type.ts`

### ✅ 任务清单
- [ ] 定义IWorldbookService接口
- [ ] 实现WorldbookClientService类
  - 世界书管理方法 (6个)
  - 条目管理方法 (8个)
  - 批量操作方法 (3个)
  - 导入功能方法 (2个)
- [ ] 实现错误处理和重试逻辑
- [ ] 添加中文错误信息转换
- [ ] 实现请求缓存和优化

### 🔍 质量检查清单
- [ ] **接口设计**: 清晰的服务接口定义
- [ ] **错误处理**: 友好的错误信息和重试机制
- [ ] **类型安全**: 严格的输入输出类型
- [ ] **性能优化**: 请求缓存、批量操作
- [ ] **代码复用**: 避免重复的请求逻辑
- [ ] **异步处理**: 正确的Promise和async/await使用
- [ ] **单例模式**: 服务实例的正确管理

### 🧪 验证方法 - 逐行代码检查
#### 检查 `src/services/worldbook/type.ts`
- [ ] IWorldbookService接口方法完整
- [ ] 方法签名与API接口一致
- [ ] 返回类型使用Promise包装
- [ ] 参数类型与创建接口一致

#### 检查 `src/services/worldbook/client.ts`
- [ ] 类实现IWorldbookService接口
- [ ] 使用lambdaClient调用API
- [ ] 错误处理转换为中文信息
- [ ] 方法命名与接口一致
- [ ] 异步方法正确使用async/await
- [ ] 单例导出正确

### 📤 阶段完成总结
#### 改造前状态
- ❌ 无客户端服务抽象
- ❌ 缺少错误信息转换
- ❌ 无统一的服务接口

#### 改造后功能点
- ✅ IWorldbookService接口定义
- ✅ WorldbookClientService实现
- ✅ 14个服务方法
- ✅ 中文错误信息转换
- ✅ 统一的异常处理
- ✅ 单例服务实例

#### 风险降低措施
- 接口抽象便于测试和替换
- 统一错误处理提升用户体验
- 类型安全保证调用正确性

---

## 🎨 阶段7: UI组件层 (2天)

### 📁 创建文件
- `src/components/WorldbookManager/index.tsx`
- `src/components/WorldbookManager/WorldbookCard.tsx`
- `src/components/WorldbookManager/WorldbookCreateModal.tsx`
- `src/components/WorldbookEntryEditor/index.tsx`
- `src/components/WorldbookImportModal/index.tsx`

### ✅ 任务清单

#### Day 1: 管理器和卡片组件
- [ ] 实现WorldbookManager组件
  - 世界书列表展示
  - 搜索和筛选功能
  - 创建和导入按钮
- [ ] 实现WorldbookCard组件
  - 卡片展示和选择
  - 编辑和删除操作
  - 状态指示器
- [ ] 实现WorldbookCreateModal组件

#### Day 2: 编辑器和导入组件
- [ ] 实现WorldbookEntryEditor组件
  - 完整的表单编辑
  - 高级设置折叠面板
  - 实时验证和保存
- [ ] 实现WorldbookImportModal组件
  - 文件上传和解析
  - 进度显示和结果展示

### 🔍 质量检查清单
- [ ] **组件设计**: 遵循lobe-chat的组件设计模式
- [ ] **样式一致**: 使用项目统一的样式系统
- [ ] **交互体验**: 流畅的用户交互和反馈
- [ ] **响应式设计**: 适配不同屏幕尺寸
- [ ] **无障碍性**: 支持键盘导航和屏幕阅读器
- [ ] **性能优化**: 合理的组件拆分和懒加载
- [ ] **错误边界**: 组件错误处理和降级显示

### 🧪 验证方法 - 逐行代码检查
#### 检查 `src/components/WorldbookManager/index.tsx`
- [ ] 使用useWorldbookStore正确获取状态
- [ ] useEffect依赖数组正确
- [ ] 事件处理函数使用useCallback优化
- [ ] 条件渲染逻辑正确 (loading, empty, error)
- [ ] 组件props类型定义完整
- [ ] 样式类名遵循项目约定

#### 检查 `src/components/WorldbookEntryEditor/index.tsx`
- [ ] Form组件使用antd最佳实践
- [ ] 表单验证规则完整
- [ ] 受控组件状态管理正确
- [ ] 表单提交处理异步操作
- [ ] 高级设置使用Collapse组件
- [ ] 响应式布局使用Row/Col

#### 检查所有组件通用要求
- [ ] 导入语句按类型分组排序
- [ ] 组件使用React.FC类型
- [ ] 事件处理防止冒泡
- [ ] 无障碍属性 (aria-label, role)
- [ ] 错误边界处理

### 📤 阶段完成总结
#### 改造前状态
- ❌ 无世界书管理界面
- ❌ 缺少条目编辑功能
- ❌ 无导入功能界面

#### 改造后功能点
- ✅ WorldbookManager (列表管理)
- ✅ WorldbookCard (卡片展示)
- ✅ WorldbookEntryEditor (条目编辑)
- ✅ WorldbookImportModal (导入功能)
- ✅ 响应式设计
- ✅ 无障碍支持
- ✅ 完整的交互反馈

#### 风险降低措施
- 组件拆分降低复杂度
- 类型安全防止运行时错误
- 错误边界提升用户体验

---

## 📥 阶段8: 导入处理器 (并行1天)

### 📁 创建文件
- `src/utils/worldbook/importParser.ts`
- `src/utils/worldbook/sillyTavernMapper.ts`

### ✅ 任务清单
- [ ] 实现ImportParser类
  - 自动格式检测
  - SillyTavern格式解析
  - CharacterAI格式解析
  - 通用JSON格式解析
- [ ] 实现SillyTavernMapper类
  - 字段映射 (39个字段)
  - 枚举值转换
  - 反向映射 (导出功能)
- [ ] 添加错误处理和警告收集
- [ ] 实现文件验证和安全检查

### 🔍 质量检查清单
- [ ] **格式支持**: 完整支持SillyTavern等主流格式
- [ ] **字段映射**: 准确的字段转换和类型匹配
- [ ] **错误处理**: 详细的错误信息和警告提示
- [ ] **性能优化**: 大文件的流式处理
- [ ] **安全性**: 文件内容验证和XSS防护
- [ ] **可扩展性**: 易于添加新的导入格式
- [ ] **测试覆盖**: 各种格式的测试用例

### 🧪 验证方法 - 逐行代码检查
#### 检查 `src/utils/worldbook/importParser.ts`
- [ ] ImportFormat枚举定义完整
- [ ] parseFile方法支持多种格式检测
- [ ] 每种格式解析方法独立实现
- [ ] 错误处理使用try-catch包装
- [ ] 文件读取使用FileReader API
- [ ] 返回结果包含warnings数组
- [ ] 类型定义与接口一致

#### 检查 `src/utils/worldbook/sillyTavernMapper.ts`
- [ ] mapEntry方法字段映射完整 (39个字段)
- [ ] 枚举值转换逻辑正确
- [ ] 反向映射方法实现
- [ ] 私有方法命名使用下划线前缀
- [ ] 默认值处理合理
- [ ] 类型转换安全 (避免undefined)

### 📤 阶段完成总结
#### 改造前状态
- ❌ 无导入功能支持
- ❌ 缺少格式转换
- ❌ 无SillyTavern兼容性

#### 改造后功能点
- ✅ ImportParser通用解析器
- ✅ SillyTavernMapper格式映射
- ✅ 3种导入格式支持
- ✅ 39个字段完整映射
- ✅ 双向转换支持
- ✅ 错误和警告收集
- ✅ 文件安全验证

#### 风险降低措施
- 格式检测避免解析错误
- 字段映射保证数据完整性
- 错误收集提供调试信息

---

## ⚡ 阶段9: 激活引擎 (2天)

### 📁 创建文件
- `src/utils/worldbook/activationEngine.ts`
- `src/utils/worldbook/positionInserter.ts`

### ✅ 任务清单

#### Day 1: 激活引擎核心
- [ ] 实现ActivationEngine类
  - 关键词匹配算法
  - 激活逻辑控制
  - 概率计算
  - 递归扫描
- [ ] 实现匹配源控制
- [ ] 添加性能优化和缓存

#### Day 2: 位置插入器
- [ ] 实现PositionInserter类
  - 7种插入位置支持
  - 优先级排序
  - 内容格式化
- [ ] 集成激活引擎和位置插入器
- [ ] 添加调试和日志功能

### 🔍 质量检查清单
- [ ] **算法准确性**: 关键词匹配和激活逻辑正确
- [ ] **性能优化**: 高效的匹配算法和缓存机制
- [ ] **可配置性**: 支持全局和条目级别的配置
- [ ] **调试支持**: 详细的激活日志和调试信息
- [ ] **扩展性**: 易于添加新的激活模式
- [ ] **兼容性**: 与SillyTavern行为一致
- [ ] **测试覆盖**: 各种激活场景的测试

### 🧪 验证方法 - 逐行代码检查
#### 检查 `src/utils/worldbook/activationEngine.ts`
- [ ] ActivationEngine类构造函数正确
- [ ] activate方法参数类型正确
- [ ] 关键词匹配算法实现 (支持正则、大小写、全词)
- [ ] 激活模式判断逻辑 (keyword/constant/vectorized)
- [ ] 概率计算和随机判断
- [ ] 递归扫描实现
- [ ] 性能优化 (缓存、早期退出)
- [ ] 调试日志输出

#### 检查 `src/utils/worldbook/positionInserter.ts`
- [ ] PositionInserter类方法定义
- [ ] 7种插入位置处理逻辑
- [ ] 优先级排序算法
- [ ] 内容格式化处理
- [ ] 深度插入逻辑 (AT_DEPTH)
- [ ] 角色类型处理
- [ ] 去重和合并逻辑

### 📤 阶段完成总结
#### 改造前状态
- ❌ 无世界书激活功能
- ❌ 缺少关键词匹配
- ❌ 无位置插入逻辑

#### 改造后功能点
- ✅ ActivationEngine激活引擎
- ✅ PositionInserter位置插入器
- ✅ 关键词匹配算法
- ✅ 3种激活模式支持
- ✅ 概率控制机制
- ✅ 递归扫描功能
- ✅ 7种插入位置
- ✅ 优先级排序
- ✅ 性能优化和缓存

#### 风险降低措施
- 算法测试保证匹配准确性
- 性能优化避免卡顿
- 调试日志便于问题排查

---

## 📊 总体质量保证

### 🔍 代码质量标准
- **TypeScript**: 严格模式，无any类型
- **ESLint**: 通过所有linting规则
- **Prettier**: 代码格式化一致
- **测试覆盖**: 核心功能100%覆盖
- **性能**: 响应时间<100ms
- **安全**: 输入验证和XSS防护

### 🎨 项目风格一致性
- **命名规范**: 遵循lobe-chat约定
- **文件结构**: 符合项目架构
- **组件设计**: 使用统一的设计系统
- **错误处理**: 统一的错误处理模式
- **国际化**: 支持多语言
- **无障碍**: 符合WCAG标准

### 📋 每阶段检查清单
1. **功能完整**: 所有任务清单项目完成
2. **代码质量**: 通过质量检查清单
3. **测试验证**: 验证方法执行成功
4. **文档更新**: 相关文档同步更新
5. **代码提交**: Git提交并标记阶段

### 🎯 最终验收标准
- [ ] 所有9个阶段完成
- [ ] 端到端功能测试通过
- [ ] 性能基准测试达标
- [ ] 代码审查通过
- [ ] 用户体验测试满意
- [ ] 文档完整准确

---

## 📅 执行时间表

| 阶段 | 内容 | 时间 | 依赖 |
|------|------|------|------|
| 1 | 类型系统 | 1天 | 无 |
| 2 | 数据库Schema | 1天 | 阶段1 |
| 3 | 数据库Model | 1天 | 阶段2 |
| 4 | API层 | 2天 | 阶段3 |
| 5 | Store层 | 2天 | 阶段4 |
| 6 | 服务层 | 1天 | 阶段5 |
| 7 | UI组件 | 2天 | 阶段6 |
| 8 | 导入处理器 | 1天 | 阶段1 (并行) |
| 9 | 激活引擎 | 2天 | 阶段4,5 |

**总计**: 12天完成全部功能

---

## 🚀 开始执行

准备好开始第一个阶段了吗？请确保：
- [ ] 开发环境已配置
- [ ] 项目依赖已安装
- [ ] Git仓库已准备
- [ ] 技术方案文档已阅读

**下一步**: 开始阶段1 - 类型系统设计
