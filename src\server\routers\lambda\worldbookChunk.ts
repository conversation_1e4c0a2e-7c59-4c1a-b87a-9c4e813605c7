import { z } from 'zod';

import { WorldbookChunkModel } from '@/database/models/worldbookChunk';
import { authedProcedure, router } from '@/libs/trpc/lambda';
import { serverDatabase } from '@/libs/trpc/lambda/middleware';
import type {
  BulkOperationResult,
  CreateEntryData,
  PaginatedResponse,
  SearchParams,
  UpdateEntryData,
  WorldbookEntry,
} from '@/types/worldbook';
import { createEntryWithWorldbookSchema, updateEntryBaseSchema, searchParamsBaseSchema } from '@/types/worldbook/schemas';

const worldbookChunkProcedure = authedProcedure.use(serverDatabase).use(async (opts) => {
  const { ctx } = opts;

  return opts.next({
    ctx: {
      worldbookChunkModel: new WorldbookChunkModel(ctx.serverDB, ctx.userId),
    },
  });
});

// Zod schemas for validation
const createEntrySchema = createEntryWithWorldbookSchema satisfies z.ZodType<CreateEntryData & { worldbookId: string }>;

const updateEntrySchema = updateEntryBaseSchema.extend({
  content: z.string().min(1, '条目内容不能为空').optional(),
  enabled: z.boolean().optional(),
}) satisfies z.ZodType<UpdateEntryData & { content?: string }>;

const searchParamsSchema = searchParamsBaseSchema.extend({
  orderMin: z.number().min(0).optional(),
  orderMax: z.number().min(0).optional(),
  probabilityMin: z.number().min(0).max(100).optional(),
  probabilityMax: z.number().min(0).max(100).optional(),
}) satisfies z.ZodType<SearchParams>;

export const worldbookChunkRouter = router({
  // ====== Query Operations ======

  /**
   * 根据ID获取条目
   */
  getEntryById: worldbookChunkProcedure
    .input(z.object({ id: z.string().min(1, 'ID不能为空') }))
    .query(async ({ ctx, input }): Promise<WorldbookEntry | null> => {
      return ctx.worldbookChunkModel.findById(input.id);
    }),

  /**
   * 获取世界书的所有条目
   */
  getEntriesByWorldbookId: worldbookChunkProcedure
    .input(z.object({ worldbookId: z.string().min(1, '世界书ID不能为空') }))
    .query(async ({ ctx, input }): Promise<WorldbookEntry[]> => {
      return ctx.worldbookChunkModel.findByWorldbookId(input.worldbookId);
    }),

  /**
   * 分页查询条目（支持搜索和过滤）
   */
  searchEntries: worldbookChunkProcedure
    .input(
      z.object({
        worldbookId: z.string().min(1, '世界书ID不能为空'),
        params: searchParamsSchema.optional(),
      }),
    )
    .query(async ({ ctx, input }): Promise<PaginatedResponse<WorldbookEntry>> => {
      const params = input.params || {};
      return ctx.worldbookChunkModel.findByWorldbookIdWithParams(input.worldbookId, params);
    }),

  // ====== Mutation Operations ======

  /**
   * 创建条目
   */
  createEntry: worldbookChunkProcedure
    .input(createEntrySchema)
    .mutation(async ({ input, ctx }): Promise<WorldbookEntry> => {
      return ctx.worldbookChunkModel.create(input);
    }),

  /**
   * 更新条目
   */
  updateEntry: worldbookChunkProcedure
    .input(
      z.object({
        id: z.string().min(1, 'ID不能为空'),
        data: updateEntrySchema,
      }),
    )
    .mutation(async ({ input, ctx }): Promise<WorldbookEntry | null> => {
      return ctx.worldbookChunkModel.update(input.id, input.data);
    }),

  /**
   * 删除条目
   */
  deleteEntry: worldbookChunkProcedure
    .input(z.object({ id: z.string().min(1, 'ID不能为空') }))
    .mutation(async ({ input, ctx }): Promise<void> => {
      console.log('🗑️ [WorldbookChunkRouter] deleteEntry called:', input.id);

      await ctx.worldbookChunkModel.delete(input.id);

      console.log('✅ [WorldbookChunkRouter] deleteEntry completed:', input.id);
    }),

  // ====== Batch Operations ======

  /**
   * 批量创建条目
   */
  bulkCreateEntries: worldbookChunkProcedure
    .input(
      z.object({
        worldbookId: z.string().min(1, '世界书ID不能为空'),
        entries: z.array(createEntrySchema.omit({ worldbookId: true })).min(1, '至少需要一个条目'),
      }),
    )
    .mutation(async ({ input, ctx }): Promise<WorldbookEntry[]> => {
      console.log('📝 [WorldbookChunkRouter] bulkCreateEntries called:', {
        worldbookId: input.worldbookId,
        entriesCount: input.entries.length,
      });

      const entriesWithWorldbookId = input.entries.map(entry => ({
        ...entry,
        worldbookId: input.worldbookId,
      }));

      const result = await ctx.worldbookChunkModel.bulkCreate(entriesWithWorldbookId);

      console.log('✅ [WorldbookChunkRouter] bulkCreateEntries success:', result.length);
      return result;
    }),

  /**
   * 批量更新条目
   */
  bulkUpdateEntries: worldbookChunkProcedure
    .input(
      z.object({
        ids: z.array(z.string().min(1)).min(1, '至少需要选择一个条目'),
        data: updateEntrySchema.omit({ content: true }), // 批量更新不支持内容更新
      }),
    )
    .mutation(async ({ input, ctx }): Promise<BulkOperationResult> => {
      console.log('📝 [WorldbookChunkRouter] bulkUpdateEntries called:', {
        ids: input.ids,
        data: input.data,
      });

      const errors: Array<{ id: string; error: string }> = [];
      let successCount = 0;

      for (const id of input.ids) {
        try {
          await ctx.worldbookChunkModel.update(id, input.data);
          successCount++;
        } catch (error) {
          console.error(`❌ [WorldbookChunkRouter] Failed to update entry ${id}:`, error);
          errors.push({
            id,
            error: error instanceof Error ? error.message : '更新失败',
          });
        }
      }

      const result = {
        successCount,
        failedCount: errors.length,
        errors,
      };

      console.log('✅ [WorldbookChunkRouter] bulkUpdateEntries completed:', result);
      return result;
    }),

  /**
   * 批量删除条目
   */
  bulkDeleteEntries: worldbookChunkProcedure
    .input(z.object({ ids: z.array(z.string().min(1)).min(1, '至少需要选择一个条目') }))
    .mutation(async ({ input, ctx }): Promise<BulkOperationResult> => {
      console.log('🗑️ [WorldbookChunkRouter] bulkDeleteEntries called:', input.ids);

      const errors: Array<{ id: string; error: string }> = [];
      let successCount = 0;

      for (const id of input.ids) {
        try {
          await ctx.worldbookChunkModel.delete(id);
          successCount++;
        } catch (error) {
          console.error(`❌ [WorldbookChunkRouter] Failed to delete entry ${id}:`, error);
          errors.push({
            id,
            error: error instanceof Error ? error.message : '删除失败',
          });
        }
      }

      const result = {
        successCount,
        failedCount: errors.length,
        errors,
      };

      console.log('✅ [WorldbookChunkRouter] bulkDeleteEntries completed:', result);
      return result;
    }),

  /**
   * 批量启用/禁用条目
   */
  bulkToggleEntries: worldbookChunkProcedure
    .input(
      z.object({
        ids: z.array(z.string().min(1)).min(1, '至少需要选择一个条目'),
        enabled: z.boolean(),
      }),
    )
    .mutation(async ({ input, ctx }): Promise<BulkOperationResult> => {
      console.log('🔄 [WorldbookChunkRouter] bulkToggleEntries called:', {
        ids: input.ids,
        enabled: input.enabled,
      });

      const errors: Array<{ id: string; error: string }> = [];
      let successCount = 0;

      for (const id of input.ids) {
        try {
          await ctx.worldbookChunkModel.update(id, { enabled: input.enabled });
          successCount++;
        } catch (error) {
          console.error(`❌ [WorldbookChunkRouter] Failed to toggle entry ${id}:`, error);
          errors.push({
            id,
            error: error instanceof Error ? error.message : '切换状态失败',
          });
        }
      }

      const result = {
        successCount,
        failedCount: errors.length,
        errors,
      };

      console.log('✅ [WorldbookChunkRouter] bulkToggleEntries completed:', result);
      return result;
    }),
});