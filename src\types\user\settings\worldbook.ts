/**
 * 世界书全局设置类型定义
 */

export interface UserWorldbookConfig {
  /**
   * 是否启用世界书功能
   * @default true
   */
  enabled: boolean;

  /**
   * 扫描深度 - 扫描多少条历史消息
   * @default 4
   */
  scanDepth: number;

  /**
   * 预算百分比 - 世界书占用上下文的百分比
   * @default 25
   */
  budgetPercent: number;

  /**
   * 预算上限（token数）
   * @default 2000
   */
  budgetCap: number;

  /**
   * 最小激活数
   * @default 0
   */
  minActivations: number;

  /**
   * 最小激活深度上限
   * @default 0
   */
  minActivationsDepthMax: number;

  /**
   * 是否使用组评分机制
   * @default false
   */
  useGroupScoring: boolean;

  /**
   * 是否启用递归扫描
   * @default false
   */
  recursiveEnabled: boolean;

  /**
   * 最大递归深度
   * @default 3
   */
  maxRecursionDepth: number;

  /**
   * 默认大小写敏感
   * @default false
   */
  caseSensitive: boolean;

  /**
   * 默认全词匹配
   * @default false
   */
  matchWholeWords: boolean;







  /**
   * 是否包含发送者名称在匹配中
   * @default true
   */
  includeNames: boolean;

  /**
   * 是否启用溢出警告
   * @default false
   */
  overflowAlert: boolean;

  /**
   * 角色策略 - 如何处理角色相关的世界书
   * 0: 禁用, 1: 仅当前角色, 2: 所有角色
   * @default 1
   */
  characterStrategy: number;
}
