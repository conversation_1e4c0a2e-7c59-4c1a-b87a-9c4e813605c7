import { Icon, Modal } from '@lobehub/ui';
import { Download } from 'lucide-react';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import ImportForm from './ImportForm';

interface ImportModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const Title = () => {
  const { t } = useTranslation('worldbook');
  return (
    <Flexbox gap={8} horizontal>
      <Icon icon={Download} />
      {t('import.title')}
    </Flexbox>
  );
};

const ImportModal = memo<ImportModalProps>(({ open, onClose, onSuccess }) => {
  return (
    <Modal
      centered
      footer={null}
      onCancel={onClose}
      open={open}
      styles={{
        body: { padding: 0 },
        content: {
          maxHeight: '70vh',
          overflow: 'hidden',
        },
      }}
      title={<Title />}
      width={600}
    >
      <ImportForm
        onClose={onClose}
        onSuccess={() => {
          onSuccess?.();
          onClose();
        }}
      />
    </Modal>
  );
});

ImportModal.displayName = 'ImportModal';

// Hook模式已移除，统一使用直接的Modal组件管理

export default ImportModal;
