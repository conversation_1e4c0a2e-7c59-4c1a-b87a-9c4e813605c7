/**
 * 世界书相关类型定义
 */

export interface Worldbook {
  createdAt: string;
  description?: string;
  enabled: boolean;
  entryCount?: number;
  id: string;
  lastActivated?: string;
  name: string;
  updatedAt: string;
  userId: string;
  // 专属agent信息
  primaryAgent?: {
    id: string;
    title: string;
  };
}

export interface WorldbookEntry {
  caseSensitive?: boolean;
  chunkId: string;
  constant: boolean;
  content: string;
  cooldown?: number;
  createdAt: string;

  delay?: number;
  delayUntilRecursion: number;
  enabled: boolean;
  excludeRecursion: boolean;
  groupName?: string;
  groupWeight: number;
  scanDepth?: number;
  // 激活规则
  keys: string[];
  worldbookId: string;

  // 高级功能
  sticky?: number;
  order: number;
  position: number;
  title: string;
  keysSecondary: string[];
  selectiveLogic: SelectiveLogic;
  id: string;
  matchWholeWords?: boolean;
  useRegex: boolean;

  probability: number;
  updatedAt: string;
}

export enum SelectiveLogic {
  AND_ANY = 0, // 主关键词 + 任一次关键词
  NOT_ALL = 1, // 主关键词 + 非全部次关键词
  NOT_ANY = 2, // 主关键词 + 无任何次关键词
  AND_ALL = 3, // 主关键词 + 全部次关键词
}

export interface CreateWorldbookData {
  description?: string;
  name: string;
}

export interface UpdateWorldbookData {
  description?: string;
  enabled?: boolean;
  name?: string;
}

export interface CreateEntryData {
  caseSensitive?: boolean;
  constant?: boolean;
  content: string;
  cooldown?: number;
  delay?: number;
  delayUntilRecursion?: number;
  excludeRecursion?: boolean;
  groupName?: string;
  groupWeight?: number;
  keys: string[];
  keysSecondary?: string[];
  matchWholeWords?: boolean;
  order?: number;
  position?: number;
  probability?: number;
  scanDepth?: number;
  selectiveLogic?: SelectiveLogic;
  sticky?: number;
  title: string;
  useRegex?: boolean;
}

export interface UpdateEntryData extends Partial<CreateEntryData> {
  enabled?: boolean;
}

export interface ImportResult {
  errors?: string[];
  failed: number;
  imported: number;
  success: boolean;
}



// 项目标准导入格式
export interface WorldbookImportData {
  worldbook: CreateWorldbookData;
  entries: CreateEntryData[];
  version?: string; // 格式版本，用于向后兼容
}

// 导入结果详情
export interface ImportResultDetail {
  worldbookId: string;
  worldbookName: string;
  totalEntries: number;
  successfulEntries: number;
  failedEntries: number;
  errors: Array<{
    entryIndex: number;
    entryTitle?: string;
    error: string;
  }>;
}

export interface ActivationStatus {
  activeEntries: string[];
  lastActivated: string;
  worldbookId: string;
}

// 批量操作相关类型
export interface BulkUpdateData {
  enabled?: boolean;
  groupName?: string;
  order?: number;
  probability?: number;
}

export interface BulkOperationResult {
  errors?: string[];
  failed: number;
  success: number;
}

// SillyTavern 格式相关类型（用于兼容导入）
export interface SillyTavernWorldbook {
  name?: string;
  description?: string;
  entries: { [key: string]: SillyTavernEntry };
}

export interface SillyTavernEntry {
  key: string[];
  keysecondary?: string[];
  content: string;
  comment?: string;
  order?: number;
  selective?: number | boolean;
  selectiveLogic?: number;
  constant?: boolean;
  probability?: number;
  position?: number;
  disable?: boolean;
  excludeRecursion?: boolean;
  caseSensitive?: boolean;
  matchWholeWords?: boolean;
  useRegex?: boolean;
  depth?: number;
  group?: string;
  groupWeight?: number;
  sticky?: number;
  cooldown?: number;
  delay?: number;
  delayUntilRecursion?: number;
  // 其他可能的字段
  [key: string]: any;
}
