import { StateCreator } from 'zustand';

import { worldbookService } from '@/services/worldbook/client';
import type {
  CreateEntryData,
  CreateWorldbookData,
  ImportResultDetail,
  UpdateWorldbookData,
} from '@/types/worldbook';

import type { WorldbookStore } from '../../store';

export interface WorldbookAction {
  createWorldbook: (data: CreateWorldbookData) => Promise<void>;
  deleteWorldbook: (id: string) => Promise<void>;
  // 世界书操作
  fetchWorldbooks: () => Promise<void>;
  // 导入导出
  importWorldbook: (data: {
    entries: CreateEntryData[];
    version?: string;
    worldbook: CreateWorldbookData;
  }) => Promise<ImportResultDetail>;

  resetState: () => void;
  setCurrentWorldbook: (id: string | null) => void;

  setFilterEnabled: (enabled: boolean | null) => void;
  setSearchQuery: (query: string) => void;
  setSortBy: (sortBy: 'name' | 'date' | 'order') => void;
  setSortOrder: (order: 'asc' | 'desc') => void;
  // UI控制
  toggleSidebar: () => void;
  updateWorldbook: (id: string, data: UpdateWorldbookData) => Promise<void>;

  // 暂时移除SWR hooks，避免服务端渲染问题
  // useFetchWorldbooks: () => SWRResponse<Worldbook[]>;
  // useFetchWorldbook: (id: string) => SWRResponse<Worldbook | undefined>;
}

export const createWorldbookSlice: StateCreator<
  WorldbookStore,
  [['zustand/devtools', never]],
  [],
  WorldbookAction
> = (set, get) => ({
  createWorldbook: async (data) => {
    try {
      await worldbookService.createWorldbook(data);
      // 创建成功后重新获取列表，确保数据一致性
      const { fetchWorldbooks } = get();
      await fetchWorldbooks();
    } catch (error) {
      console.error('Failed to create worldbook:', error);
      throw error;
    }
  },

  deleteWorldbook: async (id) => {
    try {
      await worldbookService.deleteWorldbook(id);
      set((state) => ({
        currentWorldbook: state.currentWorldbook?.id === id ? null : state.currentWorldbook,
        selectedWorldbookId: state.selectedWorldbookId === id ? null : state.selectedWorldbookId,
        worldbooks: state.worldbooks.filter((wb) => wb.id !== id),
      }));
    } catch (error) {
      console.error('Failed to delete worldbook:', error);
      throw error;
    }
  },



  fetchWorldbooks: async () => {
    set({ loading: true });
    try {
      const worldbooks = await worldbookService.getWorldbooks();
      set({ loading: false, worldbooks });
    } catch (error) {
      console.error('Failed to fetch worldbooks:', error);
      set({ loading: false });
      throw error;
    }
  },

  importWorldbook: async (data) => {
    set({ importing: true });
    try {
      // 使用新的统一导入API
      const result = await worldbookService.importWorldbook(data);

      // 更新世界书列表
      const { fetchWorldbooks } = get();
      await fetchWorldbooks();

      set({ importing: false });

      return result;
    } catch (error) {
      set({ importing: false });
      console.error('Failed to import worldbook:', error);
      throw error;
    }
  },

  resetState: () => {
    set({
      currentEntry: null,
      currentWorldbook: null,
      entries: [],
      filterEnabled: null,
      importing: false,
      loading: false,
      searchQuery: '',
      selectedEntryIds: [],
      selectedWorldbookId: null,
      sortBy: 'order',
      sortOrder: 'asc',
      worldbooks: [],
    });
  },

  setCurrentWorldbook: (id) => {
    const worldbook = id ? get().worldbooks.find((wb) => wb.id === id) : null;
    set({
      currentWorldbook: worldbook || null,
      selectedWorldbookId: id,
    });
  },

  setFilterEnabled: (enabled) => {
    set({ filterEnabled: enabled });
  },

  setSearchQuery: (query) => {
    set({ searchQuery: query });
  },

  setSortBy: (sortBy) => {
    set({ sortBy });
  },

  setSortOrder: (order) => {
    set({ sortOrder: order });
  },

  toggleSidebar: () => {
    set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed }));
  },

  updateWorldbook: async (id, data) => {
    try {
      const updatedWorldbook = await worldbookService.updateWorldbook(id, data);
      set((state) => ({
        currentWorldbook:
          state.currentWorldbook?.id === id ? updatedWorldbook : state.currentWorldbook,
        worldbooks: state.worldbooks.map((wb) => (wb.id === id ? updatedWorldbook : wb)),
      }));
    } catch (error) {
      console.error('Failed to update worldbook:', error);
      throw error;
    }
  },

  // 暂时移除SWR hooks实现，避免服务端渲染问题
  // 后续会在客户端组件中直接使用SWR
});
