-- =====================================================
-- 世界书功能完整建表语句
-- 基于技术方案的47个字段设计
-- 支持所有新功能和向后兼容
--
-- 重要提示：
-- 1. 本建表语句依赖于Drizzle ORM已创建的枚举类型
-- 2. 建议使用Drizzle迁移而不是直接执行此SQL
-- 3. 此文件主要用于参考和理解表结构
-- =====================================================

-- 注意：项目不使用PostgreSQL枚举类型
-- 所有枚举字段使用TEXT类型存储字符串值

-- 创建世界书主表
-- =====================================================
CREATE TABLE worldbooks (
  -- 基础标识
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  enabled BOOLEAN DEFAULT true,
  
  -- 用户关联
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  client_id TEXT,
  
  -- 时间戳
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建世界书条目表 (完整的47个字段)
-- =====================================================
CREATE TABLE worldbook_chunks (
  -- 基础标识字段 (4个)
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  enabled BOOLEAN DEFAULT true,
  
  -- 关联字段 (2个)
  worldbook_id TEXT NOT NULL REFERENCES worldbooks(id) ON DELETE CASCADE,
  chunk_id UUID NOT NULL REFERENCES chunks(id) ON DELETE CASCADE,
  
  -- 激活规则字段 (4个)
  keys TEXT[] DEFAULT '{}',
  keys_secondary TEXT[] DEFAULT '{}',
  selective_logic TEXT DEFAULT 'and_any',
  activation_mode TEXT DEFAULT 'keyword',

  -- 位置控制字段 (4个)
  position TEXT DEFAULT 'after',
  depth INTEGER DEFAULT 4,
  role TEXT DEFAULT 'system',
  "order" INTEGER DEFAULT 100,
  
  -- 匹配配置字段 (7个)
  match_sources JSONB DEFAULT '{
    "personaDescription": false,
    "characterDescription": false,
    "characterPersonality": false,
    "characterDepthPrompt": false,
    "scenario": false,
    "creatorNotes": false
  }',
  case_sensitive BOOLEAN DEFAULT false,
  match_whole_words BOOLEAN DEFAULT false,
  use_regex BOOLEAN DEFAULT false,
  
  -- 概率控制字段 (2个)
  probability INTEGER DEFAULT 100,
  use_probability BOOLEAN DEFAULT true,
  
  -- 时间效果字段 (3个)
  sticky INTEGER,
  cooldown INTEGER,
  delay INTEGER,
  
  -- 递归控制字段 (3个)
  exclude_recursion BOOLEAN DEFAULT false,
  prevent_recursion BOOLEAN DEFAULT false,
  delay_until_recursion INTEGER DEFAULT 0,
  
  -- 分组功能字段 (4个)
  group_name TEXT,
  group_weight INTEGER DEFAULT 100,
  group_override BOOLEAN DEFAULT false,
  use_group_scoring BOOLEAN DEFAULT false,
  
  -- 扫描控制字段 (1个)
  scan_depth INTEGER,
  
  -- 显示控制字段 (2个)
  display_index INTEGER DEFAULT 0,
  add_memo BOOLEAN DEFAULT false,
  
  -- 装饰器支持字段 (1个)
  decorators TEXT[] DEFAULT '{}',
  
  -- 角色过滤字段 (3个)
  character_filter_names TEXT[] DEFAULT '{}',
  character_filter_tags TEXT[] DEFAULT '{}',
  character_filter_exclude BOOLEAN DEFAULT false,
  
  -- 系统字段 (4个)
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  client_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引优化查询性能
-- =====================================================

-- 世界书主表索引
CREATE INDEX worldbooks_user_id_idx ON worldbooks(user_id);
CREATE INDEX worldbooks_name_idx ON worldbooks(name);
CREATE INDEX worldbooks_enabled_idx ON worldbooks(enabled);

-- 世界书条目表基础索引
CREATE INDEX worldbook_chunks_worldbook_id_idx ON worldbook_chunks(worldbook_id);
CREATE INDEX worldbook_chunks_user_id_idx ON worldbook_chunks(user_id);
CREATE INDEX worldbook_chunks_enabled_idx ON worldbook_chunks(enabled);
CREATE INDEX worldbook_chunks_order_idx ON worldbook_chunks("order");
CREATE INDEX worldbook_chunks_chunk_id_idx ON worldbook_chunks(chunk_id);

-- 世界书条目表查询优化索引
CREATE INDEX worldbook_chunks_activation_mode_idx ON worldbook_chunks(activation_mode);
CREATE INDEX worldbook_chunks_position_idx ON worldbook_chunks(position);
CREATE INDEX worldbook_chunks_probability_idx ON worldbook_chunks(probability);
CREATE INDEX worldbook_chunks_group_name_idx ON worldbook_chunks(group_name);

-- 复合索引优化常用查询
CREATE INDEX worldbook_chunks_worldbook_enabled_order_idx ON worldbook_chunks(worldbook_id, enabled, "order");
CREATE INDEX worldbook_chunks_user_worldbook_idx ON worldbook_chunks(user_id, worldbook_id);

-- 添加表注释
-- =====================================================
COMMENT ON TABLE worldbooks IS '世界书主表 - 存储世界书基础信息';
COMMENT ON TABLE worldbook_chunks IS '世界书条目表 - 存储条目的完整配置信息，通过chunk_id外键关联chunks表获取内容';

-- 字段注释
COMMENT ON COLUMN worldbook_chunks.match_sources IS '匹配源配置 - JSON格式存储6种匹配源的开关状态';
COMMENT ON COLUMN worldbook_chunks.keys IS '主关键词数组 - 触发条目的主要关键词';
COMMENT ON COLUMN worldbook_chunks.keys_secondary IS '次关键词数组 - 配合主关键词的次要关键词';
COMMENT ON COLUMN worldbook_chunks.selective_logic IS '选择逻辑 - 控制主次关键词的匹配逻辑';
COMMENT ON COLUMN worldbook_chunks.activation_mode IS '激活模式 - 关键字/始终/向量化激活';
COMMENT ON COLUMN worldbook_chunks.position IS '注入位置 - 控制内容在对话中的插入位置';
COMMENT ON COLUMN worldbook_chunks.depth IS '深度 - 当position为at_depth时使用';
COMMENT ON COLUMN worldbook_chunks.role IS '角色 - 当position为at_depth时使用';
COMMENT ON COLUMN worldbook_chunks.probability IS '概率 - 条目激活的概率(0-100)';
COMMENT ON COLUMN worldbook_chunks.decorators IS '装饰器数组 - 应用于条目的装饰器';
COMMENT ON COLUMN worldbook_chunks.character_filter_names IS '角色名称过滤 - 限制条目对特定角色生效';
COMMENT ON COLUMN worldbook_chunks.character_filter_tags IS '角色标签过滤 - 限制条目对特定标签的角色生效';

-- 创建更新时间触发器
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_worldbooks_updated_at 
    BEFORE UPDATE ON worldbooks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_worldbook_chunks_updated_at 
    BEFORE UPDATE ON worldbook_chunks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 建表语句完成
--
-- 功能特性：
-- ✅ 支持47个字段的完整功能
-- ✅ 包含所有新增的高级功能
-- ✅ 优化的索引设计
-- ✅ 完整的约束和默认值
-- ✅ 详细的注释说明
-- ✅ 自动更新时间戳
--
-- 推荐使用方式：
-- 1. 使用Drizzle迁移 (推荐)：
--    npm run db:generate
--    npm run db:migrate
--
-- 2. 直接执行SQL (仅用于参考)：
--    确保枚举类型已存在后执行此文件
--
-- 3. 验证表结构：
--    \d worldbooks
--    \d worldbook_chunks
-- =====================================================
