import { Icon, Modal } from '@lobehub/ui';
import { BookOpen } from 'lucide-react';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import WorldbookForm from '../components/WorldbookForm';

interface CreateWorldbookModalProps {
  onSuccess?: () => void;
  open: boolean;
  onClose: () => void;
}

const Title = () => {
  const { t } = useTranslation('worldbook');
  return (
    <Flexbox gap={8} horizontal>
      <Icon icon={BookOpen} />
      {t('create')}
    </Flexbox>
  );
};

const CreateWorldbookModal = memo<CreateWorldbookModalProps>(({ onSuccess, open, onClose }) => {
  return (
    <Modal
      centered
      footer={null}
      onCancel={onClose}
      open={open}
      styles={{
        body: { padding: 0 },
        content: {
          maxHeight: '80vh',
          overflow: 'hidden',
        },
      }}
      title={<Title />}
      width={600}
    >
      <WorldbookForm
        onClose={onClose}
        onSuccess={() => {
          onSuccess?.();
          onClose();
        }}
      />
    </Modal>
  );
});

// Hook模式已移除，统一使用直接的Modal组件管理

export default CreateWorldbookModal;
