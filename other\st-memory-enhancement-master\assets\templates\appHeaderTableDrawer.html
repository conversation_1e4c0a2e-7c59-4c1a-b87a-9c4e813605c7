<div id="table_database_settings_drawer" class="drawer">
    <div class="drawer-toggle">
        <div id="table_drawer_icon" class="drawer-icon fa-solid fa-bars-progress fa-fw closedIcon interactable" title="事件表" data-i18n="[title]Event table" tabindex="0"></div>
    </div>
    <div id="table_drawer_content" class="drawer-content closedIcon" data-slide-toggle="hidden" style="display: none; ">
        <!-- 切换界面按钮 -->
        <div id="table_button_container" class="wide100p flex-container" style="display: flex; flex-grow: 1; justify-content: space-between">
            <div style="display: flex; flex-grow: 1; gap: 5px;">
                <button id="database_button" class="menu_button_icon menu_button interactable"><i class="fa-solid fa-database"></i><b data-i18n="Data">数据</b></button>
                <button id="editor_button" class="menu_button_icon menu_button interactable"><i class="fa-solid fa-pen-ruler"></i><b data-i18n="Template">模板</b></button>
                <!--                <button id="nodes_button" class="menu_button_icon menu_button interactable"><i class="fa-brands fa-hubspot"></i><b>流程</b></button>-->
            </div>
            <button id="setting_button" class="menu_button_icon menu_button interactable"><i class="fa-solid fa-gears"></i><b data-i18n="Settings">设置</b></button>
        </div>
        <!-- 加载对话表功能面板 -->
        <div id="app_header_table_container" style="max-height: 100%; overflow-y: auto; flex-grow: 1;">

        </div>
    </div>
</div>

<style>
    #table_drawer_content {
        padding: 0 0 0 5px;
        overflow-y: scroll;
        display: flex;
        flex-direction: column;
        &::-webkit-scrollbar {

        }
        &::-webkit-scrollbar-thumb {

        }
        &::-webkit-scrollbar-track {
            background-color: transparent;
        }
    }

    /* 创建一个粘性容器，用于固定按钮栏 */
    #table_button_container {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: var(--SmartThemeBlurTintColor, rgba(0, 0, 0, 0.5));
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        padding: 1px 0;
        margin-bottom: 5px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* 确保按钮栏在滚动时不会被遮挡 */
    #app_header_table_container {
        overflow-y: auto;
        flex-grow: 1;
    }
</style>
