import { StateCreator } from 'zustand';

import { worldbookService } from '@/services/worldbook';
import type {
  CreateEntryData,
  UpdateEntryData,
  SearchParams,
  BulkOperationResult,
} from '@/types/worldbook';

import type { WorldbookStore } from '../../store';

export interface WorldbookEntryAction {
  // ====== CRUD Operations ======
  fetchEntries: (worldbookId: string, params?: Partial<SearchParams>) => Promise<void>;
  fetchEntryById: (id: string) => Promise<void>;
  createEntry: (worldbookId: string, data: CreateEntryData) => Promise<void>;
  updateEntry: (id: string, data: UpdateEntryData & { content?: string }) => Promise<void>;
  deleteEntry: (id: string) => Promise<void>;
  
  // ====== Pagination Operations ======
  loadMoreEntries: (worldbookId: string) => Promise<void>;
  refreshEntries: (worldbookId: string) => Promise<void>;
  
  // ====== Batch Operations ======
  bulkCreateEntries: (worldbookId: string, entries: CreateEntryData[]) => Promise<void>;
  bulkUpdateEntries: (ids: string[], data: Partial<UpdateEntryData>) => Promise<BulkOperationResult>;
  bulkDeleteEntries: (ids: string[]) => Promise<BulkOperationResult>;
  bulkToggleEntries: (ids: string[], enabled: boolean) => Promise<BulkOperationResult>;
  
  // ====== Search and Filter Operations ======
  setSearchParams: (params: Partial<SearchParams>) => void;
  resetSearchParams: () => void;
  setFilterEnabled: (enabled: boolean | null) => void;
  setSortBy: (sortBy: 'order' | 'title' | 'createdAt' | 'updatedAt' | 'probability') => void;
  setSortOrder: (order: 'asc' | 'desc') => void;
  
  // ====== Selection Operations ======
  setCurrentEntry: (id: string | null) => void;
  setSelectedEntryIds: (ids: string[]) => void;
  toggleEntrySelection: (id: string) => void;
  selectAllEntries: () => void;
  clearEntrySelection: () => void;
  
  // ====== UI State Management ======
  setEntryEditorVisible: (visible: boolean) => void;
  resetEntryState: () => void;
  
  // ====== Internal State Management ======
  internal_setEntryLoading: (id: string, loading: boolean) => void;
  internal_setEntryUpdating: (id: string, updating: boolean) => void;
  internal_setEntriesLoading: (loading: boolean) => void;
}

export const createWorldbookEntrySlice: StateCreator<
  WorldbookStore,
  [['zustand/devtools', never]],
  [],
  WorldbookEntryAction
> = (set, get) => ({
  // ====== CRUD Operations ======
  
  fetchEntries: async (worldbookId, params = {}) => {
    get().internal_setEntriesLoading(true);
    try {
      const state = get();
      const searchParams = {
        ...state.searchParams,
        ...params,
        page: params.page || 1, // 重置页码，除非明确指定
      };
      
      console.log('🔍 [EntryStore] Fetching entries for worldbook:', worldbookId, 'with params:', searchParams);
      
      const response = await worldbookService.searchEntries(worldbookId, searchParams);
      
      console.log('✅ [EntryStore] Fetched entries:', response.data.length, 'total:', response.total);
      
      set({
        entries: response.data,
        entriesTotal: response.total,
        entriesPage: response.page,
        entriesPageSize: response.pageSize,
        entriesHasMore: response.hasMore,
        searchParams,
      });
    } catch (error) {
      console.error('❌ [EntryStore] Failed to fetch entries:', error);
      throw error;
    } finally {
      get().internal_setEntriesLoading(false);
    }
  },

  fetchEntryById: async (id) => {
    get().internal_setEntryLoading(id, true);
    try {
      console.log('🔍 [EntryStore] Fetching entry by id:', id);
      const entry = await worldbookService.getEntry(id);
      console.log('✅ [EntryStore] Fetched entry:', entry?.id);
      
      if (entry) {
        set((state) => ({
          entries: state.entries.map((e) => e.id === id ? entry : e),
          currentEntry: state.currentEntry?.id === id ? entry : state.currentEntry,
        }));
      }
    } catch (error) {
      console.error('❌ [EntryStore] Failed to fetch entry:', error);
      throw error;
    } finally {
      get().internal_setEntryLoading(id, false);
    }
  },

  createEntry: async (worldbookId, data) => {
    try {
      console.log('📝 [EntryStore] Creating entry for worldbook:', worldbookId);
      const newEntry = await worldbookService.createEntry(worldbookId, data);
      console.log('✅ [EntryStore] Created entry:', newEntry.id);
      
      set((state) => ({
        entries: [newEntry, ...state.entries],
        entriesTotal: state.entriesTotal + 1,
      }));
    } catch (error) {
      console.error('❌ [EntryStore] Failed to create entry:', error);
      throw error;
    }
  },

  updateEntry: async (id, data) => {
    get().internal_setEntryUpdating(id, true);
    try {
      console.log('📝 [EntryStore] Updating entry:', id);
      const updatedEntry = await worldbookService.updateEntry(id, data);
      console.log('✅ [EntryStore] Updated entry:', updatedEntry?.id);
      
      if (updatedEntry) {
        set((state) => ({
          entries: state.entries.map((e) => e.id === id ? updatedEntry : e),
          currentEntry: state.currentEntry?.id === id ? updatedEntry : state.currentEntry,
        }));
      }
    } catch (error) {
      console.error('❌ [EntryStore] Failed to update entry:', error);
      throw error;
    } finally {
      get().internal_setEntryUpdating(id, false);
    }
  },

  deleteEntry: async (id) => {
    get().internal_setEntryLoading(id, true);
    try {
      console.log('🗑️ [EntryStore] Deleting entry:', id);
      await worldbookService.deleteEntry(id);
      console.log('✅ [EntryStore] Deleted entry:', id);
      
      set((state) => ({
        entries: state.entries.filter((e) => e.id !== id),
        currentEntry: state.currentEntry?.id === id ? null : state.currentEntry,
        selectedEntryIds: state.selectedEntryIds.filter((entryId) => entryId !== id),
        entriesTotal: Math.max(0, state.entriesTotal - 1),
      }));
    } catch (error) {
      console.error('❌ [EntryStore] Failed to delete entry:', error);
      throw error;
    } finally {
      get().internal_setEntryLoading(id, false);
    }
  },

  // ====== Pagination Operations ======
  
  loadMoreEntries: async (worldbookId) => {
    const state = get();
    if (!state.entriesHasMore || state.entriesLoading) return;
    
    try {
      const nextPage = state.entriesPage + 1;
      console.log('📄 [EntryStore] Loading more entries, page:', nextPage);
      
      const response = await worldbookService.searchEntries(worldbookId, {
        ...state.searchParams,
        page: nextPage,
      });
      
      console.log('✅ [EntryStore] Loaded more entries:', response.data.length);
      
      set({
        entries: [...state.entries, ...response.data],
        entriesPage: response.page,
        entriesHasMore: response.hasMore,
      });
    } catch (error) {
      console.error('❌ [EntryStore] Failed to load more entries:', error);
      throw error;
    }
  },

  refreshEntries: async (worldbookId) => {
    const state = get();
    await get().fetchEntries(worldbookId, { ...state.searchParams, page: 1 });
  },

  // ====== Batch Operations ======
  
  bulkCreateEntries: async (worldbookId, entries) => {
    get().internal_setBulkOperationLoading(true);
    try {
      console.log('📝 [EntryStore] Bulk creating entries:', entries.length);
      const newEntries = await worldbookService.bulkCreateEntries(worldbookId, entries);
      console.log('✅ [EntryStore] Bulk created entries:', newEntries.length);
      
      set((state) => ({
        entries: [...newEntries, ...state.entries],
        entriesTotal: state.entriesTotal + newEntries.length,
      }));
    } catch (error) {
      console.error('❌ [EntryStore] Failed to bulk create entries:', error);
      throw error;
    } finally {
      get().internal_setBulkOperationLoading(false);
    }
  },

  bulkUpdateEntries: async (ids, data) => {
    get().internal_setBulkOperationLoading(true);
    try {
      console.log('📝 [EntryStore] Bulk updating entries:', ids);
      const result = await worldbookService.bulkUpdateEntries(ids, data);
      console.log('✅ [EntryStore] Bulk update result:', result);
      
      // 更新成功的条目
      const successfulIds = ids.filter(id => 
        !result.errors.some(error => error.id === id)
      );
      
      set((state) => ({
        entries: state.entries.map((entry) =>
          successfulIds.includes(entry.id) ? { ...entry, ...data } : entry
        ),
        lastBulkOperationResult: result,
      }));
      
      return result;
    } catch (error) {
      console.error('❌ [EntryStore] Failed to bulk update entries:', error);
      throw error;
    } finally {
      get().internal_setBulkOperationLoading(false);
    }
  },

  bulkDeleteEntries: async (ids) => {
    get().internal_setBulkOperationLoading(true);
    try {
      console.log('🗑️ [EntryStore] Bulk deleting entries:', ids);
      const result = await worldbookService.bulkDeleteEntries(ids);
      console.log('✅ [EntryStore] Bulk delete result:', result);
      
      // 移除成功删除的条目
      const successfulIds = ids.filter(id => 
        !result.errors.some(error => error.id === id)
      );
      
      set((state) => ({
        entries: state.entries.filter((entry) => !successfulIds.includes(entry.id)),
        selectedEntryIds: state.selectedEntryIds.filter((id) => !successfulIds.includes(id)),
        entriesTotal: Math.max(0, state.entriesTotal - successfulIds.length),
        lastBulkOperationResult: result,
      }));
      
      return result;
    } catch (error) {
      console.error('❌ [EntryStore] Failed to bulk delete entries:', error);
      throw error;
    } finally {
      get().internal_setBulkOperationLoading(false);
    }
  },

  bulkToggleEntries: async (ids, enabled) => {
    get().internal_setBulkOperationLoading(true);
    try {
      console.log('🔄 [EntryStore] Bulk toggling entries:', ids, 'enabled:', enabled);
      const result = await worldbookService.bulkToggleEntries(ids, enabled);
      console.log('✅ [EntryStore] Bulk toggle result:', result);
      
      // 更新成功的条目
      const successfulIds = ids.filter(id => 
        !result.errors.some(error => error.id === id)
      );
      
      set((state) => ({
        entries: state.entries.map((entry) =>
          successfulIds.includes(entry.id) ? { ...entry, enabled } : entry
        ),
        lastBulkOperationResult: result,
      }));
      
      return result;
    } catch (error) {
      console.error('❌ [EntryStore] Failed to bulk toggle entries:', error);
      throw error;
    } finally {
      get().internal_setBulkOperationLoading(false);
    }
  },

  // ====== Search and Filter Operations ======

  setSearchParams: (params) => {
    set((state) => ({
      searchParams: { ...state.searchParams, ...params },
    }));
  },

  resetSearchParams: () => {
    set((state) => ({
      searchParams: {
        ...state.searchParams,
        page: 1,
        query: '',
        enabled: undefined,
        activationMode: undefined,
        position: undefined,
        groupName: undefined,
        hasKeys: undefined,
        hasSecondaryKeys: undefined,
        selectiveLogic: undefined,
        orderMin: undefined,
        orderMax: undefined,
        probabilityMin: undefined,
        probabilityMax: undefined,
      },
    }));
  },

  setFilterEnabled: (enabled) => {
    set((state) => ({
      filterEnabled: enabled,
      searchParams: { ...state.searchParams, enabled, page: 1 },
    }));
  },

  setSortBy: (sortBy) => {
    set((state) => ({
      sortBy,
      searchParams: { ...state.searchParams, sortBy, page: 1 },
    }));
  },

  setSortOrder: (sortOrder) => {
    set((state) => ({
      sortOrder,
      searchParams: { ...state.searchParams, sortOrder, page: 1 },
    }));
  },

  // ====== Selection Operations ======

  setCurrentEntry: (id) => {
    const entry = id ? get().entries.find((e) => e.id === id) : null;
    set({ currentEntry: entry || null });
  },

  setSelectedEntryIds: (ids) => {
    set({ selectedEntryIds: ids });
  },

  toggleEntrySelection: (id) => {
    set((state) => ({
      selectedEntryIds: state.selectedEntryIds.includes(id)
        ? state.selectedEntryIds.filter((entryId) => entryId !== id)
        : [...state.selectedEntryIds, id],
    }));
  },

  selectAllEntries: () => {
    const { entries } = get();
    set({ selectedEntryIds: entries.map((entry) => entry.id) });
  },

  clearEntrySelection: () => {
    set({ selectedEntryIds: [] });
  },

  // ====== UI State Management ======

  setEntryEditorVisible: (visible) => {
    set({ entryEditorVisible: visible });
  },

  resetEntryState: () => {
    set({
      entries: [],
      currentEntry: null,
      selectedEntryIds: [],
      entriesTotal: 0,
      entriesPage: 1,
      entriesHasMore: false,
      entriesLoading: false,
      entryLoadingIds: [],
      entryUpdatingIds: [],
    });
  },

  // ====== Internal State Management ======

  internal_setEntryLoading: (id, loading) => {
    set((state) => ({
      entryLoadingIds: loading
        ? [...state.entryLoadingIds, id]
        : state.entryLoadingIds.filter((loadingId) => loadingId !== id),
    }));
  },

  internal_setEntryUpdating: (id, updating) => {
    set((state) => ({
      entryUpdatingIds: updating
        ? [...state.entryUpdatingIds, id]
        : state.entryUpdatingIds.filter((updatingId) => updatingId !== id),
    }));
  },

  internal_setEntriesLoading: (loading) => {
    set({ entriesLoading: loading });
  },

  internal_setBulkOperationLoading: (loading) => {
    set({ bulkOperationLoading: loading });
  },
});
