# Lobe-Chat 世界书激活技术规范

## 📋 技术实现规范

### 项目规范遵循
- **代码风格**：严格遵循 Lobe-Chat 的 ESLint 和 Prettier 配置
- **TypeScript**：使用严格模式，完整的类型定义
- **文件命名**：使用 camelCase，与项目现有命名保持一致
- **导入导出**：使用 ES6 模块语法，遵循项目的导入顺序规范
- **错误处理**：使用项目统一的错误处理机制

### 依赖管理
- **复用现有依赖**：最大化利用项目现有的依赖包
- **新增依赖最小化**：仅在必要时添加新的依赖
- **版本兼容性**：确保新依赖与现有依赖的版本兼容

## 🏗️ 核心类实现规范

### 1. WorldbookActivationEngine 实现

**类设计原则**：
- 单一职责：专注于激活流程的协调
- 依赖注入：通过构造函数注入依赖
- 错误隔离：每个步骤的错误不影响其他步骤
- 状态管理：维护清晰的激活状态

**方法设计**：
```typescript
class WorldbookActivationEngine {
  // 构造函数：注入必要的依赖
  constructor(
    private db: LobeChatDatabase,
    private userId: string,
    private config: WorldbookConfig
  )
  
  // 主激活方法：统一的激活入口
  async activateWorldbooks(params: ActivationParams): Promise<ActivationResult>
  
  // 私有方法：内部实现细节
  private async initializeActivationContext(): Promise<ActivationContext>
  private async executeActivationCycle(): Promise<ActivatedEntry[]>
  private async processActivationResults(): Promise<string>
}
```

**错误处理策略**：
- 使用 try-catch 包装每个主要步骤
- 记录详细的错误日志和上下文信息
- 提供降级机制，确保基本功能可用
- 返回部分结果而不是完全失败

### 2. 激活器实现规范

**接口统一性**：
所有激活器都实现 `IActivator` 接口，确保行为一致性

**常驻激活器 (ConstantActivator)**：
```typescript
class ConstantActivator implements IActivator {
  canActivate(entry: WorldbookEntry): boolean {
    return entry.activationMode === ActivationMode.Constant
  }
  
  async activate(entry: WorldbookEntry): Promise<ActivationDecision> {
    // 直接返回激活决策，无需额外检查
    return { shouldActivate: true, reason: 'constant_mode' }
  }
}
```

**关键词激活器 (KeywordActivator)**：
```typescript
class KeywordActivator implements IActivator {
  constructor(private keywordMatcher: KeywordMatcher) {}
  
  canActivate(entry: WorldbookEntry): boolean {
    return entry.activationMode === ActivationMode.Keyword && 
           entry.keys.length > 0
  }
  
  async activate(entry: WorldbookEntry, context: ActivationContext): Promise<ActivationDecision> {
    // 执行关键词匹配逻辑
    const scanText = context.scanBuffer.getScanText(entry)
    const primaryMatch = this.checkPrimaryKeywords(entry, scanText)
    const secondaryMatch = this.checkSecondaryLogic(entry, scanText)
    
    return {
      shouldActivate: primaryMatch && secondaryMatch,
      reason: 'keyword_match',
      matchedKeys: this.getMatchedKeys(entry, scanText)
    }
  }
}
```

**向量化激活器 (VectorizedActivator)**：
```typescript
class VectorizedActivator implements IActivator {
  constructor(
    private chunkModel: ChunkModel,
    private embeddingService: EmbeddingService
  ) {}
  
  canActivate(entry: WorldbookEntry): boolean {
    return entry.activationMode === ActivationMode.Vectorized &&
           entry.chunkId !== null
  }
  
  async activate(entry: WorldbookEntry, context: ActivationContext): Promise<ActivationDecision> {
    // 检查是否已被外部激活（向量化搜索结果）
    const externalActivation = context.scanBuffer.getExternalActivation(entry)
    
    return {
      shouldActivate: externalActivation !== null,
      reason: 'vectorized_search',
      similarity: externalActivation?.similarity
    }
  }
}
```

### 3. 扫描缓冲区实现规范

**数据结构设计**：
```typescript
class ScanBuffer implements IScanBuffer {
  private depthBuffer: string[] = []
  private recurseBuffer: string[] = []
  private globalScanData: GlobalScanData
  private externalActivations: Map<string, ExternalActivation> = new Map()
  
  constructor(messages: OpenAIChatMessage[], globalScanData: GlobalScanData) {
    this.initializeDepthBuffer(messages)
    this.globalScanData = globalScanData
  }
}
```

**关键方法实现**：
- `getScanText()`: 根据条目的匹配源设置构建扫描文本
- `matchKeywords()`: 执行关键词匹配，支持多种匹配模式
- `addRecurseContent()`: 添加递归内容到缓冲区
- `getExternalActivation()`: 获取外部激活的条目

### 4. 时间效果管理器实现规范

**状态存储设计**：
```typescript
class TimedEffectsManager implements ITimedEffectsManager {
  private effectStates: Map<string, TimedEffectState[]> = new Map()
  
  constructor(
    private sessionId: string,
    private sessionService: SessionService
  ) {}
  
  async loadEffectStates(): Promise<void> {
    // 从 session metadata 加载时间效果状态
    const metadata = await this.sessionService.getMetadata(this.sessionId)
    this.effectStates = this.deserializeEffectStates(metadata.timedEffects)
  }
  
  async saveEffectStates(): Promise<void> {
    // 保存时间效果状态到 session metadata
    const serialized = this.serializeEffectStates(this.effectStates)
    await this.sessionService.updateMetadata(this.sessionId, {
      timedEffects: serialized
    })
  }
}
```

**效果检查逻辑**：
- **Sticky 检查**：检查粘性效果是否仍在持续期内
- **Cooldown 检查**：检查冷却效果是否已过期
- **Delay 检查**：检查延迟效果是否已到激活时间

## 🔗 系统集成实现规范

### 1. 消息处理集成

**集成点选择**：
在 `ChatService.processMessagesPreset` 方法中集成世界书激活

**集成方式**：
```typescript
// src/services/chat.ts
private async processMessagesPreset(params: ProcessMessagesParams): Promise<OpenAIChatMessage[]> {
  // 现有预设处理逻辑
  let processedMessages = await this.handleExistingPresets(params)
  
  // 世界书激活处理
  if (params.sessionId && await this.shouldActivateWorldbook(params.sessionId)) {
    processedMessages = await this.processWorldbookActivation(
      processedMessages,
      params.sessionId
    )
  }
  
  return processedMessages
}

private async processWorldbookActivation(
  messages: OpenAIChatMessage[],
  sessionId: string
): Promise<OpenAIChatMessage[]> {
  try {
    const activationEngine = new WorldbookActivationEngine(
      this.db,
      this.userId,
      this.worldbookConfig
    )
    
    const activationResult = await activationEngine.activateWorldbooks({
      messages,
      sessionId,
      maxTokens: this.getTokenBudget()
    })
    
    if (activationResult.formattedContent) {
      return this.injectWorldbookContent(messages, activationResult.formattedContent)
    }
    
    return messages
  } catch (error) {
    // 错误处理：记录日志但不影响正常流程
    console.error('Worldbook activation failed:', error)
    return messages
  }
}
```

### 2. RAG 系统集成

**并行处理实现**：
```typescript
// 在 generateAIChat 中实现并行处理
const [ragResult, worldbookResult] = await Promise.allSettled([
  // 现有 RAG 处理
  params?.ragQuery ? this.processRAGQuery(params) : null,
  
  // 世界书激活处理
  this.shouldUseWorldbook() ? this.processWorldbookActivation(params) : null
])

// 结果合并
const combinedContent = this.combineRAGAndWorldbook(
  ragResult.status === 'fulfilled' ? ragResult.value : null,
  worldbookResult.status === 'fulfilled' ? worldbookResult.value : null
)
```

**内容格式化统一**：
```typescript
// src/services/worldbook/integration/formatters.ts
export const worldbookQAPrompts = (activationResult: ActivationResult): string => {
  if (!activationResult.entries.length) return ''
  
  // 参考 knowledgeBaseQAPrompts 的格式
  return `<worldbook_context>
<worldbook_context_docstring>
You have access to relevant worldbook information. Use this information to enhance your responses when relevant.
</worldbook_context_docstring>

${formatWorldbookEntries(activationResult.entries)}
</worldbook_context>`
}
```

### 3. 数据库集成

**查询优化**：
```typescript
// 批量查询世界书条目
async getWorldbookEntries(sessionId: string): Promise<WorldbookEntry[]> {
  // 一次性查询所有相关数据，减少数据库往返
  const query = this.db
    .select({
      // 选择所有必要字段
      id: worldbookChunks.id,
      title: worldbookChunks.title,
      content: chunks.text,
      // ... 其他字段
    })
    .from(worldbookChunks)
    .leftJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
    .leftJoin(agentsWorldbooks, eq(worldbookChunks.worldbookId, agentsWorldbooks.worldbookId))
    .leftJoin(sessions, eq(agentsWorldbooks.agentId, sessions.agentId))
    .where(
      and(
        eq(sessions.id, sessionId),
        eq(worldbookChunks.enabled, true),
        eq(worldbookChunks.userId, this.userId)
      )
    )
    .orderBy(desc(worldbookChunks.order))
  
  return await query
}
```

**缓存策略**：
```typescript
// 实现多级缓存
class WorldbookCache {
  private entryCache = new Map<string, WorldbookEntry[]>()
  private activationCache = new Map<string, ActivationResult>()
  
  async getEntries(sessionId: string): Promise<WorldbookEntry[]> {
    // L1: 内存缓存
    if (this.entryCache.has(sessionId)) {
      return this.entryCache.get(sessionId)!
    }
    
    // L2: 数据库查询
    const entries = await this.queryDatabase(sessionId)
    this.entryCache.set(sessionId, entries)
    
    return entries
  }
}
```

## 🧪 测试实现规范

### 1. 单元测试结构

**测试文件组织**：
```
src/services/worldbook/__tests__/
├── activation/
│   ├── engine.test.ts
│   ├── activators/
│   │   ├── constant.test.ts
│   │   ├── keyword.test.ts
│   │   └── vectorized.test.ts
│   └── processors/
│       ├── scanBuffer.test.ts
│       ├── timedEffects.test.ts
│       └── recursionScanner.test.ts
├── integration/
│   ├── ragIntegration.test.ts
│   └── messageIntegration.test.ts
└── __fixtures__/
    ├── testWorldbooks.ts
    ├── testMessages.ts
    └── testExpectations.ts
```

**测试数据管理**：
```typescript
// src/services/worldbook/__tests__/__fixtures__/testWorldbooks.ts
export const TEST_WORLDBOOKS = {
  basic: {
    name: 'Basic Test Worldbook',
    entries: [
      {
        id: 'test-entry-1',
        title: 'Test Character',
        keys: ['Alice', 'protagonist'],
        content: 'Alice is the main character...',
        activationMode: ActivationMode.Keyword,
        // ... 其他字段
      }
    ]
  },
  
  complex: {
    name: 'Complex Test Worldbook',
    entries: [
      // 包含各种复杂场景的测试条目
    ]
  }
}
```

### 2. 集成测试策略

**端到端测试**：
```typescript
describe('Worldbook End-to-End Integration', () => {
  it('should activate worldbook entries and inject into conversation', async () => {
    // 设置测试环境
    const testSession = await createTestSession()
    const testWorldbook = await createTestWorldbook()
    
    // 执行完整流程
    const result = await chatService.processMessage({
      content: 'Tell me about Alice',
      sessionId: testSession.id
    })
    
    // 验证结果
    expect(result.messages).toContainWorldbookContent()
    expect(result.activatedEntries).toHaveLength(1)
    expect(result.activatedEntries[0].title).toBe('Test Character')
  })
})
```

**性能测试**：
```typescript
describe('Worldbook Performance Tests', () => {
  it('should handle large worldbooks within time limits', async () => {
    const largeWorldbook = createLargeTestWorldbook(1000) // 1000 条目
    
    const startTime = Date.now()
    const result = await activationEngine.activateWorldbooks({
      messages: testMessages,
      sessionId: testSession.id
    })
    const endTime = Date.now()
    
    expect(endTime - startTime).toBeLessThan(500) // 500ms 限制
    expect(result.entries.length).toBeGreaterThan(0)
  })
})
```

## 📊 监控实现规范

### 1. 性能监控

**指标收集**：
```typescript
class WorldbookMetrics {
  private static instance: WorldbookMetrics
  
  recordActivationTime(duration: number, entryCount: number): void {
    // 记录激活耗时
    this.histogram('worldbook_activation_duration', duration, {
      entry_count: entryCount.toString()
    })
  }
  
  recordActivationSuccess(mode: ActivationMode, count: number): void {
    // 记录激活成功数
    this.counter('worldbook_activations_total', count, {
      mode: mode,
      status: 'success'
    })
  }
  
  recordError(error: WorldbookError): void {
    // 记录错误
    this.counter('worldbook_errors_total', 1, {
      type: error.type,
      recoverable: error.recoverable.toString()
    })
  }
}
```

### 2. 日志记录

**结构化日志**：
```typescript
class WorldbookLogger {
  logActivationStart(params: ActivationParams): void {
    logger.info('Worldbook activation started', {
      sessionId: params.sessionId,
      messageCount: params.messages.length,
      timestamp: new Date().toISOString()
    })
  }
  
  logActivationResult(result: ActivationResult): void {
    logger.info('Worldbook activation completed', {
      activatedCount: result.entries.length,
      totalTime: result.timingInfo.totalTime,
      activationsByMode: result.activationStats.activationsByMode
    })
  }
  
  logError(error: WorldbookError, context: Record<string, any>): void {
    logger.error('Worldbook activation error', {
      error: error.message,
      type: error.type,
      context,
      stack: error.stack
    })
  }
}
```

**通过这些详细的技术规范，开发团队可以确保实现的世界书激活系统完全符合 Lobe-Chat 的项目标准，同时提供与 SillyTavern 完全对等的功能特性。**
