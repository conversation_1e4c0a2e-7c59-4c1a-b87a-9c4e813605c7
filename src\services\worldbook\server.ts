/* eslint-disable @typescript-eslint/no-unused-vars */
import { lambdaClient } from '@/libs/trpc/client';

import { IWorldbookService } from './type';

export class ServerService implements IWorldbookService {
  // ====== Worldbook Operations ======

  getWorldbooks: IWorldbookService['getWorldbooks'] = async () => {
    return lambdaClient.worldbook.getWorldbooks.query();
  };

  getWorldbook: IWorldbookService['getWorldbook'] = async (id) => {
    return lambdaClient.worldbook.getWorldbookById.query({ id });
  };

  createWorldbook: IWorldbookService['createWorldbook'] = async (data) => {
    return lambdaClient.worldbook.createWorldbook.mutate(data);
  };

  updateWorldbook: IWorldbookService['updateWorldbook'] = async (id, data) => {
    return lambdaClient.worldbook.updateWorldbook.mutate({ data, id });
  };

  deleteWorldbook: IWorldbookService['deleteWorldbook'] = async (id) => {
    await lambdaClient.worldbook.deleteWorldbook.mutate({ id });
  };

  deleteAllWorldbooks: IWorldbookService['deleteAllWorldbooks'] = async () => {
    const worldbooks = await this.getWorldbooks();
    const ids = worldbooks.map(wb => wb.id);
    if (ids.length > 0) {
      await lambdaClient.worldbook.bulkDeleteWorldbooks.mutate({ ids });
    }
  };

  bulkDeleteWorldbooks: IWorldbookService['bulkDeleteWorldbooks'] = async (ids) => {
    return lambdaClient.worldbook.bulkDeleteWorldbooks.mutate({ ids });
  };

  getWorldbookStats: IWorldbookService['getWorldbookStats'] = async () => {
    return lambdaClient.worldbook.getWorldbookStats.query();
  };

  importWorldbook: IWorldbookService['importWorldbook'] = async (data) => {
    return lambdaClient.worldbook.importWorldbook.mutate(data);
  };

  // ====== Entry Operations ======

  getEntry: IWorldbookService['getEntry'] = async (id) => {
    return lambdaClient.worldbookChunk.getEntryById.query({ id });
  };

  getEntriesByWorldbookId: IWorldbookService['getEntriesByWorldbookId'] = async (worldbookId) => {
    const result = await lambdaClient.worldbookChunk.getEntriesByWorldbookId.query({ worldbookId });
    return result;
  };

  searchEntries: IWorldbookService['searchEntries'] = async (worldbookId, params) => {
    return lambdaClient.worldbookChunk.searchEntries.query({ params: params || {}, worldbookId });
  };

  createEntry: IWorldbookService['createEntry'] = async (worldbookId, data) => {
    return lambdaClient.worldbookChunk.createEntry.mutate({ data, worldbookId });
  };

  updateEntry: IWorldbookService['updateEntry'] = async (id, data) => {
    return lambdaClient.worldbookChunk.updateEntry.mutate({ data, id });
  };

  deleteEntry: IWorldbookService['deleteEntry'] = async (id) => {
    await lambdaClient.worldbookChunk.deleteEntry.mutate({ id });
  };

  bulkCreateEntries: IWorldbookService['bulkCreateEntries'] = async (worldbookId, entries) => {
    const result = await lambdaClient.worldbookChunk.bulkCreateEntries.mutate({ entries, worldbookId });
    return result.data || [];
  };

  bulkUpdateEntries: IWorldbookService['bulkUpdateEntries'] = async (ids, data) => {
    return lambdaClient.worldbookChunk.bulkUpdateEntries.mutate({ data, ids });
  };

  bulkDeleteEntries: IWorldbookService['bulkDeleteEntries'] = async (ids) => {
    return lambdaClient.worldbookChunk.bulkDeleteEntries.mutate({ ids });
  };

  bulkToggleEntries: IWorldbookService['bulkToggleEntries'] = async (ids, enabled) => {
    return lambdaClient.worldbookChunk.bulkToggleEntries.mutate({ enabled, ids });
  };

  getActivationStatus: IWorldbookService['getActivationStatus'] = async (_worldbookId) => {
    // 预留接口，暂时返回默认状态
    return {
      activatedEntries: [],
      isActive: false,
      lastActivation: null,
      totalTokens: 0,
    };
  };
}
