# Lobe-Chat 世界书激活注入开发方案

## 📋 项目概述

### 目标
在 Lobe-Chat 中实现完整的世界书激活注入功能，支持 SillyTavern 的所有核心特性，包括三种激活模式、时间效果系统、递归扫描机制等，与现有 RAG 系统无缝集成。

### 设计原则
- **最小化架构变更**：复用现有 RAG 流程和消息处理机制
- **模块化设计**：清晰的职责分离，便于维护和扩展
- **渐进式实现**：分阶段交付，每个阶段都有独立价值
- **性能优先**：缓存机制、批量处理、异步优化
- **类型安全**：完整的 TypeScript 类型定义

## 🏗️ 整体架构设计

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    用户消息输入                              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              消息预处理 (processMessagesPreset)              │
│  • 占位符变量解析                                           │
│  • 现有预设处理                                             │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────▼─────────────┐
        │      并行内容检索          │
        │                          │
┌───────▼────────┐        ┌────────▼────────┐
│   RAG 检索      │        │  世界书激活      │
│  (现有流程)     │        │   (新增功能)     │
│                │        │                │
│ • 语义搜索      │        │ • 三种激活模式   │
│ • 知识库检索    │        │ • 时间效果处理   │
│ • 结果格式化    │        │ • 递归扫描      │
└───────┬────────┘        └────────┬────────┘
        │                          │
        └─────────────┬─────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  内容融合与注入                              │
│  • RAG 内容 + 世界书内容                                     │
│  • 位置插入处理                                             │
│  • 格式化输出                                               │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 发送给 LLM                                  │
└─────────────────────────────────────────────────────────────┘
```

### 核心模块设计

#### 1. 世界书激活引擎 (WorldbookActivationEngine)
**职责**：统一管理三种激活模式，协调各个子系统
**位置**：`src/services/worldbook/activation/engine.ts`

#### 2. 激活模式处理器
- **常驻激活器** (ConstantActivator)：处理 constant 模式
- **关键词激活器** (KeywordActivator)：处理关键词匹配
- **向量化激活器** (VectorizedActivator)：处理语义搜索

#### 3. 支撑系统
- **扫描缓冲区** (ScanBuffer)：管理扫描文本和上下文
- **时间效果管理器** (TimedEffectsManager)：处理 sticky/cooldown/delay
- **递归扫描器** (RecursionScanner)：处理递归激活逻辑
- **位置插入器** (PositionInserter)：处理内容插入位置

## 🔄 核心流程设计

### 主激活流程

```
开始激活
    ↓
1. 初始化扫描环境
   • 创建扫描缓冲区
   • 加载时间效果状态
   • 获取世界书条目
    ↓
2. 多轮扫描循环
   ┌─→ 当前轮次激活检查
   │   ├── 常驻激活检查
   │   ├── 关键词匹配检查  
   │   ├── 向量化搜索检查
   │   └── 外部激活检查
   │    ↓
   │  应用过滤器
   │   ├── 基础状态过滤 (enabled/disabled)
   │   ├── 角色过滤 (characterFilter)
   │   ├── 时间效果过滤 (sticky/cooldown/delay)
   │   ├── 递归控制过滤 (excludeRecursion)
   │   └── 装饰器过滤 (@@activate/@@dont_activate)
   │    ↓
   │  后处理阶段
   │   ├── 分组过滤 (groupName/groupWeight)
   │   ├── 概率检查 (probability)
   │   ├── 预算控制 (token budget)
   │   └── 递归准备
   │    ↓
   │  确定下一轮扫描状态
   │   ├── 需要递归？ → 递归扫描
   │   ├── 激活数不足？ → 最小激活扫描
   │   └── 完成条件？ → 结束扫描
   └─── 继续下一轮 ←─────┘
    ↓
3. 构建激活结果
   • 按位置分组激活条目
   • 格式化内容输出
   • 设置时间效果
   • 清理临时状态
    ↓
返回激活结果
```

### 三种激活模式详细设计

#### 常驻激活模式 (Constant)
**触发条件**：`activationMode === 'constant'`
**处理逻辑**：
- 直接标记为激活，跳过关键词检查
- 仍需通过所有过滤器检查
- 优先级最高，最先处理

#### 关键词激活模式 (Keyword)  
**触发条件**：`activationMode === 'keyword'`
**处理逻辑**：
- 主关键词匹配：`keys` 数组中任一关键词匹配即可
- 次关键词逻辑：根据 `selectiveLogic` 处理 `keysSecondary`
- 匹配选项：支持 `caseSensitive`、`matchWholeWords`、`useRegex`
- 匹配源控制：根据 `matchSources` 选择扫描内容

#### 向量化激活模式 (Vectorized)
**触发条件**：`activationMode === 'vectorized'`
**处理逻辑**：
- 复用现有 `semanticSearchForChat` 能力
- 按世界书分组执行语义搜索
- 相似度阈值过滤
- 通过外部激活机制注入主流程

## 📦 模块详细设计

### 1. 世界书激活引擎 (WorldbookActivationEngine)

**核心方法**：
- `activateWorldbooks(messages, sessionId)`: 主激活入口
- `executeActivationCycle()`: 执行激活循环
- `processActivationResults()`: 处理激活结果

**设计要点**：
- 统一管理三种激活模式
- 协调各个子系统的工作
- 维护激活状态和扫描状态
- 处理错误和异常情况

### 2. 扫描缓冲区 (ScanBuffer)

**核心功能**：
- 构建扫描文本：根据 `matchSources` 组合不同来源的内容
- 深度管理：支持 `scanDepth` 控制扫描范围
- 递归内容管理：维护递归激活的内容缓冲区
- 外部激活管理：处理向量化搜索的激活结果

**数据结构**：
- `depthBuffer`: 按深度存储的消息内容
- `recurseBuffer`: 递归激活的内容
- `globalScanData`: 全局扫描数据（角色描述、场景等）
- `externalActivations`: 外部激活的条目映射

### 3. 时间效果管理器 (TimedEffectsManager)

**三种时间效果**：
- **Sticky 粘性效果**：条目持续激活指定轮数
- **Cooldown 冷却效果**：条目在冷却期内不激活
- **Delay 延迟效果**：条目延迟指定轮数后激活

**存储机制**：
- 使用 Session metadata 存储时间效果状态
- 支持效果的持久化和恢复
- 自动清理过期的时间效果

### 4. 递归扫描器 (RecursionScanner)

**递归控制字段**：
- `excludeRecursion`: 排除递归的条目
- `preventRecursion`: 阻止递归的条目  
- `delayUntilRecursion`: 延迟到递归的条目

**递归逻辑**：
- 将激活条目的内容加入递归缓冲区
- 支持多级递归和递归深度控制
- 防止递归循环和无限递归

### 5. 位置插入器 (PositionInserter)

**七种插入位置**：
- `before`: 消息前插入
- `after`: 消息后插入  
- `author_note_top`: 作者注释顶部
- `author_note_bottom`: 作者注释底部
- `at_depth`: 指定深度插入
- `example_message_top`: 示例消息顶部
- `example_message_bottom`: 示例消息底部

**处理逻辑**：
- 按位置分组激活的条目
- 支持深度和角色类型控制
- 按优先级排序和组织内容

## 🔗 与现有系统集成

### RAG 系统集成策略

**并行处理模式**：
```
消息输入
    ↓
并行执行:
├── RAG 检索 (现有)
│   ├── 语义搜索
│   ├── 知识库检索  
│   └── 结果格式化
│
└── 世界书激活 (新增)
    ├── 常驻激活
    ├── 关键词匹配
    ├── 向量化搜索
    └── 结果格式化
    ↓
内容融合
├── RAG 内容 (knowledgeBaseQAPrompts)
├── 世界书内容 (worldbookQAPrompts)  
└── 合并策略
    ↓
注入到消息
```

**集成点设计**：
- 在 `processMessagesPreset` 中添加世界书激活调用
- 复用现有的消息格式化和注入机制
- 保持与 RAG 系统的独立性，避免相互影响

### 数据库集成

**复用现有表结构**：
- `worldbookChunks`: 世界书条目数据
- `chunks`: 条目内容存储
- `embeddings`: 向量化数据
- `sessions`: 会话元数据（存储时间效果状态）

**新增查询方法**：
- 扩展 `WorldbookChunkModel` 支持激活查询
- 扩展 `ChunkModel` 支持世界书语义搜索
- 添加时间效果状态的存储和查询

## 📊 性能优化策略

### 缓存机制
- **条目缓存**：缓存世界书条目数据，减少数据库查询
- **匹配缓存**：缓存关键词匹配结果，避免重复计算
- **向量缓存**：缓存语义搜索结果，提高响应速度

### 批量处理
- **批量查询**：一次性获取所有相关的世界书条目
- **批量匹配**：并行处理多个条目的关键词匹配
- **批量向量搜索**：合并多个世界书的语义搜索请求

### 异步优化
- **并行激活**：三种激活模式并行执行
- **异步I/O**：数据库查询和向量搜索异步处理
- **流式处理**：大量条目的流式处理，避免内存溢出

## 🧪 测试策略

### 单元测试覆盖
- **激活模式测试**：每种激活模式的独立测试
- **过滤器测试**：各种过滤条件的正确性测试
- **时间效果测试**：时间效果的状态管理测试
- **递归逻辑测试**：递归扫描的正确性测试

### 集成测试覆盖
- **端到端流程测试**：完整的激活到注入流程
- **RAG 集成测试**：与现有 RAG 系统的兼容性
- **性能测试**：大量条目的处理性能
- **并发测试**：多用户并发使用的稳定性

### 兼容性测试
- **SillyTavern 对比测试**：与 SillyTavern 行为的一致性
- **导入导出测试**：世界书格式的兼容性
- **边界情况测试**：异常输入和错误处理

## 📈 监控和调试

### 性能监控
- **激活耗时统计**：各个阶段的处理时间
- **内存使用监控**：缓存和临时数据的内存占用
- **数据库查询监控**：查询次数和响应时间
- **错误率监控**：激活失败和异常的统计

### 调试支持
- **激活日志**：详细的激活过程日志
- **状态检查**：扫描状态和时间效果状态的查看
- **性能分析**：性能瓶颈的识别和分析
- **错误追踪**：异常的堆栈信息和上下文

## 🚀 实施计划

### 阶段划分
1. **基础激活引擎**：核心架构和关键词匹配
2. **时间效果系统**：sticky/cooldown/delay 功能
3. **向量化集成**：语义搜索和 RAG 集成
4. **高级功能**：递归扫描和位置插入
5. **优化完善**：性能优化和错误处理

### 交付里程碑
- **Alpha 版本**：基础功能可用，内部测试
- **Beta 版本**：完整功能实现，公开测试
- **正式版本**：性能优化完成，生产部署

## 📁 文件结构设计

### 目录组织
```
src/services/worldbook/
├── activation/
│   ├── engine.ts                 # 主激活引擎
│   ├── activators/
│   │   ├── constant.ts           # 常驻激活器
│   │   ├── keyword.ts            # 关键词激活器
│   │   └── vectorized.ts         # 向量化激活器
│   ├── processors/
│   │   ├── scanBuffer.ts         # 扫描缓冲区
│   │   ├── timedEffects.ts       # 时间效果管理器
│   │   ├── recursionScanner.ts   # 递归扫描器
│   │   └── positionInserter.ts   # 位置插入器
│   ├── filters/
│   │   ├── baseFilter.ts         # 基础过滤器
│   │   ├── characterFilter.ts    # 角色过滤器
│   │   ├── groupFilter.ts        # 分组过滤器
│   │   └── probabilityFilter.ts  # 概率过滤器
│   └── types/
│       ├── activation.ts         # 激活相关类型
│       ├── scanning.ts           # 扫描相关类型
│       └── effects.ts            # 效果相关类型
├── integration/
│   ├── ragIntegration.ts         # RAG 系统集成
│   ├── messageIntegration.ts     # 消息系统集成
│   └── formatters.ts             # 内容格式化器
└── utils/
    ├── constants.ts              # 常量定义
    ├── helpers.ts                # 辅助函数
    └── validators.ts             # 验证函数
```

## 🔧 核心接口设计

### 主要接口定义

#### 激活引擎接口
```typescript
interface IWorldbookActivationEngine {
  activateWorldbooks(params: ActivationParams): Promise<ActivationResult>
  shouldActivate(sessionId: string): Promise<boolean>
  getActivationStatus(sessionId: string): Promise<ActivationStatus>
}

interface ActivationParams {
  messages: OpenAIChatMessage[]
  sessionId: string
  maxTokens?: number
  dryRun?: boolean
}

interface ActivationResult {
  entries: ActivatedEntry[]
  formattedContent: string
  activationStats: ActivationStats
  timingInfo: TimingInfo
}
```

#### 激活器接口
```typescript
interface IActivator {
  canActivate(entry: WorldbookEntry, context: ActivationContext): boolean
  activate(entry: WorldbookEntry, context: ActivationContext): Promise<ActivationDecision>
  getActivatorType(): ActivatorType
}

interface ActivationContext {
  scanBuffer: IScanBuffer
  timedEffects: ITimedEffectsManager
  scanState: ScanState
  activatedEntries: Map<string, ActivatedEntry>
}
```

#### 过滤器接口
```typescript
interface IFilter {
  canPass(entry: WorldbookEntry, context: FilterContext): boolean
  getFilterType(): FilterType
  getFilterPriority(): number
}

interface FilterContext {
  scanBuffer: IScanBuffer
  timedEffects: ITimedEffectsManager
  sessionMetadata: SessionMetadata
}
```

## 🎛️ 配置管理设计

### 全局配置
```typescript
interface WorldbookConfig {
  // 扫描控制
  defaultScanDepth: number
  maxRecursionSteps: number
  enableRecursion: boolean

  // 激活控制
  minActivations: number
  maxActivations: number

  // 性能控制
  tokenBudgetPercent: number
  tokenBudgetCap: number
  enableCaching: boolean
  cacheTimeout: number

  // 向量化搜索
  semanticThreshold: number
  maxSemanticResults: number

  // 调试选项
  enableDebugLogging: boolean
  enablePerformanceMonitoring: boolean
}
```

### 用户级配置
```typescript
interface UserWorldbookSettings {
  enableWorldbook: boolean
  preferredActivationMode: ActivationMode[]
  customScanDepth?: number
  enableTimeEffects: boolean
  debugMode: boolean
}
```

## 🔄 状态管理设计

### 扫描状态枚举
```typescript
enum ScanState {
  INITIAL = 'initial',           // 初始扫描
  RECURSION = 'recursion',       // 递归扫描
  MIN_ACTIVATIONS = 'min_activations', // 最小激活扫描
  COMPLETED = 'completed'        // 扫描完成
}
```

### 时间效果状态
```typescript
interface TimedEffectState {
  entryId: string
  effectType: TimedEffectType
  startTurn: number
  endTurn: number
  isActive: boolean
  metadata?: Record<string, any>
}

enum TimedEffectType {
  STICKY = 'sticky',
  COOLDOWN = 'cooldown',
  DELAY = 'delay'
}
```

### 激活统计
```typescript
interface ActivationStats {
  totalEntries: number
  activatedEntries: number
  activationsByMode: Record<ActivationMode, number>
  filterStats: Record<FilterType, number>
  timingStats: {
    totalTime: number
    activationTime: number
    filteringTime: number
    formattingTime: number
  }
}
```

## 🔍 关键算法设计

### 关键词匹配算法
**设计要点**：
- 支持多种匹配模式：普通文本、正则表达式、全词匹配
- 大小写敏感控制
- 性能优化：预编译正则表达式、字符串预处理

**匹配流程**：
1. 文本预处理：根据大小写敏感设置转换
2. 匹配模式选择：正则表达式 vs 普通文本
3. 全词匹配处理：添加词边界检查
4. 结果验证：确保匹配的有效性

### 次关键词逻辑算法
**四种逻辑模式**：
- `AND_ANY`: 任一次关键词匹配即可
- `NOT_ALL`: 非全部次关键词匹配
- `NOT_ANY`: 全部次关键词不匹配
- `AND_ALL`: 全部次关键词匹配

**实现策略**：
- 并行检查所有次关键词
- 短路求值优化
- 结果缓存机制

### 语义搜索集成算法
**集成策略**：
- 复用现有 `semanticSearchForChat` 方法
- 按世界书分组执行搜索
- 相似度阈值过滤
- 结果排序和去重

**优化措施**：
- 批量向量查询
- 查询结果缓存
- 异步并行处理

## 📋 错误处理设计

### 错误分类
```typescript
enum WorldbookErrorType {
  ACTIVATION_FAILED = 'activation_failed',
  FILTER_ERROR = 'filter_error',
  TIMEOUT_ERROR = 'timeout_error',
  MEMORY_ERROR = 'memory_error',
  DATABASE_ERROR = 'database_error',
  VALIDATION_ERROR = 'validation_error'
}

interface WorldbookError extends Error {
  type: WorldbookErrorType
  context: Record<string, any>
  recoverable: boolean
}
```

### 错误恢复策略
- **激活失败**：降级到基础模式，记录错误日志
- **过滤器错误**：跳过有问题的过滤器，继续处理
- **超时错误**：返回部分结果，设置超时警告
- **内存错误**：清理缓存，减少处理批次
- **数据库错误**：使用缓存数据，异步重试

### 降级机制
```typescript
interface FallbackStrategy {
  onActivationFailed(): ActivationResult
  onFilterFailed(filter: IFilter): boolean
  onTimeoutReached(): ActivationResult
  onMemoryLimitReached(): void
}
```

## 🎯 质量保证设计

### 代码质量标准
- **TypeScript 严格模式**：启用所有严格类型检查
- **ESLint 规则**：遵循 Lobe-Chat 项目的 ESLint 配置
- **代码覆盖率**：单元测试覆盖率 > 90%
- **性能基准**：响应时间和内存使用的基准测试

### 测试数据设计
```typescript
interface TestWorldbook {
  name: string
  entries: TestWorldbookEntry[]
  expectedActivations: ExpectedActivation[]
  performanceBenchmark: PerformanceBenchmark
}

interface TestWorldbookEntry extends WorldbookEntry {
  testMetadata: {
    shouldActivate: boolean
    activationReason: string
    expectedPosition: WorldbookPosition
  }
}
```

### 性能基准
```typescript
interface PerformanceBenchmark {
  maxActivationTime: number    // 最大激活时间 (ms)
  maxMemoryUsage: number      // 最大内存使用 (MB)
  maxDatabaseQueries: number  // 最大数据库查询次数
  targetThroughput: number    // 目标吞吐量 (requests/sec)
}
```

## 📊 监控指标设计

### 业务指标
- **激活成功率**：成功激活的条目比例
- **激活准确率**：正确激活的条目比例
- **用户满意度**：基于用户反馈的满意度评分

### 技术指标
- **响应时间分布**：P50, P90, P95, P99 响应时间
- **错误率**：各类错误的发生频率
- **资源使用率**：CPU、内存、数据库连接使用情况
- **缓存命中率**：各级缓存的命中率统计

### 告警规则
```typescript
interface AlertRule {
  metric: string
  threshold: number
  duration: number
  severity: AlertSeverity
  action: AlertAction
}

enum AlertSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}
```

**通过这个全面的设计方案，我们可以在严格遵循 Lobe-Chat 项目规范的前提下，实现一个功能完整、性能优秀、可维护性强的世界书激活系统，完全覆盖 SillyTavern 的所有功能特性。**
