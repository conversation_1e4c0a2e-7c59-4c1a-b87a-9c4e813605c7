# 标签提取功能使用指南 (v2.0)

## 功能概述

增强版vectors扩展现在拥有一个全新的、基于规则的标签提取系统。您不再需要编写复杂的命令，而是通过一个直观的UI来精确地从聊天消息中提取或排除特定内容进行向量化。

## 核心概念：规则 (Rules)

所有操作都围绕着“规则”进行。您可以添加任意多条规则，系统会智能地处理它们。主要有三种规则类型：

1.  **包含 (Include):** 提取匹配此标签的内容。
2.  **排除 (Exclude):** 从文本中**预先移除**匹配此标签的内容。
3.  **正则包含 (Regex Include):** 使用正则表达式进行高级提取。系统会提取正则表达式中第一个**捕获组 `(...)`** 匹配到的内容。

## 处理顺序 (重要！)

系统严格遵循一个固定的、健壮的处理流程，与您在UI中设置的规则顺序无关：

1.  **全局排除:** 首先，应用所有“排除”规则，从原始文本中移除所有不想要的内容。
2.  **全局提取:** 然后，在“净化后”的文本上，应用所有“包含”和“正则包含”规则来提取您需要的内容。
    *   **注意:** 如果您没有设置任何“包含”或“正则包含”规则，系统将默认提取所有经过排除步骤后剩余的全部文本。
3.  **黑名单过滤:** 最后，对提取出的内容进行黑名单检查。

---

## 用法示例

以下是常见配置如何用新系统实现：

### 场景1: 提取简单标签

**目标:** 提取 `<content>` 和 `<thinking>` 标签的内容。

**操作方法:** 添加 **两条** `包含` 规则。

*   **规则一:** 类型: `包含`, 值: `content`
*   **规则二:** 类型: `包含`, 值: `thinking`

### 场景2: 提取复杂条件内容

**目标:** 只提取 `<summary>` 为“摘要”的 `<details>` 块中的内容。

**操作方法:** 添加 **一条** `正则包含` 规则。

*   **类型:** `正则包含`
*   **值:** `<details><summary>摘要<\/summary>([\s\S]*?)<\/details>`

*(原理: 正则表达式会匹配整个结构，但系统只提取由圆括号 `(...)` 捕获的内容。)*

### 场景3: 嵌套排除

**目标:** 提取 `<content>` 的内容，但**不包括**其中的 `<thinking>` 和 `<analysis>` 部分。

**操作方法:** 添加 **三条** 规则。

*   **规则一 (排除):** 类型: `排除`, 值: `thinking`
*   **规则二 (排除):** 类型: `排除`, 值: `analysis`
*   **规则三 (提取):** 类型: `包含`, 值: `content`

*(工作流程: 系统会先从原始文本中移除所有 `<thinking>` 和 `<analysis>` 块，然后在剩下的“干净”文本中提取 `<content>` 的内容。)*

### 场景4: 复杂提取与排除结合

**目标:** 提取 `<summary>` 为“摘要”的 `<details>` 块，但排除其中的 `<note>` 和 `<思考>` 部分。

**操作方法:** 添加 **三条** 规则。

*   **规则一 (排除):** 类型: `排除`, 值: `note`
*   **规则二 (排除):** 类型: `排除`, 值: `思考`
*   **规则三 (提取):** 类型: `正则包含`, 值: `<details><summary>摘要<\/summary>([\s\S]*?)<\/details>`

---

## 注意事项

1.  **顺序无关:** 您在UI中添加规则的顺序**不影响**执行结果。系统总是遵循“先排除，后提取”的原则。
2.  **捕获组是关键:** 对于 `正则包含` 规则，请确保您想要提取的部分被包裹在**第一个圆括号捕获组 `(...)`** 中。
3.  **内容保留:** 提取的内容会保留其内部的HTML标签结构。
4.  **黑名单:** 黑名单过滤总是在所有标签提取/排除操作**之后**进行。
