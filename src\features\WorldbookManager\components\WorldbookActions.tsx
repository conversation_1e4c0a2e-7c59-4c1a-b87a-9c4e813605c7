'use client';

import { Button } from '@lobehub/ui';
import { createStyles } from 'antd-style';
import { Download, Plus, Settings } from 'lucide-react';
import React, { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

// Hook模式已移除，改为通过props接收回调函数

const useStyles = createStyles(({ css, token }) => ({
  actionButton: css`
    flex: 1;

    .ant-btn {
      width: 100%;
      height: 36px;
      font-size: 13px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px ${token.colorPrimaryBg};
      }

      .anticon {
        font-size: 14px;
      }
    }
  `,
  buttonText: css`
    font-size: 12px;
    font-weight: 500;
  `,
  container: css`
    padding: 12px 0;
    border-bottom: 1px solid ${token.colorBorderSecondary};
    margin-bottom: 16px;
  `,
}));

interface WorldbookActionsProps {
  onCreateWorldbook?: () => void;
  onImport?: () => void;
  onSettings?: () => void;
}

const WorldbookActions = memo<WorldbookActionsProps>(({ onCreateWorldbook, onImport, onSettings }) => {
  const { styles } = useStyles();

  const handleCreate = () => {
    onCreateWorldbook?.();
  };

  const handleImport = () => {
    onImport?.();
  };

  const handleSettings = () => {
    onSettings?.();
  };

  return (
    <div className={styles.container}>
      <Flexbox gap={8} horizontal>
        <div className={styles.actionButton}>
          <Button
            icon={Plus}
            onClick={handleCreate}
            size="small"
            title="创建世界书"
            type="primary"
          >
            <span className={styles.buttonText}>创建</span>
          </Button>
        </div>
        <div className={styles.actionButton}>
          <Button
            icon={Download}
            onClick={handleImport}
            size="small"
            title="导入世界书"
            variant="outline"
          >
            <span className={styles.buttonText}>导入</span>
          </Button>
        </div>
        <div className={styles.actionButton}>
          <Button
            icon={Settings}
            onClick={handleSettings}
            size="small"
            title="全局设置"
            variant="outline"
          >
            <span className={styles.buttonText}>设置</span>
          </Button>
        </div>
      </Flexbox>

      {/* Modal组件已移至页面级别统一管理 */}
    </div>
  );
});

WorldbookActions.displayName = 'WorldbookActions';

export default WorldbookActions;
