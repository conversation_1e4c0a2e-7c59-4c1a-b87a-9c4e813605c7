'use client';

import { ActionIcon } from '@lobehub/ui';
import { Dropdown } from 'antd';
import { createStyles } from 'antd-style';
import { Download, MoreVertical, Plus, Settings } from 'lucide-react';
import React, { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import { useWorldbookStore } from '@/store/worldbook';

// Hook模式已移除，改为通过props接收回调函数

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    padding-block: 24px 0;
    padding-inline: 0.75rem;
  `,
  desc: css`
    line-height: 1.4;
    color: ${token.colorTextDescription};
    margin: 0;
  `,
  header: css`
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 16px;
  `,
  menuButton: css`
    margin-top: 4px;
    flex-shrink: 0;

    .ant-btn {
      border: none;
      box-shadow: none;

      &:hover {
        background: ${token.colorFillTertiary};
      }
    }
  `,
  title: css`
    margin: 0;
    font-size: 26px;
    font-weight: 600;
    line-height: 1.3;
  `,
  titleSection: css`
    flex: 1;
    min-width: 0;
  `,
}));

interface WorldbookPanelTitleProps {
  desc?: string;
  title?: string;
  onCreateWorldbook?: () => void;
  onImportWorldbook?: () => void;
  onOpenSettings?: () => void;
}

const WorldbookPanelTitle = memo<WorldbookPanelTitleProps>(({
  title,
  desc,
  onCreateWorldbook,
  onImportWorldbook,
  onOpenSettings
}) => {
  const { styles } = useStyles();

  const handleCreate = () => {
    onCreateWorldbook?.();
  };

  const handleImport = () => {
    onImportWorldbook?.();
  };

  const handleSettings = () => {
    onOpenSettings?.();
  };

  const menuItems = [
    {
      icon: <Plus size={16} style={{ color: '#1890ff' }} />,
      key: 'create',
      label: '创建世界书',
      onClick: handleCreate,
    },
    {
      icon: <Download size={16} style={{ color: '#52c41a' }} />,
      key: 'import',
      label: '导入世界书',
      onClick: handleImport,
    },
    {
      type: 'divider' as const,
    },
    {
      icon: <Settings size={16} style={{ color: '#8c8c8c' }} />,
      key: 'settings',
      label: '全局设置',
      onClick: handleSettings,
    },
  ];

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <Flexbox gap={4}>
            <h1 className={styles.title}>{title}</h1>
            {desc && <p className={styles.desc}>{desc}</p>}
          </Flexbox>
        </div>
        
        <div className={styles.menuButton}>
          <Dropdown
            menu={{
              items: menuItems,
            }}
            placement="bottomRight"
            trigger={['click']}
          >
            <ActionIcon
              icon={MoreVertical}
              size="normal"
              title="更多操作"
            />
          </Dropdown>
        </div>
      </div>

      {/* Modal组件已移至页面级别统一管理 */}
    </div>
  );
});

WorldbookPanelTitle.displayName = 'WorldbookPanelTitle';

export default WorldbookPanelTitle;
