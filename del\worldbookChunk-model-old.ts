import { and, asc, desc, eq, gte, ilike, inArray, lte, or } from 'drizzle-orm/expressions';
import { count, sql } from 'drizzle-orm';
import { nanoid } from 'nanoid';

import { LobeChatDatabase, Transaction } from '@/database/type';

import { NewChunkItem, WorldbookChunkItem, chunks, embeddings, worldbookChunks } from '../schemas';

export interface CreateChunkData {
  worldbookId: string;
  title: string;
  content: string;
  keys: string[];
  keysSecondary?: string[];
  selectiveLogic?: number;
  order?: number;
  probability?: number;
  constant?: boolean;
  scanDepth?: number;
  position?: number;
  groupName?: string;
  sticky?: number;
  cooldown?: number;
  delay?: number;
  excludeRecursion?: boolean;
  delayUntilRecursion?: number;
  groupWeight?: number;
  caseSensitive?: boolean;
  matchWholeWords?: boolean;
  useRegex?: boolean;
}

export interface SillyTavernEntry {
  key: string[];
  keysecondary?: string[];
  content: string;
  comment?: string;
  order?: number;
  selective?: number | boolean; // 支持布尔值和数字
  selectiveLogic?: number;
  constant?: boolean;
  probability?: number;
  position?: number;
  disable?: boolean;
  excludeRecursion?: boolean;
  useProbability?: boolean;
  addMemo?: boolean;
  displayIndex?: number;
  uid?: number;
  [key: string]: any;
}

export interface SillyTavernWorldbook {
  entries: { [key: string]: SillyTavernEntry };
  [key: string]: any;
}

export interface SearchParams {
  page?: number;
  pageSize?: number;
  query?: string;
  enabled?: boolean;
  constant?: boolean;
  hasKeys?: boolean;
  hasSecondaryKeys?: boolean;
  groupName?: string;
  selectiveLogic?: string;
  orderMin?: number;
  orderMax?: number;
  probabilityMin?: number;
  probabilityMax?: number;
  sortBy?: 'name' | 'date' | 'order' | 'probability';
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export class WorldbookChunkModel {
  private readonly userId: string;
  private db: LobeChatDatabase;

  constructor(db: LobeChatDatabase, userId: string) {
    this.userId = userId;
    this.db = db;
  }

  // ====== CRUD Operations ======
  // Note: Use createWithContent method to create entries with content

  findById = async (id: string) => {
    const result = await this.db
      .select({
        id: worldbookChunks.id,
        worldbookId: worldbookChunks.worldbookId,
        chunkId: worldbookChunks.chunkId,
        title: worldbookChunks.title,
        keys: worldbookChunks.keys,
        keysSecondary: worldbookChunks.keysSecondary,
        selectiveLogic: worldbookChunks.selectiveLogic,
        order: worldbookChunks.order,
        probability: worldbookChunks.probability,
        constant: worldbookChunks.constant,
        scanDepth: worldbookChunks.scanDepth,
        position: worldbookChunks.position,
        groupName: worldbookChunks.groupName,
        sticky: worldbookChunks.sticky,
        cooldown: worldbookChunks.cooldown,
        delay: worldbookChunks.delay,
        excludeRecursion: worldbookChunks.excludeRecursion,
        delayUntilRecursion: worldbookChunks.delayUntilRecursion,
        groupWeight: worldbookChunks.groupWeight,
        caseSensitive: worldbookChunks.caseSensitive,
        matchWholeWords: worldbookChunks.matchWholeWords,
        useRegex: worldbookChunks.useRegex,
        enabled: worldbookChunks.enabled,
        userId: worldbookChunks.userId,
        createdAt: worldbookChunks.createdAt,
        updatedAt: worldbookChunks.updatedAt,
        content: chunks.text,
      })
      .from(worldbookChunks)
      .leftJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)))
      .limit(1);

    return result[0];
  };

  findByWorldbookId = async (worldbookId: string) => {
    const data = await this.db
      .select({
        id: worldbookChunks.id,
        worldbookId: worldbookChunks.worldbookId,
        chunkId: worldbookChunks.chunkId,
        title: worldbookChunks.title,
        enabled: worldbookChunks.enabled,
        keys: worldbookChunks.keys,
        keysSecondary: worldbookChunks.keysSecondary,
        selectiveLogic: worldbookChunks.selectiveLogic,
        order: worldbookChunks.order,
        constant: worldbookChunks.constant,
        probability: worldbookChunks.probability,
        scanDepth: worldbookChunks.scanDepth,
        position: worldbookChunks.position,
        groupName: worldbookChunks.groupName,
        sticky: worldbookChunks.sticky,
        cooldown: worldbookChunks.cooldown,
        delay: worldbookChunks.delay,
        excludeRecursion: worldbookChunks.excludeRecursion,
        delayUntilRecursion: worldbookChunks.delayUntilRecursion,
        groupWeight: worldbookChunks.groupWeight,
        caseSensitive: worldbookChunks.caseSensitive,
        matchWholeWords: worldbookChunks.matchWholeWords,
        useRegex: worldbookChunks.useRegex,
        createdAt: worldbookChunks.createdAt,
        updatedAt: worldbookChunks.updatedAt,
        content: chunks.text,
      })
      .from(worldbookChunks)
      .leftJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(
        and(
          eq(worldbookChunks.worldbookId, worldbookId),
          eq(worldbookChunks.userId, this.userId)
        )
      )
      .orderBy(desc(worldbookChunks.order), desc(worldbookChunks.createdAt));

    // 转换数据格式以匹配前端期望的WorldbookEntry类型
    return data.map(item => ({
      id: item.id,
      worldbookId: item.worldbookId,
      chunkId: item.chunkId,
      title: item.title,
      enabled: item.enabled,
      keys: item.keys || [],
      keysSecondary: item.keysSecondary || [],
      selectiveLogic: item.selectiveLogic || 0,
      order: item.order || 100,
      constant: item.constant || false,
      probability: item.probability || 100,
      scanDepth: item.scanDepth,
      position: item.position || 0,
      groupName: item.groupName,
      sticky: item.sticky,
      cooldown: item.cooldown,
      delay: item.delay,
      excludeRecursion: item.excludeRecursion || false,
      delayUntilRecursion: item.delayUntilRecursion || 0,
      groupWeight: item.groupWeight || 100,
      caseSensitive: item.caseSensitive,
      matchWholeWords: item.matchWholeWords,
      useRegex: item.useRegex || false,
      createdAt: item.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: item.updatedAt?.toISOString() || new Date().toISOString(),
      content: item.content || '',
    }));
  };

  findByWorldbookIdWithParams = async (worldbookId: string, params: SearchParams): Promise<PaginatedResponse<any>> => {
    const {
      page = 1,
      pageSize = 20,
      query,
      enabled,
      constant,
      hasKeys,
      hasSecondaryKeys,
      groupName,
      orderMin,
      orderMax,
      probabilityMin,
      probabilityMax,
      sortBy = 'order',
      sortOrder = 'asc'
    } = params;

    // 构建查询条件
    const conditions = [
      eq(worldbookChunks.worldbookId, worldbookId),
      eq(worldbookChunks.userId, this.userId)
    ];

    // 文本搜索
    if (query) {
      conditions.push(
        or(
          ilike(worldbookChunks.title, `%${query}%`),
          ilike(chunks.text, `%${query}%`)
        )!
      );
    }

    // 过滤条件
    if (enabled !== undefined) {
      conditions.push(eq(worldbookChunks.enabled, enabled));
    }
    if (constant !== undefined) {
      conditions.push(eq(worldbookChunks.constant, constant));
    }
    if (groupName) {
      conditions.push(ilike(worldbookChunks.groupName, `%${groupName}%`));
    }

    // 范围过滤
    if (orderMin !== undefined) {
      conditions.push(gte(worldbookChunks.order, orderMin));
    }
    if (orderMax !== undefined) {
      conditions.push(lte(worldbookChunks.order, orderMax));
    }
    if (probabilityMin !== undefined) {
      conditions.push(gte(worldbookChunks.probability, probabilityMin));
    }
    if (probabilityMax !== undefined) {
      conditions.push(lte(worldbookChunks.probability, probabilityMax));
    }

    // 键值过滤
    if (hasKeys !== undefined) {
      if (hasKeys) {
        // 有键值：keys 数组不为空
        conditions.push(sql`array_length(${worldbookChunks.keys}, 1) > 0`);
      } else {
        // 无键值：keys 数组为空或为null
        conditions.push(
          or(
            sql`array_length(${worldbookChunks.keys}, 1) IS NULL`,
            sql`array_length(${worldbookChunks.keys}, 1) = 0`
          )!
        );
      }
    }

    if (hasSecondaryKeys !== undefined) {
      if (hasSecondaryKeys) {
        // 有次要键值：keysSecondary 数组不为空
        conditions.push(sql`array_length(${worldbookChunks.keysSecondary}, 1) > 0`);
      } else {
        // 无次要键值：keysSecondary 数组为空或为null
        conditions.push(
          or(
            sql`array_length(${worldbookChunks.keysSecondary}, 1) IS NULL`,
            sql`array_length(${worldbookChunks.keysSecondary}, 1) = 0`
          )!
        );
      }
    }

    // 构建排序
    let orderByClause;
    const isAsc = sortOrder === 'asc';
    switch (sortBy) {
      case 'name':
        orderByClause = isAsc ? asc(worldbookChunks.title) : desc(worldbookChunks.title);
        break;
      case 'date':
        orderByClause = isAsc ? asc(worldbookChunks.updatedAt) : desc(worldbookChunks.updatedAt);
        break;
      case 'probability':
        orderByClause = isAsc ? asc(worldbookChunks.probability) : desc(worldbookChunks.probability);
        break;
      case 'order':
      default:
        orderByClause = isAsc ? asc(worldbookChunks.order) : desc(worldbookChunks.order);
        break;
    }

    // 获取总数
    const totalResult = await this.db
      .select({ count: count() })
      .from(worldbookChunks)
      .leftJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(and(...conditions));

    const total = totalResult[0]?.count || 0;

    // 获取分页数据
    const offset = (page - 1) * pageSize;
    const data = await this.db
      .select({
        id: worldbookChunks.id,
        worldbookId: worldbookChunks.worldbookId,
        chunkId: worldbookChunks.chunkId,
        title: worldbookChunks.title,
        enabled: worldbookChunks.enabled,
        keys: worldbookChunks.keys,
        keysSecondary: worldbookChunks.keysSecondary,
        selectiveLogic: worldbookChunks.selectiveLogic,
        order: worldbookChunks.order,
        constant: worldbookChunks.constant,
        probability: worldbookChunks.probability,
        scanDepth: worldbookChunks.scanDepth,
        position: worldbookChunks.position,
        groupName: worldbookChunks.groupName,
        sticky: worldbookChunks.sticky,
        cooldown: worldbookChunks.cooldown,
        delay: worldbookChunks.delay,
        excludeRecursion: worldbookChunks.excludeRecursion,
        delayUntilRecursion: worldbookChunks.delayUntilRecursion,
        groupWeight: worldbookChunks.groupWeight,
        caseSensitive: worldbookChunks.caseSensitive,
        matchWholeWords: worldbookChunks.matchWholeWords,
        useRegex: worldbookChunks.useRegex,
        createdAt: worldbookChunks.createdAt,
        updatedAt: worldbookChunks.updatedAt,
        content: chunks.text,
      })
      .from(worldbookChunks)
      .leftJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(and(...conditions))
      .orderBy(orderByClause)
      .limit(pageSize)
      .offset(offset);

    // 转换数据格式
    const items = data.map(item => ({
      id: item.id,
      worldbookId: item.worldbookId,
      chunkId: item.chunkId,
      title: item.title,
      enabled: item.enabled,
      keys: item.keys || [],
      keysSecondary: item.keysSecondary || [],
      selectiveLogic: item.selectiveLogic || 0,
      order: item.order || 100,
      constant: item.constant || false,
      probability: item.probability || 100,
      scanDepth: item.scanDepth,
      position: item.position || 0,
      groupName: item.groupName,
      sticky: item.sticky,
      cooldown: item.cooldown,
      delay: item.delay,
      excludeRecursion: item.excludeRecursion || false,
      delayUntilRecursion: item.delayUntilRecursion || 0,
      groupWeight: item.groupWeight || 100,
      caseSensitive: item.caseSensitive,
      matchWholeWords: item.matchWholeWords,
      useRegex: item.useRegex || false,
      createdAt: item.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: item.updatedAt?.toISOString() || new Date().toISOString(),
      content: item.content || '',
    }));

    return {
      items,
      total,
      page,
      pageSize,
      hasMore: offset + pageSize < total
    };
  };

  update = async (id: string, value: Partial<WorldbookChunkItem> & { content?: string }) => {
    return this.db.transaction(async (trx) => {
      // 获取当前记录
      const worldbookChunk = await trx.query.worldbookChunks.findFirst({
        where: and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)),
      });

      if (!worldbookChunk) {
        throw new Error('Worldbook chunk not found');
      }

      // 如果包含 content 字段，更新 chunks 表
      if (value.content !== undefined) {
        await trx
          .update(chunks)
          .set({ text: value.content, updatedAt: new Date() })
          .where(eq(chunks.id, worldbookChunk.chunkId));
      }

      // 更新 worldbook_chunks 表（排除 content 字段）
      const { content, ...worldbookChunkValue } = value;
      if (Object.keys(worldbookChunkValue).length > 0) {
        await trx
          .update(worldbookChunks)
          .set({ ...worldbookChunkValue, updatedAt: new Date() })
          .where(and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)));
      } else if (value.content !== undefined) {
        // 如果只更新了内容，也要更新 worldbook_chunks 的时间戳
        await trx
          .update(worldbookChunks)
          .set({ updatedAt: new Date() })
          .where(and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)));
      }

      // 在事务内查询更新后的完整条目数据（包含content）
      const result = await trx
        .select({
          id: worldbookChunks.id,
          worldbookId: worldbookChunks.worldbookId,
          chunkId: worldbookChunks.chunkId,
          title: worldbookChunks.title,
          keys: worldbookChunks.keys,
          keysSecondary: worldbookChunks.keysSecondary,
          selectiveLogic: worldbookChunks.selectiveLogic,
          order: worldbookChunks.order,
          probability: worldbookChunks.probability,
          constant: worldbookChunks.constant,
          scanDepth: worldbookChunks.scanDepth,
          position: worldbookChunks.position,
          groupName: worldbookChunks.groupName,
          sticky: worldbookChunks.sticky,
          cooldown: worldbookChunks.cooldown,
          delay: worldbookChunks.delay,
          excludeRecursion: worldbookChunks.excludeRecursion,
          delayUntilRecursion: worldbookChunks.delayUntilRecursion,
          groupWeight: worldbookChunks.groupWeight,
          caseSensitive: worldbookChunks.caseSensitive,
          matchWholeWords: worldbookChunks.matchWholeWords,
          useRegex: worldbookChunks.useRegex,
          enabled: worldbookChunks.enabled,
          userId: worldbookChunks.userId,
          createdAt: worldbookChunks.createdAt,
          updatedAt: worldbookChunks.updatedAt,
          content: chunks.text,
        })
        .from(worldbookChunks)
        .leftJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
        .where(and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)))
        .limit(1);

      return result[0];
    });
  };

  delete = async (id: string) => {
    return this.db.transaction(async (trx) => {
      // 1. 获取要删除的worldbook_chunk记录
      const worldbookChunk = await trx.query.worldbookChunks.findFirst({
        where: and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)),
      });

      if (!worldbookChunk) return;

      // 2. 删除对应的chunk（这会级联删除embeddings）
      await trx.delete(chunks).where(eq(chunks.id, worldbookChunk.chunkId));

      // 3. 删除worldbook_chunks记录（可能已经被级联删除，但为了确保一致性还是执行）
      await trx
        .delete(worldbookChunks)
        .where(and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)));
    });
  };

  // 删除整个世界书的所有chunks
  deleteByWorldbookId = async (worldbookId: string, trx?: Transaction) => {
    // 1. 先查询获取chunk IDs（查询操作不需要事务）
    const db = trx || this.db;
    const worldbookChunkList = await db.query.worldbookChunks.findMany({
      columns: { chunkId: true },
      where: and(eq(worldbookChunks.worldbookId, worldbookId), eq(worldbookChunks.userId, this.userId)),
    });

    if (worldbookChunkList.length === 0) return;

    const chunkIds = worldbookChunkList.map((item: { chunkId: string }) => item.chunkId);

    // 2. 删除操作需要事务保证原子性
    const executeDelete = async (tx: Transaction) => {
      // 先删除embeddings，再删除chunks
      await tx.delete(embeddings).where(inArray(embeddings.chunkId, chunkIds));
      await tx.delete(chunks).where(inArray(chunks.id, chunkIds));
      // worldbook_chunks会通过外键约束自动级联删除
    };

    // 如果已经在事务中，直接执行删除；否则创建新事务
    if (trx) {
      await executeDelete(trx);
    } else {
      await this.db.transaction(executeDelete);
    }

    console.log(`🗑️ [WorldbookChunk] Deleted ${chunkIds.length} chunks for worldbook ${worldbookId}`);
  };

  bulkCreate = async (entries: CreateChunkData[]) => {
    return this.db.transaction(async (trx) => {
      const results = [];

      for (const entry of entries) {
        const result = await this.createWithContent(entry, trx);
        results.push(result);
      }

      return results;
    });
  };

  // ====== Content Management ======
  createWithContent = async (data: CreateChunkData, trx?: Transaction) => {
    const db = trx || this.db;

    return db.transaction(async (transaction) => {
      // 1. Create chunk record to store content
      const [chunkResult] = await transaction
        .insert(chunks)
        .values({
          text: data.content,
          type: 'worldbook',
          userId: this.userId,
          metadata: {
            worldbookId: data.worldbookId,
            title: data.title,
          },
        } as NewChunkItem)
        .returning();

      // 2. Create worldbook_chunks record
      const [worldbookChunkResult] = await transaction
        .insert(worldbookChunks)
        .values({
          worldbookId: data.worldbookId,
          chunkId: chunkResult.id,
          title: data.title,
          keys: data.keys,
          keysSecondary: data.keysSecondary || [],
          selectiveLogic: data.selectiveLogic || 0,
          order: data.order || 100,
          constant: data.constant || false,
          probability: data.probability || 100,
          scanDepth: data.scanDepth,
          position: data.position || 0,
          groupName: data.groupName,
          sticky: data.sticky,
          cooldown: data.cooldown,
          delay: data.delay,
          excludeRecursion: data.excludeRecursion || false,
          delayUntilRecursion: data.delayUntilRecursion || 0,
          groupWeight: data.groupWeight || 100,
          caseSensitive: data.caseSensitive,
          matchWholeWords: data.matchWholeWords,
          useRegex: data.useRegex || false,
          userId: this.userId,
        })
        .returning();

      return {
        ...worldbookChunkResult,
        content: chunkResult.text,
      };
    });
  };

  // @deprecated Use update method with { content: string } parameter instead
  updateContent = async (id: string, content: string) => {
    return this.update(id, { content });
  };

  getContentByChunkId = async (chunkId: string) => {
    const data = await this.db
      .select({
        id: worldbookChunks.id,
        title: worldbookChunks.title,
        content: chunks.text,
        worldbookId: worldbookChunks.worldbookId,
      })
      .from(worldbookChunks)
      .leftJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(
        and(
          eq(worldbookChunks.chunkId, chunkId),
          eq(worldbookChunks.userId, this.userId)
        )
      )
      .limit(1);

    return data[0];
  };

  // ====== Activation Queries ======
  findActivatableEntries = async (worldbookIds: string[]) => {
    if (worldbookIds.length === 0) return [];

    const data = await this.db
      .select({
        id: worldbookChunks.id,
        worldbookId: worldbookChunks.worldbookId,
        title: worldbookChunks.title,
        content: chunks.text,
        keys: worldbookChunks.keys,
        keysSecondary: worldbookChunks.keysSecondary,
        selectiveLogic: worldbookChunks.selectiveLogic,
        order: worldbookChunks.order,
        constant: worldbookChunks.constant,
        probability: worldbookChunks.probability,
        scanDepth: worldbookChunks.scanDepth,
        position: worldbookChunks.position,
        groupName: worldbookChunks.groupName,
        sticky: worldbookChunks.sticky,
        cooldown: worldbookChunks.cooldown,
        delay: worldbookChunks.delay,
        excludeRecursion: worldbookChunks.excludeRecursion,
        delayUntilRecursion: worldbookChunks.delayUntilRecursion,
        groupWeight: worldbookChunks.groupWeight,
        caseSensitive: worldbookChunks.caseSensitive,
        matchWholeWords: worldbookChunks.matchWholeWords,
        useRegex: worldbookChunks.useRegex,
      })
      .from(worldbookChunks)
      .leftJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(
        and(
          inArray(worldbookChunks.worldbookId, worldbookIds),
          eq(worldbookChunks.enabled, true),
          eq(worldbookChunks.userId, this.userId)
        )
      )
      .orderBy(desc(worldbookChunks.order));

    return data;
  };

  searchByKeywords = async (keywords: string[], worldbookIds?: string[]) => {
    // TODO: Implement keyword search logic
    // Return empty array for now, can be implemented later as needed
    return [];
  };

  searchBySemantic = async (query: string, worldbookIds?: string[]) => {
    // TODO: Integrate existing semantic search functionality
    // Return empty array for now, can be implemented later as needed
    return [];
  };

  // ====== Import/Export Functions ======
  importFromSillyTavern = async (worldbookId: string, data: SillyTavernWorldbook) => {
    const entries = data.entries || {};
    const importResults = [];

    for (const [entryId, entry] of Object.entries(entries)) {
      try {
        // Handle selective field type conversion
        let selectiveLogic = 0;
        if (entry.selectiveLogic !== undefined) {
          selectiveLogic = entry.selectiveLogic;
        } else if (entry.selective !== undefined) {
          // Convert boolean to number
          if (typeof entry.selective === 'boolean') {
            selectiveLogic = entry.selective ? 1 : 0;
          } else {
            selectiveLogic = entry.selective;
          }
        }

        // Generate title: prioritize comment, then first keyword, finally Entry ID
        let title = entry.comment || '';
        if (!title && entry.key && entry.key.length > 0) {
          title = Array.isArray(entry.key) ? entry.key[0] : entry.key;
        }
        if (!title) {
          title = `Entry ${entryId}`;
        }

        const chunkData: CreateChunkData = {
          worldbookId,
          title,
          content: entry.content || '',
          keys: Array.isArray(entry.key) ? entry.key : [entry.key].filter(Boolean),
          keysSecondary: Array.isArray(entry.keysecondary) ? entry.keysecondary : [],
          selectiveLogic,
          order: entry.order || 100,
          constant: entry.constant || false,
          probability: entry.probability || 100,
          position: entry.position || 0,
          excludeRecursion: entry.excludeRecursion || false,
        };

        const result = await this.createWithContent(chunkData);
        importResults.push({
          success: true,
          entryId,
          worldbookChunkId: result.id,
          title: title,
        });
      } catch (error) {
        importResults.push({
          success: false,
          entryId,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return {
      total: Object.keys(entries).length,
      successful: importResults.filter(r => r.success).length,
      failed: importResults.filter(r => !r.success).length,
      results: importResults,
    };
  };

}
