import type { WorldbookStore } from './store';

/**
 * 世界书相关选择器
 */
export const worldbookSelectors = {
  // 基础数据选择器
  worldbooks: (s: WorldbookStore) => s.worldbooks,
  currentWorldbook: (s: WorldbookStore) => s.currentWorldbook,
  selectedWorldbookId: (s: WorldbookStore) => s.selectedWorldbookId,
  
  // 加载状态选择器
  loading: (s: WorldbookStore) => s.loading,
  importing: (s: WorldbookStore) => s.importing,
  worldbookLoadingIds: (s: WorldbookStore) => s.worldbookLoadingIds,
  
  // 统计信息选择器
  stats: (s: WorldbookStore) => s.stats,
  
  // UI状态选择器
  sidebarCollapsed: (s: WorldbookStore) => s.sidebarCollapsed,
  worldbookManagerVisible: (s: WorldbookStore) => s.worldbookManagerVisible,
  
  // 计算属性选择器
  enabledWorldbooks: (s: WorldbookStore) => s.worldbooks.filter(wb => wb.enabled),
  disabledWorldbooks: (s: WorldbookStore) => s.worldbooks.filter(wb => !wb.enabled),
  worldbookCount: (s: WorldbookStore) => s.worldbooks.length,
  enabledWorldbookCount: (s: WorldbookStore) => s.worldbooks.filter(wb => wb.enabled).length,
  
  // 查找方法
  getWorldbookById: (s: WorldbookStore) => (id: string) => 
    s.worldbooks.find(wb => wb.id === id),
  
  // 状态检查方法
  isWorldbookLoading: (s: WorldbookStore) => (id: string) => 
    s.worldbookLoadingIds.includes(id),
};

/**
 * 条目相关选择器
 */
export const entrySelectors = {
  // 基础数据选择器
  entries: (s: WorldbookStore) => s.entries,
  currentEntry: (s: WorldbookStore) => s.currentEntry,
  selectedEntryIds: (s: WorldbookStore) => s.selectedEntryIds,
  
  // 分页和搜索选择器
  searchParams: (s: WorldbookStore) => s.searchParams,
  entriesTotal: (s: WorldbookStore) => s.entriesTotal,
  entriesPage: (s: WorldbookStore) => s.entriesPage,
  entriesPageSize: (s: WorldbookStore) => s.entriesPageSize,
  entriesHasMore: (s: WorldbookStore) => s.entriesHasMore,
  
  // 加载状态选择器
  entriesLoading: (s: WorldbookStore) => s.entriesLoading,
  entryLoadingIds: (s: WorldbookStore) => s.entryLoadingIds,
  entryUpdatingIds: (s: WorldbookStore) => s.entryUpdatingIds,
  bulkOperationLoading: (s: WorldbookStore) => s.bulkOperationLoading,
  
  // 过滤和排序选择器
  filterEnabled: (s: WorldbookStore) => s.filterEnabled,
  sortBy: (s: WorldbookStore) => s.sortBy,
  sortOrder: (s: WorldbookStore) => s.sortOrder,
  
  // UI状态选择器
  entryEditorVisible: (s: WorldbookStore) => s.entryEditorVisible,
  
  // 操作结果选择器
  lastBulkOperationResult: (s: WorldbookStore) => s.lastBulkOperationResult,
  lastImportResult: (s: WorldbookStore) => s.lastImportResult,
  
  // 计算属性选择器
  enabledEntries: (s: WorldbookStore) => s.entries.filter(entry => entry.enabled),
  disabledEntries: (s: WorldbookStore) => s.entries.filter(entry => !entry.enabled),
  selectedEntries: (s: WorldbookStore) => 
    s.entries.filter(entry => s.selectedEntryIds.includes(entry.id)),
  entryCount: (s: WorldbookStore) => s.entries.length,
  selectedEntryCount: (s: WorldbookStore) => s.selectedEntryIds.length,
  
  // 查找方法
  getEntryById: (s: WorldbookStore) => (id: string) => 
    s.entries.find(entry => entry.id === id),
  
  // 状态检查方法
  isEntryLoading: (s: WorldbookStore) => (id: string) => 
    s.entryLoadingIds.includes(id),
  isEntryUpdating: (s: WorldbookStore) => (id: string) => 
    s.entryUpdatingIds.includes(id),
  isEntrySelected: (s: WorldbookStore) => (id: string) => 
    s.selectedEntryIds.includes(id),
  
  // 分页信息
  paginationInfo: (s: WorldbookStore) => ({
    page: s.entriesPage,
    pageSize: s.entriesPageSize,
    total: s.entriesTotal,
    hasMore: s.entriesHasMore,
    totalPages: Math.ceil(s.entriesTotal / s.entriesPageSize),
  }),
};

/**
 * 组合选择器
 * 提供跨域的复杂查询
 */
export const combinedSelectors = {
  // 当前世界书的条目
  currentWorldbookEntries: (s: WorldbookStore) => {
    if (!s.currentWorldbook) return [];
    return s.entries;
  },
  
  // 当前世界书的条目统计
  currentWorldbookStats: (s: WorldbookStore) => {
    if (!s.currentWorldbook) return null;
    
    const entries = s.entries;
    return {
      total: entries.length,
      enabled: entries.filter(e => e.enabled).length,
      disabled: entries.filter(e => !e.enabled).length,
      selected: s.selectedEntryIds.length,
    };
  },
  
  // 是否有选中的条目
  hasSelectedEntries: (s: WorldbookStore) => s.selectedEntryIds.length > 0,
  
  // 是否全选
  isAllEntriesSelected: (s: WorldbookStore) => 
    s.entries.length > 0 && s.selectedEntryIds.length === s.entries.length,
  
  // 是否部分选中
  isPartialEntriesSelected: (s: WorldbookStore) => 
    s.selectedEntryIds.length > 0 && s.selectedEntryIds.length < s.entries.length,
  
  // 当前是否有任何加载状态
  hasAnyLoading: (s: WorldbookStore) => 
    s.loading || 
    s.entriesLoading || 
    s.importing || 
    s.bulkOperationLoading ||
    s.entryLoadingIds.length > 0 ||
    s.entryUpdatingIds.length > 0 ||
    s.worldbookLoadingIds.length > 0,
  
  // 搜索状态
  hasActiveSearch: (s: WorldbookStore) => 
    Boolean(s.searchParams.query) ||
    s.searchParams.enabled !== undefined ||
    s.searchParams.activationMode !== undefined ||
    s.searchParams.position !== undefined ||
    Boolean(s.searchParams.groupName) ||
    s.searchParams.hasKeys !== undefined ||
    s.searchParams.hasSecondaryKeys !== undefined ||
    s.searchParams.selectiveLogic !== undefined ||
    s.searchParams.orderMin !== undefined ||
    s.searchParams.orderMax !== undefined ||
    s.searchParams.probabilityMin !== undefined ||
    s.searchParams.probabilityMax !== undefined,
  
  // 过滤状态
  hasActiveFilter: (s: WorldbookStore) => 
    s.filterEnabled !== null,
  
  // 排序状态
  sortingInfo: (s: WorldbookStore) => ({
    sortBy: s.sortBy,
    sortOrder: s.sortOrder,
    isDefaultSort: s.sortBy === 'order' && s.sortOrder === 'asc',
  }),
  
  // 完整的UI状态
  uiState: (s: WorldbookStore) => ({
    sidebarCollapsed: s.sidebarCollapsed,
    entryEditorVisible: s.entryEditorVisible,
    worldbookManagerVisible: s.worldbookManagerVisible,
  }),
  
  // 完整的加载状态
  loadingState: (s: WorldbookStore) => ({
    worldbooks: s.loading,
    entries: s.entriesLoading,
    importing: s.importing,
    bulkOperation: s.bulkOperationLoading,
    entryLoadingIds: s.entryLoadingIds,
    entryUpdatingIds: s.entryUpdatingIds,
    worldbookLoadingIds: s.worldbookLoadingIds,
  }),
};
