{"Favorite": "<PERSON><PERSON><PERSON><PERSON>", "Tag": "Etiqueta", "Duplicate": "Duplicar", "Persona": "<PERSON>a", "Delete": "Eliminar", "AI Response Configuration": "Configuración de Respuesta de IA", "AI Configuration panel will stay open": "El panel de Configuración de IA permanecerá abierto", "clickslidertips": "Haz clic para introducir valores manualmente.", "MAD LAB MODE ON": "MODO LABORATORIO MAD ENCENDIDO", "Documentation on sampling parameters": "Documentación sobre parámetros de muestreo", "kobldpresets": "Preajustes de Kobold", "guikoboldaisettings": "Ajustes de interfaz de KoboldAI", "Update current preset": "Actualizar el preajuste actual", "Save preset as": "Guardar preajuste como", "Import preset": "Importar preajuste", "Export preset": "Exportar preajuste", "Restore current preset": "Restaurar el preajuste actual", "Delete the preset": "Eliminar el preajuste", "novelaipresets": "Preajustes de NovelAI", "Default": "Predeterminado", "openaipresets": "Preajustes de OpenAI", "Text Completion presets": "Preajustes de Completado de Texto", "AI Module": "Módulo de IA", "Changes the style of the generated text.": "Cambia el estilo del texto generado.", "No Module": "<PERSON> módulo", "Instruct": "Instruir", "Prose Augmenter": "Aumentador de prosa", "Text Adventure": "Aventura de texto", "response legth(tokens)": "<PERSON><PERSON>ud de respuesta (tokens)", "Streaming": "Transmisión (Streaming)", "Streaming_desc": "Mostrar la respuesta poco a poco según se genera", "context size(tokens)": "<PERSON><PERSON><PERSON>o (tokens)", "unlocked": "Desbloqueado", "Only enable this if your model supports context sizes greater than 8192 tokens": "Habilita esto solo si tu modelo admite tamaños de contexto mayores de 8192 tokens", "Max prompt cost:": "Costo inmediato máximo:", "Display the response bit by bit as it is generated.": "Mostrar la respuesta poco a poco a medida que se genera.", "When this is off, responses will be displayed all at once when they are complete.": "<PERSON>uando esto está apagado, las respuestas se mostrarán de una vez cuando estén completas.", "Temperature": "Temperatura", "rep.pen": "Penalización de repetición", "Rep. Pen. Range.": "<PERSON><PERSON> de Pen. Rep.", "Rep. Pen. Slope": "Pendiente de penalización de repetición", "Rep. Pen. Freq.": "Frec. de Pen. Rep.", "Rep. Pen. Presence": "Presencia de Pen. Rep.", "TFS": "TFS", "Phrase Repetition Penalty": "Penalización por repetición de frases", "Off": "<PERSON><PERSON><PERSON>", "Very light": "<PERSON><PERSON> ligero", "Light": "Ligero", "Medium": "Medio", "Aggressive": "Agresivo", "Very aggressive": "<PERSON>y ag<PERSON>", "Unlocked Context Size": "Tamaño de contexto desbloqueado", "Unrestricted maximum value for the context slider": "Valor máximo sin restricciones para el control deslizante de contexto", "Context Size (tokens)": "<PERSON><PERSON><PERSON>o (tokens)", "Max Response Length (tokens)": "Longitud máxima de respuesta (tokens)", "Multiple swipes per generation": "<PERSON><PERSON><PERSON><PERSON> golpes por generación", "Enable OpenAI completion streaming": "Activar streaming de completado de OpenAI", "Frequency Penalty": "Penalización de frecuencia", "Presence Penalty": "Penalización de presencia", "Count Penalty": "Penalización de conteo", "Top K": "Top K", "Top P": "Top P", "Repetition Penalty": "Penalización por Repetición", "Min P": "<PERSON> m<PERSON>.", "Top A": "Top A", "Quick Prompts Edit": "Edición rápida de indicaciones", "Main": "Principal", "NSFW": "NSFW", "Jailbreak": "Jailbreak", "Utility Prompts": "Indicaciones de utilidad", "Impersonation prompt": "Indicaciónes de Suplantación", "Restore default prompt": "Restaurar las indicaciones predeterminada", "Prompt that is used for Impersonation function": "Indicación que se utiliza para la función de Suplantación", "World Info Format Template": "Plantilla de formato de información mundial", "Restore default format": "Restaurar formato predeterminado", "Wraps activated World Info entries before inserting into the prompt.": "Envuelve las entradas de información mundial activadas antes de insertarlas en el mensaje.", "scenario_format_template_part_1": "<PERSON>ar", "scenario_format_template_part_2": "para marcar un lugar donde se inserta el contenido.", "Scenario Format Template": "Plantilla de formato de escenario", "Personality Format Template": "Plantilla de formato de personalidad", "Group Nudge Prompt Template": "Plantilla de mensaje de desplazamiento grupal", "Sent at the end of the group chat history to force reply from a specific character.": "Enviado al final del historial de chat grupal para forzar la respuesta de un personaje específico.", "New Chat": "Nueva conversación", "Restore new chat prompt": "Restaurar nuevo mensaje de chat", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "Establecer al principio del historial de chat para indicar que está por comenzar un nuevo chat.", "New Group Chat": "Nuevo chat grupal", "Restore new group chat prompt": "Restaurar mensaje predeterminado", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "Establecer al principio del historial de chat para indicar que está a punto de comenzar un nuevo chat grupal.", "New Example Chat": "Nuevo chat de ejemplo", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "Establezca al comienzo de los ejemplos de diálogo para indicar que está a punto de comenzar un nuevo chat de ejemplo.", "Continue nudge": "<PERSON><PERSON><PERSON><PERSON>", "Set at the end of the chat history when the continue button is pressed.": "Se establece al final del historial de chat cuando se presiona el botón Continuar.", "Replace empty message": "Reemp<PERSON>zar mensaje vacío", "Send this text instead of nothing when the text box is empty.": "Enviar este texto en lugar de nada cuando el cuadro de texto está vacío.", "Seed": "<PERSON><PERSON>", "Set to get deterministic results. Use -1 for random seed.": "Configurado para obtener resultados deterministas. Utilice -1 para semillas aleatorias.", "Temperature controls the randomness in token selection": "La temperatura controla la aleatoriedad en la selección de tokens", "Top_K_desc": "Top K establece una cantidad máxima de tokens principales que se pueden elegir", "Top_P_desc": "Top P (también conocido como muestreo de núcleo)", "Typical P": "P típico", "Typical_P_desc": "El Muestreo P Típico prioriza tokens según su desviación de la entropía promedio del conjunto", "Min_P_desc": "Min P establece una probabilidad mínima base", "Top_A_desc": "Top A establece un umbral para la selección de tokens basado en el cuadrado de la probabilidad de token más alta", "Tail_Free_Sampling_desc": "Muestreo sin cola (TFS)", "rep.pen range": "rango de penalización de repetición", "Mirostat": "miros<PERSON>o", "Mode": "Modo", "Mirostat_Mode_desc": "Un valor de 0 desactiva Mirostat por completo. 1 es para Mirostat 1.0 y 2 es para Mirostat 2.0", "Tau": "tau", "Mirostat_Tau_desc": "Controla la variabilidad de las salidas de Mirostat.", "Eta": "eta", "Mirostat_Eta_desc": "Controla la tasa de aprendizaje de Mirostat", "Ban EOS Token": "Prohibir token EOS", "Ban_EOS_Token_desc": "Prohibir el token de fin de secuencia (EOS) con KoboldCpp (y posiblemente también otros tokens con KoboldAI).\rBueno para escribir historias, pero no debe usarse para chatear ni para el modo de instrucción.", "GBNF Grammar": "Gramática GBNF", "Type in the desired custom grammar": "Escribe la gramática personalizada deseada", "Samplers Order": "<PERSON><PERSON> de <PERSON>", "Samplers will be applied in a top-down order. Use with caution.": "Los Muestreadores se aplicarán en un orden de arriba hacia abajo. Úsalo con precaución.", "Tail Free Sampling": "Muestreo sin cola", "Load koboldcpp order": "<PERSON><PERSON> orden de koboldcpp", "Preamble": "Preambulo", "Use style tags to modify the writing style of the output.": "Usa etiquetas de estilo para modificar el estilo de escritura de la salida.", "Banned Tokens": "Tokens prohibidos", "Sequences you don't want to appear in the output. One per line.": "Secuencias que no quieres que aparezcan en la salida. Una por línea.", "Logit Bias": "Sesgo de logit", "Add": "Agregar", "Helps to ban or reenforce the usage of certain words": "Ayuda a prohibir o reforzar el uso de ciertas palabras", "CFG Scale": "Escala CFG", "Negative Prompt": "Indicaciónes negativas", "Add text here that would make the AI generate things you don't want in your outputs.": "Agrega aquí texto que haría que la IA genere cosas que no quieres en tus salidas.", "Used if CFG Scale is unset globally, per chat or character": "Usado si la Escala CFG no está configurada globalmente, por chat o por personaje", "Mirostat Tau": "Tau de Mirostat", "Mirostat LR": "Mirostato LR", "Min Length": "<PERSON><PERSON><PERSON>", "Top K Sampling": "Muestreo de Top K", "Nucleus Sampling": "Muestreo de núcleo", "Top A Sampling": "Muestreo de Top A", "CFG": "CFG", "Neutralize Samplers": "Neutraliza<PERSON>", "Set all samplers to their neutral/disabled state.": "<PERSON><PERSON>cer todos los muestreadores en su estado neutral/desactivado.", "Sampler Select": "Selección de muestrario", "Customize displayed samplers or add custom samplers.": "Personalice los samplers mostrados o agregue samplers personalizados.", "Epsilon Cutoff": "Corte Epsilon", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "El corte Epsilon establece un límite de probabilidad por debajo del cual se excluyen los tokens de ser muestreados", "Eta Cutoff": "Corte Eta", "Eta_Cutoff_desc": "El Corte de Eta es el parámetro principal de la técnica especial de Muestreo Eta.&#13;En unidades de 1e-4; un valor razonable es 3.&#13;Establecer en 0 para desactivar.&#13;Consulte el documento Truncation Sampling as Language Model Desmoothing de <PERSON> et al. (2022) para más detalles.", "rep.pen decay": "Decaimiento de la pluma de representación", "Encoder Rep. Pen.": "Pen. de repetición de codificador", "No Repeat Ngram Size": "Tamaño de ngrama sin repetición", "Skew": "<PERSON><PERSON><PERSON>", "Max Tokens Second": "Máximo de tokens por segundo", "Smooth Sampling": "Muestreo suave", "Smooth_Sampling_desc": "Le permite utilizar transformaciones cuadráticas/cúbicas para ajustar la distribución. Los valores más bajos del factor de suavizado serán más creativos, normalmente entre 0,2 y 0,3 es el punto óptimo (suponiendo que la curva = 1). Los valores más altos de la curva de suavizado harán que la curva sea más pronunciada, lo que castigará de manera más agresiva las elecciones de baja probabilidad. La curva 1,0 equivale a utilizar únicamente el factor de suavizado.", "Smoothing Factor": "Factor de suavizado", "Smoothing Curve": "Curva de su<PERSON>zado", "DRY_Repetition_Penalty_desc": "DRY penaliza los tokens que extenderían el final de la entrada a una secuencia que ocurrió previamente en la entrada. Establezca el multiplicador en 0 para desactivarlo.", "DRY Repetition Penalty": "Penalización por repetición de DRY", "DRY_Multiplier_desc": "Establezca el valor > 0 para habilitar SECO. Controla la magnitud de la penalización para las secuencias penalizadas más cortas.", "Multiplier": "Multiplicador", "DRY_Base_desc": "Controla qué tan rápido crece la penalización al aumentar la longitud de la secuencia.", "Base": "Base", "DRY_Allowed_Length_desc": "Secuencia más larga que se puede repetir sin ser penalizada.", "Allowed Length": "<PERSON><PERSON><PERSON>", "Penalty Range": "Rango de penalización", "DRY_Sequence_Breakers_desc": "Tokens en los que no se continúa la coincidencia de secuencias. Especificado como una lista de cadenas entre comillas separadas por comas.", "Sequence Breakers": "Rompedores de secuencia", "JSON-serialized array of strings.": "Matriz de cadenas serializadas en JSON.", "Dynamic Temperature": "Temperatura dinámica", "Scale Temperature dynamically per token, based on the variation of probabilities": "Escala la Temperatura dinámicamente por token, basado en la variación de probabilidades", "Minimum Temp": "Temperatura mínima", "Maximum Temp": "Temperatura máxima", "Exponent": "Exponente", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat (modo=1 es solo para llama.cpp)", "Mirostat_desc": "Mirostat es un termostato para la perplejidad de salida", "Mirostat Mode": "Modo de Mirostat", "Variability parameter for Mirostat outputs": "Parámetro de variabilidad para las salidas de Mirostat", "Mirostat Eta": "Eta de Mirostat", "Learning rate of Mirostat": "Tasa de aprendizaje de Mirostat", "Beam search": "Búsqueda de haz", "Helpful tip coming soon.": "Próximamente habrá un consejo útil.", "Number of Beams": "Númer<PERSON>", "Length Penalty": "Penalización de longitud", "Early Stopping": "Detención temprana", "Contrastive search": "Búsqueda contrastiva", "Penalty Alpha": "Alfa de penalización", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "Fuerza del término de regularización de la Búsqueda Contrastiva. Establece en 0 para deshabilitar CS.", "Do Sample": "<PERSON><PERSON><PERSON><PERSON>", "Add BOS Token": "Agregar token BOS", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "Agrega el token BOS al principio de las indicaciones. Desactivar esto puede hacer que las respuestas sean más creativas", "Ban the eos_token. This forces the model to never end the generation prematurely": "Prohibir el token EOS. Esto obliga al modelo a nunca terminar la generación prematuramente", "Ignore EOS Token": "Ignorar token EOS", "Ignore the EOS Token even if it generates.": "Ignore el token EOS incluso si se genera.", "Skip Special Tokens": "Omitir tokens especiales", "Temperature Last": "Temperatura de Último", "Temperature_Last_desc": "Usar el muestreador de temperatura al final", "Speculative Ngram": "Ngram especulativo", "Use a different speculative decoding method without a draft model": "Utilice un método de decodificación especulativa diferente sin un modelo preliminar. Se prefiere utilizar un modelo preliminar. El ngrama especulativo no es tan eficaz.", "Spaces Between Special Tokens": "Espacios entre fichas especiales", "LLaMA / Mistral / Yi models only": "Solo modelos LLaMA / Mistral / Yi", "Example: some text [42, 69, 1337]": "Ejemplo: al<PERSON><PERSON> texto [42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "Guía Libre de Clasificadores. Pronto llegará un consejo más útil", "Scale": "Escala", "JSON Schema": "Esquema JSON", "Type in the desired JSON schema": "Escriba el esquema JSON deseado", "Grammar String": "Cadena de gramática", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF o EBNF, depende del backend en uso. Si estás usando esto, debes saber cuál.", "Top P & Min P": "P superior y P mínima", "Load default order": "<PERSON><PERSON> orden predeterminado", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "llama.cpp únicamente. Determina el orden de los muestreadores. Si el modo Mirostat no es 0, se ignora el orden de las muestras.", "Sampler Priority": "Prioridad del muestreador", "Ooba only. Determines the order of samplers.": "Solo Ooba. Determina el orden de los muestreadores.", "Character Names Behavior": "Comportamiento de los nombres de personajes", "Helps the model to associate messages with characters.": "<PERSON><PERSON><PERSON> al modelo a asociar mensajes con personajes.", "None": "<PERSON><PERSON><PERSON>", "character_names_default": "Excepto grupos y personas pasadas. De lo contrario, asegúrese de proporcionar nombres en el mensaje.", "Don't add character names.": "No agregues nombres de personajes.", "Completion": "Objeto de finalización", "character_names_completion": "Aplican restricciones: solo caracteres alfanuméricos latinos y guiones bajos. No funciona para todas las fuentes, en particular: Claude, MistralAI, Google.", "Add character names to completion objects.": "Agregue nombres de personajes a los objetos de finalización.", "Message Content": "Contenido del mensaje", "Prepend character names to message contents.": "Anteponga los nombres de los caracteres al contenido del mensaje.", "Continue Postfix": "<PERSON><PERSON><PERSON><PERSON> sufijo", "The next chunk of the continued message will be appended using this as a separator.": "El siguiente fragmento del mensaje continuado se agregará utilizando esto como separador.", "Space": "Espacio", "Newline": "Nueva línea", "Double Newline": "Nueva línea doble", "Wrap user messages in quotes before sending": "Envolver los mensajes de usuario entre comillas antes de enviarlos", "Wrap in Quotes": "Envolver entre comillas", "Wrap entire user message in quotes before sending.": "Envolver todo el mensaje del usuario entre comillas antes de enviarlo.", "Leave off if you use quotes manually for speech.": "Omite esto si usas comillas manualmente para diálogo.", "Continue prefill": "Continuar con prellenado", "Continue sends the last message as assistant role instead of system message with instruction.": "Continuar envía el último mensaje como rol de asistente en lugar de mensaje del sistema con instrucciones.", "Squash system messages": "Aplastar mensajes del sistema", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "Combina mensajes del sistema consecutivos en uno solo (excluyendo diálogos de ejemplo). <PERSON>uede mejorar la coherencia para algunos modelos.", "Enable function calling": "Habilitar llamada a función", "Send inline images": "Enviar imágenes en línea", "image_inlining_hint_1": "Envía imágenes en mensajes si el modelo lo admite.\n                                                Utilizar el", "image_inlining_hint_2": "acción sobre cualquier mensaje o el", "image_inlining_hint_3": "menú para adjuntar un archivo de imagen al chat.", "Inline Image Quality": "Calidad de imagen en línea", "openai_inline_image_quality_auto": "Auto", "openai_inline_image_quality_low": "<PERSON><PERSON>", "openai_inline_image_quality_high": "Alto", "Use AI21 Tokenizer": "Utilice el tokenizador AI21", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "Utilice el tokenizador apropiado para los modelos Jurassic, que es más eficiente que el de GPT.", "Use Google Tokenizer": "Usar Tokenizador de Google", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "Usa el tokenizador apropiado para los modelos de Google a través de su API. Procesamiento de indicaciones más lento, pero ofrece un recuento de tokens mucho más preciso.", "Use system prompt": "Usar el mensaje del sistema", "(Gemini 1.5 Pro/Flash only)": "(<PERSON><PERSON><PERSON> 1.5 Pro/Flash)", "Merges_all_system_messages_desc_1": "Combina todos los mensajes del sistema hasta el primer mensaje con una función que no pertenece al sistema y los envía en un", "Merges_all_system_messages_desc_2": "campo.", "Assistant Prefill": "<PERSON><PERSON><PERSON> Asistente", "Start Claude's answer with...": "Iniciar la respuesta de Claude con...", "Assistant Impersonation Prefill": "Precarga de suplantación de asistente", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "Enviar la indicación del sistema para los modelos admitidos. Si está desactivado, el mensaje del usuario se agrega al principio de las indicaciónes.", "User first message": "Primer men<PERSON><PERSON> del usuario", "Restore User first message": "Restaurar el primer mensaje del usuario", "Human message": "Mensaje humano, instrucción, etc.\nNo agrega nada cuando está vacío, es decir, requiere un nuevo mensaje con el rol 'usuario'.", "New preset": "Nuevo preajuste", "Delete preset": "Elimina<PERSON> preajuste", "View / Edit bias preset": "Ver / <PERSON>ar preajuste de se<PERSON>go", "Add bias entry": "Agregar entrada de sesgo", "Most tokens have a leading space.": "La mayoría de los tokens tienen un espacio inicial.", "API Connections": "Conexiones de API", "Text Completion": "Completar texto", "Chat Completion": "Finalización del chat", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "Evite enviar información sensible a Horde.", "Review the Privacy statement": "Revise la declaración de privacidad", "Register a Horde account for faster queue times": "Registra una cuenta de Horde para tiempos de espera más rápidos", "Learn how to contribute your idle GPU cycles to the Horde": "Aprende cómo contribuir con tus ciclos de GPU inactivos a Horde", "Adjust context size to worker capabilities": "Ajusta el tamaño del contexto a las capacidades del trabajador", "Adjust response length to worker capabilities": "Ajusta la longitud de la respuesta a las capacidades del trabajador", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "<PERSON>uede ayudar con malas respuestas al encolar solo a los trabajadores aprobados. Puede ralentizar el tiempo de respuesta.", "Trusted workers only": "Solo trabajadores de confianza", "API key": "Clave API", "Get it here:": "Obt<PERSON><PERSON> aquí:", "Register": "Registrar", "View my Kudos": "<PERSON>er mis Kudos", "Enter": "Ingresar", "to use anonymous mode.": "para usar el modo anónimo.", "Clear your API key": "Borrar tu clave de API", "For privacy reasons, your API key will be hidden after you reload the page.": "Por razones de privacidad, su clave de API se ocultará después de que vuelva a cargar la página.", "Models": "Modelos", "Refresh models": "Actualizar modelos", "-- Horde models not loaded --": "-- <PERSON><PERSON> de Horde no cargados --", "Not connected...": "No conectado...", "API url": "URL de la API", "Example: http://127.0.0.1:5000/api ": "Ejemplo: http://127.0.0.1:5000/api", "Connect": "Conectar", "Cancel": "<PERSON><PERSON><PERSON>", "Novel API key": "Clave API de Novel", "Get your NovelAI API Key": "Obtenga su Clave de API de NovelAI", "Enter it in the box below": "Introdúcelo en el cuadro de abajo", "Novel AI Model": "Modelo Novel AI", "No connection...": "Sin conexión...", "API Type": "Tipo de API", "Default (completions compatible)": "Predeterminado [compatible con OpenAI/compleciones: oobabooga, LM Studio, etc.]", "TogetherAI API Key": "Clave API de TogetherAI", "TogetherAI Model": "Modelo TogetherAI", "-- Connect to the API --": "-- Conectar a la API --", "OpenRouter API Key": "Clave API de OpenRouter", "Click Authorize below or get the key from": "Haz clic en Autorizar a continuación o obtén la clave desde", "View Remaining Credits": "<PERSON>er Cré<PERSON>os Restantes", "OpenRouter Model": "<PERSON><PERSON> OpenRouter", "Model Providers": "Proveedores de modelos", "InfermaticAI API Key": "Clave API de InfermaticAI", "InfermaticAI Model": "Modelo InfermaticAI", "DreamGen API key": "Clave API de DreamGen", "DreamGen Model": "<PERSON><PERSON> DreamGen", "Mancer API key": "Clave API de Mancer", "Mancer Model": "<PERSON><PERSON>", "Make sure you run it with": "Asegúrate de ejecutarlo con", "flag": "bandera", "API key (optional)": "Clave API (opcional)", "Server url": "URL del servidor", "Example: http://127.0.0.1:5000": "Ejemplo: http://127.0.0.1:5000", "Custom model (optional)": "Modelo personalizado (opcional)", "vllm-project/vllm": "vllm-project/vllm (modo contenedor de API OpenAI)", "vLLM API key": "Clave API vLLM", "Example: http://127.0.0.1:8000": "Ejemplo: http://127.0.0.1:8000", "vLLM Model": "Modelo vLLM", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine (Modo envolvente para API de OpenAI)", "Aphrodite API key": "Clave de API de Aphrodite", "Aphrodite Model": "<PERSON><PERSON>", "ggerganov/llama.cpp": "ggerganov/llama.cpp (Servidor de salida)", "Example: http://127.0.0.1:8080": "Ejemplo: http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "Ejemplo: http://127.0.0.1:11434", "Ollama Model": "<PERSON><PERSON>", "Download": "<PERSON><PERSON><PERSON>", "Tabby API key": "Clave API de Tabby", "koboldcpp API key (optional)": "Clave API de koboldcpp (opcional)", "Example: http://127.0.0.1:5001": "Ejemplo: http://127.0.0.1:5001", "Authorize": "Autorizar", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "Obtenga su token de API de OpenRouter utilizando el flujo OAuth. Será redirigido a openrouter.ai", "Bypass status check": "Saltar la verificación del estado", "Chat Completion Source": "Fuente de Completado de Chat", "Reverse Proxy": "Proxy inverso", "Proxy Presets": "Ajustes preestablecidos de proxy", "Saved addresses and passwords.": "Direcciones y contraseñas guardadas.", "Save Proxy": "Guardar proxy", "Delete Proxy": "Eliminar proxy", "Proxy Name": "Nombre del apoderado", "This will show up as your saved preset.": "Esto aparecerá como su ajuste preestablecido guardado.", "Proxy Server URL": "URL del servidor proxy", "Alternative server URL (leave empty to use the default value).": "URL de servidor alternativo (deja vacío para usar el valor predeterminado).", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "Elimina tu verdadera clave de API de OAI del panel de API ANTES de escribir algo en este cuadro", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "No podemos brindar soporte para problemas encontrados al usar un proxy no oficial de OpenAI", "Doesn't work? Try adding": "¿No funciona? Prueba a agregar", "at the end!": "¡al final!", "Proxy Password": "Contraseña de proxy", "Will be used as a password for the proxy instead of API key.": "Se utilizará como contraseña para el proxy en lugar de clave API.", "Peek a password": "Mira una contraseña", "OpenAI API key": "Clave de API de OpenAI", "View API Usage Metrics": "Ver métricas de uso de la API", "Follow": "<PERSON><PERSON><PERSON>", "these directions": "estas instrucciones", "to get your OpenAI API key.": "para obtener tu clave API de OpenAI.", "Use Proxy password field instead. This input will be ignored.": "Utilice el campo \"Contraseña de proxy\" en su lugar. Esta entrada será ignorada.", "OpenAI Model": "Modelo de OpenAI", "Bypass API status check": "Saltar la verificación del estado de la API", "Show External models (provided by API)": "Mostrar modelos externos (provistos por la API)", "Get your key from": "Obtén tu clave desde", "Anthropic's developer console": "la consola de desarrolladores de Anthropic", "Claude Model": "<PERSON><PERSON>", "Window AI Model": "Modelo de Window AI", "Model Order": "Clasificación de modelos de OpenRouter", "Alphabetically": "Alfabéticamente", "Price": "Pre<PERSON> (más barato)", "Context Size": "Tamaño del contexto", "Group by vendors": "Agrupar por proveedores", "Group by vendors Description": "Coloque los modelos OpenAI en un grupo, los modelos antrópicos en otro grupo, etc. Se puede combinar con la clasificación.", "Allow fallback routes": "<PERSON><PERSON><PERSON>sp<PERSON>", "Allow fallback routes Description": "El modelo alternativo se elige automáticamente si el modelo seleccionado no puede cumplir con tu solicitud.", "Scale API Key": "Clave API de Scale", "Clear your cookie": "Limpia tu cookie", "Alt Method": "Método alternativo", "AI21 API Key": "Clave API de AI21", "AI21 Model": "Modelo de AI21", "Google AI Studio API Key": "Clave API de Google AI Studio", "Google Model": "Modelo de Google", "MistralAI API Key": "Clave API de MistralAI", "MistralAI Model": "Modelo MistralAI", "Groq API Key": "Clave API de Groq", "Groq Model": "<PERSON><PERSON>", "Perplexity API Key": "Clave API de Perplexity", "Perplexity Model": "Modelo de perplejidad", "Cohere API Key": "Clave API de Cohere", "Cohere Model": "Modelo coherente", "Custom Endpoint (Base URL)": "Punto final personalizado (URL base)", "Custom API Key": "Clave API personalizada", "Available Models": "Modelos disponibles", "Prompt Post-Processing": "Postprocesamiento rápido", "Applies additional processing to the prompt before sending it to the API.": "Aplica procesamiento adicional al mensaje antes de enviarlo a la API.", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "Verifica su conexión de API enviando un breve mensaje de prueba. ¡Tenga en cuenta que se le cobrará por ello!", "Test Message": "Men<PERSON><PERSON>", "Auto-connect to Last Server": "Conexión automática al último servidor", "Missing key": "❌ Llave faltante", "Key saved": "✔️ Clave guardada", "View hidden API keys": "Ver claves API ocultas", "AI Response Formatting": "Formato de Respuesta de IA", "Advanced Formatting": "Formato a<PERSON>zado", "Context Template": "Plantilla de Contexto", "Auto-select this preset for Instruct Mode": "Auto-seleccionar este preajuste para el Modo Instrucción", "Story String": "Cadena de historia", "Example Separator": "Separador de ejemplo", "Chat Start": "In<PERSON>o de <PERSON>", "Add Chat Start and Example Separator to a list of stopping strings.": "Agregue Inicio de chat y Separador de ejemplo a una lista de cadenas de parada.", "Use as Stop Strings": "Usar como Cadenas de Parada", "Allow Jailbreak": "<PERSON><PERSON><PERSON>", "Context Order": "Orden de contexto", "Summary": "Resumen", "Author's Note": "Nota del autor", "Example Dialogues": "Diálogos de ejemplo", "Hint": "Pista:", "In-Chat Position not affected": "Los pedidos de resumen y notas del autor solo se ven afectados cuando no tienen establecida una posición en el chat.", "Instruct Mode": "<PERSON><PERSON>", "Enabled": "Activado", "instruct_bind_to_context": "Si está habilitado, las plantillas de contexto se seleccionarán automáticamente según el nombre de la plantilla de instrucción seleccionada o por preferencia.", "Bind to Context": "Vincular al Contexto", "Presets": "<PERSON><PERSON><PERSON><PERSON>", "Auto-select this preset on API connection": "Auto-seleccionar este preajuste en la conexión de la API", "Activation Regex": "Regex de activación", "Wrap Sequences with Newline": "Envolver Secuencias con Nueva línea", "Replace Macro in Sequences": "Reemplazar Macro en Secuencias", "Skip Example Dialogues Formatting": "Omitir Formato de Diálogos de Ejemplo", "Include Names": "Incluir <PERSON>", "Force for Groups and Personas": "Forzar para Grupos y Personas", "System Prompt": "Indicaciones del Sistema", "Instruct Mode Sequences": "Secuencias en Modo Instrucción", "System Prompt Wrapping": "Ajuste de mensajes del sistema", "Inserted before a System prompt.": "Insertado antes de un mensaje del sistema.", "System Prompt Prefix": "Prefijo de aviso del sistema", "Inserted after a System prompt.": "Insertado después de un mensaje del sistema.", "System Prompt Suffix": "Sufijo de mensaje del sistema", "Chat Messages Wrapping": "Envoltura de mensajes de chat", "Inserted before a User message and as a last prompt line when impersonating.": "Insertado antes de un mensaje de usuario y como última línea de aviso al suplantar.", "User Message Prefix": "Prefijo de mensaje de usuario", "Inserted after a User message.": "Insertado después de un mensaje de usuario.", "User Message Suffix": "Sufijo del mensaje del usuario", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "Se inserta antes de un mensaje del Asistente y como última línea de aviso al generar una respuesta de IA.", "Assistant Message Prefix": "Prefijo de mensaje del asistente", "Inserted after an Assistant message.": "Insertado después de un mensaje del Asistente.", "Assistant Message Suffix": "Sufijo de mensaje del asistente", "Inserted before a System (added by slash commands or extensions) message.": "Insertado antes de un mensaje del Sistema (agregado mediante comandos de barra diagonal o extensiones).", "System Message Prefix": "Prefijo de mensaje del sistema", "Inserted after a System message.": "Insertado después de un mensaje del sistema.", "System Message Suffix": "Sufijo de mensaje del sistema", "If enabled, System Sequences will be the same as User Sequences.": "Si está habilitado, las secuencias del sistema serán las mismas que las secuencias del usuario.", "System same as User": "Sistema igual que el usuario", "Misc. Sequences": "Secuencias varias", "Inserted before the first Assistant's message.": "Insertado antes del mensaje del primer Asistente.", "First Assistant Prefix": "Prefijo del primer asistente", "instruct_last_output_sequence": "Se inserta antes del último mensaje del Asistente o como última línea de aviso al generar una respuesta de IA (excepto una función neutral/de sistema).", "Last Assistant Prefix": "Prefijo del último asistente", "Will be inserted as a last prompt line when using system/neutral generation.": "Se insertará como última línea de aviso cuando se utilice sistema/generación neutral.", "System Instruction Prefix": "Prefijo de instrucción del sistema", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "Si se genera una secuencia de parada, todo lo que pase después se eliminará de la salida (inclusive).", "Stop Sequence": "Secuencia de Parada", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "Se insertará al inicio del historial de chat si no comienza con un mensaje de Usuario.", "User Filler Message": "Mensaje de relleno del usuario", "Context Formatting": "Formato de Contexto", "(Saved to Context Template)": "(Guardado en Plantilla de Contexto)", "Always add character's name to prompt": "Siempre agregar el nombre del personaje a las indicaciones", "Generate only one line per request": "Generar solo una línea por solicitud", "Trim Incomplete Sentences": "Recortar Oraciones Incompletas", "Include Newline": "Incluir Nueva línea", "Misc. Settings": "Configuraciones Misceláneas", "Collapse Consecutive Newlines": "Colapsar Nuevas líneas Consecutivas", "Trim spaces": "Recortar espacios", "Tokenizer": "Tokenizador", "Token Padding": "<PERSON><PERSON><PERSON>", "Start Reply With": "Iniciar <PERSON> con", "AI reply prefix": "Prefijo de Respuesta de IA", "Show reply prefix in chat": "Mostrar prefijo de respuesta en el chat", "Non-markdown strings": "Cadenas no Markdown", "separate with commas w/o space between": "separe con comas sin espacio entre ellas", "Custom Stopping Strings": "Cadenas de Detención Personalizadas", "JSON serialized array of strings": "Arreglo de cadenas serializado en JSON", "Replace Macro in Stop Strings": "Reemplazar macro en Cadenas de Detención Personalizadas", "Auto-Continue": "Autocontinuar", "Allow for Chat Completion APIs": "Permitir para APIs de Completado de Chat", "Target length (tokens)": "Longitud objetivo (tokens)", "World Info": "Información de Mundo (WI)", "Locked = World Editor will stay open": "Bloqueado = El Editor de Mundo permanecerá abierto", "Worlds/Lorebooks": "Mundos/Libros de Historia", "Active World(s) for all chats": "Mundo(s) Activo(s) para todos los chats", "-- World Info not found --": "-- Información de Mundo (WI) no encontrada --", "Global World Info/Lorebook activation settings": "Configuración de activación de Global World Info/Lorebook", "Click to expand": "Haga clic para ampliar", "Scan Depth": "Profundidad de escaneo", "Context %": "% de Contexto", "Budget Cap": "Límite de presupuesto", "(0 = disabled)": "(0 = desactivado)", "Scan chronologically until reached min entries or token budget.": "Escanear cronológicamente hasta alcanzar el mínimo de entradas o el presupuesto de tokens.", "Min Activations": "Activaciones mínimas", "Max Depth": "Máxima profundidad", "(0 = unlimited, use budget)": "(0 = ilimitado, use presupuesto)", "Insertion Strategy": "Estrategia de Inserción", "Sorted Evenly": "Ordenado uniformemente", "Character Lore First": "Historia de Personaje Primero", "Global Lore First": "Historia Global Primero", "Entries can activate other entries by mentioning their keywords": "Las entradas pueden activar otras entradas mencionando sus palabras clave", "Recursive Scan": "Escaneo Recursiva", "Lookup for the entry keys in the context will respect the case": "La búsqueda de las claves de entrada en el contexto respetará mayús<PERSON>s y minúsculas", "Case Sensitive": "Sensible a mayúsculas y minúsculas", "If the entry key consists of only one word, it would not be matched as part of other words": "Si la clave de entrada consiste en solo una palabra, no se emparejará como parte de otras palabras", "Match Whole Words": "Coincidir con palabras completas", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "Solo se seleccionarán las entradas con la mayor cantidad de coincidencias clave para el filtrado del grupo de inclusión.", "Use Group Scoring": "Usar puntuación grupal", "Alert if your world info is greater than the allocated budget.": "Alerta si la información de tu mundo es mayor que el presupuesto asignado.", "Alert On Overflow": "Alerta en Desbordamiento", "New": "Nuevo", "or": "o", "--- Pick to Edit ---": "--- <PERSON><PERSON>ccionar para editar ---", "Rename World Info": "Renombrar Información de Mundo (WI)", "Open all Entries": "<PERSON><PERSON><PERSON> To<PERSON> las Entradas", "Close all Entries": "<PERSON><PERSON>r To<PERSON> las Entradas", "New Entry": "Nueva Entrada", "Fill empty Memo/Titles with Keywords": "Llenar Memos/Títulos vacíos con Palabras Clave", "Import World Info": "Importar Información de Mundo (WI)", "Export World Info": "Exportar Información de Mundo (WI)", "Duplicate World Info": "Duplicar Información de Mundo (WI)", "Delete World Info": "Eliminar Información de Mundo (WI)", "Search...": "Buscar...", "Search": "Buscar", "Priority": "Prioridad", "Custom": "Personalizado", "Title A-Z": "Título de A a Z", "Title Z-A": "Título de Z a A", "Tokens ↗": "Tokens ↗", "Tokens ↘": "Tokens ↘", "Depth ↗": "Profundidad ↗", "Depth ↘": "Profundidad ↘", "Order ↗": "Orden ↗", "Order ↘": "Orden ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "Activador% ↗", "Trigger% ↘": "Activador% ↘", "Refresh": "Actualizar", "User Settings": "Configuraciones de usuario", "Simple": "Simple", "Advanced": "<PERSON><PERSON><PERSON>", "UI Language": "Idioma de la UI", "Account": "C<PERSON><PERSON>", "Admin Panel": "Panel de administrador", "Logout": "<PERSON><PERSON><PERSON>", "Search Settings": "Configuración de Búsqueda", "UI Theme": "Tema de interfaz de usuario", "Import a theme file": "Importar un archivo de tema", "Export a theme file": "Exportar un archivo de tema", "Delete a theme": "Eliminar un tema", "Update a theme file": "Actualizar un archivo de tema", "Save as a new theme": "Guardar como nuevo tema", "Avatar Style:": "Estilo <PERSON>", "Circle": "<PERSON><PERSON><PERSON><PERSON>", "Square": "Cuadrado", "Rectangle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Chat Style:": "<PERSON><PERSON><PERSON>:", "Flat": "Departamento\nBurbujas\nDocumento", "Bubbles": "<PERSON><PERSON><PERSON><PERSON>", "Document": "Documento", "Specify colors for your theme.": "Especifique colores para su tema.", "Theme Colors": "Colores del tema", "Main Text": "Texto principal", "Italics Text": "Texto en cursiva", "Underlined Text": "Texto subrayado", "Quote Text": "Texto de cita", "Shadow Color": "Color de sombra", "Chat Background": "Fondo de Chat", "UI Background": "Fondo de IU", "UI Border": "Borde de IU", "User Message Blur Tint": "Tinte de Desenfoque del Mensaje del Usuario", "AI Message Blur Tint": "Tinte de Desenfoque del Mensaje de IA", "Chat Width": "<PERSON><PERSON>", "Width of the main chat window in % of screen width": "Ancho de la ventana principal de chat en % del ancho de la pantalla", "Font Scale": "Tamaño de fuente", "Font size": "Tamaño de fuente", "Blur Strength": "Fuerza de desenfoque", "Blur strength on UI panels.": "Fuerza de desenfoque en los paneles de la interfaz de usuario.", "Text Shadow Width": "<PERSON><PERSON> de sombra de texto", "Strength of the text shadows": "Fuerza de las sombras del texto.", "Disables animations and transitions": "Deshabilita animaciones y transiciones", "Reduced Motion": "Movimiento reducido", "removes blur from window backgrounds": "elimina el desenfoque de los fondos de ventana", "No Blur Effect": "Sin efecto de desenfoque", "Remove text shadow effect": "Eliminar efecto de sombra de texto", "No Text Shadows": "Sin sombras de texto", "Reduce chat height, and put a static sprite behind the chat window": "Reducir la altura del chat y poner un sprite estático detrás de la ventana del chat", "Waifu Mode": "<PERSON><PERSON>", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "Mostrar siempre la lista completa de elementos de contexto de Acciones de Mensaje para mensajes de chat, en lugar de ocultarlos detrás de '...'", "Auto-Expand Message Actions": "Expandir Automáticamente de Acciones de Mensaje", "Alternative UI for numeric sampling parameters with fewer steps": "UI alternativa para parámetros de muestreo numérico con menos pasos", "Zen Sliders": "Deslizadores Zen", "Entirely unrestrict all numeric sampling parameters": "Deslimitar completamente todos los parámetros de muestreo numérico", "Mad Lab Mode": "Modo Laboratorio Loco", "Time the AI's message generation, and show the duration in the chat log": "Medir el tiempo de generación del mensaje de IA y mostrar la duración en el registro de chat", "Message Timer": "Temporizador de mensajes", "Show a timestamp for each message in the chat log": "Mostrar una marca de tiempo para cada mensaje en el registro de chat", "Chat Timestamps": "Marcas de Tiempo del Chat", "Show an icon for the API that generated the message": "Mostrar un icono para la API que generó el mensaje", "Model Icon": "Ícono del Modelo", "Show sequential message numbers in the chat log": "Mostrar números de mensaje secuenciales en el registro de chat", "Message IDs": "IDs de Mensaje", "Hide avatars in chat messages.": "Ocultar avatares en los mensajes de chat.", "Hide Chat Avatars": "Ocultar avatares de chat", "Show the number of tokens in each message in the chat log": "Mostrar el número de tokens en cada mensaje en el registro de chat", "Show Message Token Count": "Mostrar Conteo de Tokens en Mensaje", "Single-row message input area. Mobile only, no effect on PC": "Área de entrada de mensaje de una sola fila. Solo móvil, sin efecto en PC", "Compact Input Area (Mobile)": "Área de Entrada Compacta (Móvil)", "In the Character Management panel, show quick selection buttons for favorited characters": "En el panel de Gestión de Personajes, mostrar botones de selección rápida para personajes favoritos", "Characters Hotswap": "Cambio rápido de personajes", "Enable magnification for zoomed avatar display.": "Habilitar ampliación para visualización de avatar ampliada.", "Avatar Hover Magnification": "Ampliación de desplazamiento del avatar", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "Habilita un efecto de ampliación al pasar el mouse cuando muestra el avatar ampliado después de hacer clic en la imagen de un avatar en el chat.", "Show tagged character folders in the character list": "Mostrar carpetas de personajes etiquetados en la lista de personajes", "Tags as Folders": "Etiquetas como Carpetas", "Tags_as_Folders_desc": "Cambio reciente: las etiquetas deben estar marcadas como carpetas en el menú Administración de etiquetas para que aparezcan como tales. Haga clic aquí para abrirlo.", "Character Handling": "Manipulación de personajes", "If set in the advanced character definitions, this field will be displayed in the characters list.": "Si se establece en las definiciones avanzadas de personajes, este campo se mostrará en la lista de personajes.", "Char List Subheader": "Subtítulo de lista de caracteres", "Character Version": "Versión del Personaje", "Created by": "<PERSON><PERSON>o por", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "Usar coincidencia difusa y buscar personajes en la lista por todos los campos de datos, no solo por una subcadena de nombre", "Advanced Character Search": "Búsqueda Avanzada de Personajes", "If checked and the character card contains a prompt override (System Prompt), use that instead": "Si está marcado y la tarjeta de personaje contiene una anulación de indicación (Indicación del sistema), usar eso en su lugar", "Prefer Character Card Prompt": "Preferir Indicaciones en Tarjeta de Personaje", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "Si está marcado y la tarjeta de personaje contiene una anulación de jailbreak (Instrucciones Post Historial), usar eso en su lugar", "Prefer Character Card Jailbreak": "Preferir Jailbreak en Tarjeta de Personaje", "never_resize_avatars_tooltip": "Evite recortar y cambiar el tamaño de las imágenes de personajes importados. Cuando esté desactivado, recorte/cambie el tamaño a 512x768.", "Never resize avatars": "Nunca redimensionar avatares", "Show actual file names on the disk, in the characters list display only": "Mostrar nombres de archivo reales en el disco, solo en la visualización de la lista de personajes", "Show avatar filenames": "Mostrar nombres de archivo de avatares", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "Solicitar importar etiquetas de tarjeta incrustadas al importar un personaje. De lo contrario, las etiquetas incrustadas se ignoran", "Import Card Tags": "Importar Etiquetas de Tarjetas", "Hide character definitions from the editor panel behind a spoiler button": "Ocultar definiciones de personajes del panel de editor detrás de un botón de spoiler", "Spoiler Free Mode": "Modo Libre de Spoilers", "Miscellaneous": "Varios", "Reload and redraw the currently open chat": "Recargar y volver a dibujar el chat abierto actualmente", "Reload Chat": "<PERSON><PERSON><PERSON>", "Debug Menu": "Menú de depuración", "Smooth Streaming": "Transmisión fluida", "Experimental feature. May not work for all backends.": "Característica experimental. Puede que no funcione para todos los backends.", "Slow": "<PERSON><PERSON>", "Fast": "<PERSON><PERSON><PERSON><PERSON>", "Play a sound when a message generation finishes": "Reproducir un sonido cuando finaliza la generación de un mensaje", "Message Sound": "Sonido de mensaje", "Only play a sound when ST's browser tab is unfocused": "Solo reproducir un sonido cuando la pestaña del navegador de ST no está enfocada", "Background Sound Only": "Solo Sonido de Fondo", "Reduce the formatting requirements on API URLs": "Reducir los requisitos de formato en las URL de API", "Relaxed API URLS": "URLS de API relajadas", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "Pedir importar Información de Mundo (WI)/Libro de Historia para cada nuevo personaje con un Libro de Historia incrustado. Si no está marcado, se mostrará un mensaje breve en su lugar", "Lorebook Import Dialog": "Diálogo de Importación de Libro de Historia", "Restore unsaved user input on page refresh": "Restaurar la entrada de usuario no guardada al actualizar la página", "Restore User Input": "Restaurar Entrada de Usuario", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "Permitir reposicionar ciertos elementos de IU arrastrándolos. Solo PC, sin efecto en móviles", "Movable UI Panels": "Paneles de UI Móviles", "MovingUI preset. Predefined/saved draggable positions": "Preconfiguración MovingUI. Posiciones arrastrables predefinidas/guardadas", "MUI Preset": "Preajuste MUI", "Save movingUI changes to a new file": "Guardar cambios de MovingUI en un nuevo archivo", "Reset MovingUI panel sizes/locations.": "Restablezca los tamaños/ubicaciones del panel MovingUI.", "Apply a custom CSS style to all of the ST GUI": "Aplicar un estilo CSS personalizado a toda la GUI de ST", "Custom CSS": "CSS personalizado", "Expand the editor": "<PERSON>pan<PERSON>", "Chat/Message Handling": "Manipulación de Chat/Mensaje", "# Messages to Load": "# Men<PERSON>je cargar", "The number of chat history messages to load before pagination.": "La cantidad de mensajes del historial de chat que se cargarán antes de la paginación.", "(0 = All)": "(0 = Todos)", "Streaming FPS": "FPS de Transmisión", "Update speed of streamed text.": "Actualizar la velocidad del texto transmitido.", "Example Messages Behavior": "Comportamiento de mensajes de ejemplo", "Gradual push-out": "Expulsión gradual", "Always include examples": "Siempre incluir ejemplos", "Never include examples": "Nunca incluir ejemplos", "Send on Enter": "Enviar al presionar Enter", "Disabled": "Desactivado", "Automatic (PC)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (PC)", "Press Send to continue": "Presionar Enviar para continuar", "Show a button in the input area to ask the AI to continue (extend) its last message": "Mostrar un botón en el área de entrada para pedirle a la IA que continúe (extienda) su último mensaje", "Quick 'Continue' button": "<PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON>' <PERSON>", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "Mostrar botones de flecha en el último mensaje del chat para generar respuestas alternativas de la IA. Tanto PC como móvil", "Swipes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "Permitir el uso de gestos de deslizamiento en el último mensaje del chat para activar la generación de deslizamiento. Solo móvil, sin efecto en PC", "Gestures": "Gestos", "Auto-load Last Chat": "Cargar automáticamente el último chat", "Auto-scroll Chat": "Desplazamiento de Chat Automático", "Save edits to messages without confirmation as you type": "Guardar ediciones en mensajes sin confirmación mientras escribe", "Auto-save Message Edits": "Guardar automáticamente las Ediciones de Mensajes", "Confirm message deletion": "Confirmar elimina<PERSON> de mensaje", "Auto-fix Markdown": "Auto-corregir <PERSON>", "Disallow embedded media from other domains in chat messages": "No permitir medios incrustados de otros dominios en los mensajes de chat.", "Forbid External Media": "Prohibir Medios Externos", "Allow {{char}}: in bot messages": "Permitir {{char}}: en mensajes de bot", "Allow {{user}}: in bot messages": "Permitir {{user}}: en mensajes de bot", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "Omitir la codificación de los caracteres en el texto del mensaje, permitiendo un subconjunto de marcado HTML, así como Markdown", "Show tags in responses": "Mostrar etiquetas en respuestas", "Allow AI messages in groups to contain lines spoken by other group members": "Permitir que los mensajes de IA en grupos contengan líneas habladas por otros miembros del grupo", "Relax message trim in Groups": "Relajar recorte de mensajes en Grupos", "Log prompts to console": "Registrar indicaciones en la consola", "Requests logprobs from the API for the Token Probabilities feature": "Solicita logprobs de la API para la función de Probabilidades de Token", "Request token probabilities": "Solicitar probabilidades de tokens", "Automatically reject and re-generate AI message based on configurable criteria": "Re<PERSON><PERSON> y volver a generar automáticamente el mensaje de IA en función de criterios configurables", "Auto-swipe": "Deslizamiento automático", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "Habilitar la función de deslizamiento automático. La configuración en esta sección solo tiene efecto cuando el deslizamiento automático está habilitado", "Minimum generated message length": "Longitud mínima del mensaje generado", "If the generated message is shorter than these many characters, trigger an auto-swipe": "Si el mensaje generado es más corto que esto, activar un deslizamiento automático", "Blacklisted words": "Pala<PERSON><PERSON> prohi<PERSON>", "words you dont want generated separated by comma ','": "palabras que no desea generar separadas por coma ','", "Blacklisted word count to swipe": "Número de palabras prohibidas para deslizar", "Minimum number of blacklisted words detected to trigger an auto-swipe": "Número mínimo de palabras prohibidas detectadas para activar un deslizamiento automático", "AutoComplete Settings": "Configuración de Autocompletar", "Automatically hide details": "Ocultar detalles automáticamente", "Determines how entries are found for autocomplete.": "Determina cómo se encuentran las entradas para autocompletar.", "Autocomplete Matching": "Pareo", "Starts with": "Comienza con", "Includes": "Incluye", "Fuzzy": "<PERSON><PERSON><PERSON>", "Sets the style of the autocomplete.": "Establece el estilo del autocompletado.", "Autocomplete Style": "<PERSON><PERSON><PERSON>", "Follow Theme": "<PERSON><PERSON><PERSON> te<PERSON>", "Dark": "Oscuro", "Sets the font size of the autocomplete.": "Establece el tamaño de fuente del autocompletado.", "Sets the width of the autocomplete.": "Establece el ancho del autocompletar.", "Autocomplete Width": "<PERSON><PERSON>", "chat input box": "cuadro de entrada de chat", "entire chat width": "todo el ancho del chat", "full window width": "ancho completo de la ventana", "STscript Settings": "Configuración de STscript", "Sets default flags for the STscript parser.": "Establece indicadores predeterminados para el analizador STscript.", "Parser Flags": "Banderas del analizador", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "Cambie a un escape más estricto, lo que permitirá que todos los caracteres delimitadores se escapen con una barra invertida y que las barras invertidas también se escapen.", "STRICT_ESCAPING": "ESTRICT_ESCAPING", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "Reemplace todas las macros {{getvar::}} y {{getglobalvar::}} con variables de ámbito para evitar la doble sustitución de macros.", "REPLACE_GETVAR": "REEMPLAZAR_GETVAR", "Change Background Image": "Cambiar Imagen de Fondo", "Filter": "Filtro", "Automatically select a background based on the chat context": "Seleccionar automáticamente un fondo basado en el contexto del chat", "Auto-select": "Auto-seleccionar", "System Backgrounds": "Fondos del Sistema", "Chat Backgrounds": "Fondos de Chat", "bg_chat_hint_1": "Fondos de chat generados con el", "bg_chat_hint_2": "La extensión aparecerá aquí.", "Extensions": "Extensiones", "Notify on extension updates": "Notificar sobre actualizaciones de extensión", "Manage extensions": "Gestionar extensiones", "Import Extension From Git Repo": "Importar extensión desde repositorio Git", "Install extension": "Instalar extensión", "Extras API:": "API adicionales:", "Auto-connect": "Conexión automática", "Extras API URL": "URL de API adicional", "Extras API key (optional)": "Clave API de Extras (opcional)", "Persona Management": "Gestión de Personas", "How do I use this?": "¿Cómo uso esto?", "Click for stats!": "¡Haz clic para estadísticas!", "Usage Stats": "Estadísticas de uso", "Backup your personas to a file": "Realiza una copia de seguridad de tus personajes en un archivo", "Backup": "<PERSON><PERSON><PERSON>", "Restore your personas from a file": "Restaura tus personajes desde un archivo", "Restore": "Restaurar", "Create a dummy persona": "<PERSON>rear una persona ficticia", "Create": "<PERSON><PERSON><PERSON>", "Toggle grid view": "Alternar vista de cuadrícula", "No persona description": "[Sin descripción]", "Name": "Nombre", "Enter your name": "Ingrese su nombre", "Click to set a new User Name": "Haga clic para establecer un nuevo Nombre de Usuario", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "Haga clic para bloquear su persona seleccionada en el chat actual. Haga clic nuevamente para quitar el bloqueo.", "Click to set user name for all messages": "Haga clic para establecer el nombre de usuario para todos los mensajes", "Persona Description": "Descripción de Persona", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "Ejemplo: [{{user}} es una chica gata rumana de 28 años.]", "Tokens persona description": "Descripción de tokens", "Position:": "Posición:", "In Story String / Prompt Manager": "En la Cadena de Historia / Administrador de Indicaciones", "Top of Author's Note": "Parte Superior de la Nota de Autor", "Bottom of Author's Note": "Parte Inferior de la Nota de Autor", "In-chat @ Depth": "En el chat @ Depth", "Depth:": "Profundidad:", "Role:": "Role:", "System": "Sistema", "User": "Usuario", "Assistant": "<PERSON><PERSON><PERSON>", "Show notifications on switching personas": "Mostrar notificaciones al cambiar de personas", "Character Management": "Gestión de Personajes", "Locked = Character Management panel will stay open": "Bloqueado = El panel de Gestión de Personajes permanecerá abierto", "Select/Create Characters": "<PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>", "Favorite characters to add them to HotSwaps": "Marcar personajes como favoritos para añadirlos a HotSwaps", "Token counts may be inaccurate and provided just for reference.": "El conteo de tokens pueden ser inexacto y se proporcionan solo como referencia.", "Total tokens": "fichas totales", "Calculating...": "Calculador...", "Tokens": "Tokens", "Permanent tokens": "<PERSON><PERSON><PERSON>", "Permanent": "Permanente", "About Token 'Limits'": "Acerca de los 'límites' de tokens", "Toggle character info panel": "Alternar panel de información de personaje", "Name this character": "Nombre de este personaje", "extension_token_counter": "Fichas:", "Click to select a new avatar for this character": "Haga clic para seleccionar un nuevo avatar para este personaje", "Add to Favorites": "Agregar a Favoritos", "Advanced Definition": "Definición Avanzada", "Character Lore": "Historia (Trasfondo) del personaje", "Chat Lore": "Historia del chat", "Export and Download": "Exportar y descargar", "Duplicate Character": "Duplicar personaje", "Create Character": "<PERSON><PERSON><PERSON> personaje", "Delete Character": "Eliminar personaje", "More...": "Más...", "Link to World Info": "Enlazar a Información de Mundo (WI)", "Import Card Lore": "Importar Historia de Tarjeta", "Scenario Override": "Anulación de Escenario", "Convert to Persona": "Convertir en Persona", "Rename": "Renombrar", "Link to Source": "Enlace a la fuente", "Replace / Update": "Reemplazar / Actualizar", "Import Tags": "Etiquetas de importación", "Search / Create Tags": "Buscar / Crear <PERSON>", "View all tags": "Ver todas las etiquetas", "Creator's Notes": "<PERSON><PERSON>reador", "Show / Hide Description and First Message": "Mostrar / Ocultar Descripción y Primer Mensaje", "Character Description": "Descripción del Personaje", "Click to allow/forbid the use of external media for this character.": "Haz clic para permitir/prohibir el uso de medios externos para este personaje.", "Ext. Media": "ext. Medios de comunicación", "Describe your character's physical and mental traits here.": "Describa aquí las características físicas y mentales de su personaje.", "First message": "Primer <PERSON><PERSON><PERSON>", "Click to set additional greeting messages": "Haz clic para establecer mensajes de saludo adicionales", "Alt. Greetings": "<PERSON>. <PERSON>", "This will be the first message from the character that starts every chat.": "Este será el primer mensaje del personaje que inicia cada chat.", "Group Controls": "Controles de grupo", "Chat Name (Optional)": "Nombre del Chat (Opcional)", "Click to select a new avatar for this group": "Haz clic para seleccionar un nuevo avatar para este grupo", "Group reply strategy": "Estrategia de respuesta de grupo", "Natural order": "Orden natural", "List order": "Orden de lista", "Group generation handling mode": "Modo de manejo de generación de grupos", "Swap character cards": "Intercambiar tarjetas de personajes", "Join character cards (exclude muted)": "Unirse a tarjetas de personajes (excluir silenciadas)", "Join character cards (include muted)": "Unirse a tarjetas de personajes (incluidas silenciadas)", "Inserted before each part of the joined fields.": "Insertado antes de cada parte de los campos unidos.", "Join Prefix": "Unirse al prefijo", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "Cuando se selecciona 'Unir tarjetas de personajes', todos los campos respectivos de los personajes se unen.\rEsto significa que en la cadena de la historia, por ejemplo, todas las descripciones de los personajes se unirán en un texto grande.\rSi desea que esos campos estén separados, puede definir un prefijo o sufijo aquí.\r\rEste valor admite macros normales y también reemplazará {{char}} con el nombre del personaje relevante y <FIELDNAME> con el nombre de la parte (por ejemplo: descripción, personalidad, escenario, etc.)", "Inserted after each part of the joined fields.": "Insertado después de cada parte de los campos unidos.", "Join Suffix": "Unirse al sufijo", "Set a group chat scenario": "Establecer un escenario de chat grupal", "Click to allow/forbid the use of external media for this group.": "Haga clic para permitir/prohibir el uso de medios externos para este grupo.", "Restore collage avatar": "Restaurar avatar de collage", "Allow self responses": "Permitir auto respuestas", "Auto Mode": "Modo automá<PERSON>", "Auto Mode delay": "Retardo del modo automático", "Hide Muted Member Sprites": "Ocultar sprites de miembros silenciados", "Current Members": "Miembros actuales", "Add Members": "Agregar miembros", "Create New Character": "Crear Nuevo Personaje", "Import Character from File": "Importar Personaje desde Archivo", "Import content from external URL": "Importar contenido desde URL externa", "Create New Chat Group": "Crear Nuevo Grupo de Chat", "Characters sorting order": "Orden de clasificación de personajes", "A-Z": "A-Z", "Z-A": "Z-A", "Newest": "Más Reciente", "Oldest": "<PERSON>ás <PERSON>", "Favorites": "<PERSON><PERSON><PERSON><PERSON>", "Recent": "Reciente", "Most chats": "<PERSON><PERSON>", "Least chats": "<PERSON><PERSON>", "Most tokens": "Más tokens", "Least tokens": "Menos tokens", "Random": "Aleat<PERSON>", "Toggle character grid view": "Alternar vista de cuadrícula de personajes", "Bulk_edit_characters": "Editar personaje<PERSON> masi<PERSON>e", "Bulk select all characters": "Seleccionar de forma masiva todos los personajes", "Bulk delete characters": "Eliminar personajes masi<PERSON>e", "popup-button-save": "Guardar", "popup-button-yes": "Sí", "popup-button-no": "No", "popup-button-cancel": "<PERSON><PERSON><PERSON>", "popup-button-import": "Importar", "Advanced Definitions": "Definiciones avanzadas", "Prompt Overrides": "Anulaciones rápidas", "(For Chat Completion and Instruct Mode)": "(Para completar el chat y el modo de instrucción)", "Insert {{original}} into either box to include the respective default prompt from system settings.": "Inserte {{original}} en cualquiera de las casillas para incluir las indicaciones predeterminadas respectivas de la configuración del sistema.", "Main Prompt": "Indicaciones Principales", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "Cualquier contenido aquí reemplazará las Indicaciones Principales predeterminada utilizada para este personaje. (especificación v2: system_prompt)", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "Cualquier contenido aquí reemplazará las Indicaciones de Jailbreak predeterminada utilizada para este personaje. (especificación v2: post_history_instructions)", "Creator's Metadata (Not sent with the AI prompt)": "Metadatos del Creador (No enviados con las indicaciones de la IA)", "Creator's Metadata": "Metadatos del creador", "(Not sent with the AI Prompt)": "(No se envía con el mensaje AI)", "Everything here is optional": "Todo aquí es opcional", "(Botmaker's name / Contact Info)": "(Nombre del creador del bot / Información de contacto)", "(If you want to track character versions)": "(Si desea rastrear versiones de personajes)", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "(Describa el bot, dé consejos de uso o enumere los modelos de chat en los que se ha probado. Esto se mostrará en la lista de personajes.)", "Tags to Embed": "Etiquetas para Incrustar", "(Write a comma-separated list of tags)": "(Escriba una lista de etiquetas separadas por comas)", "Personality summary": "Resumen de personalidad", "(A brief description of the personality)": "(Una breve descripción de la personalidad)", "Scenario": "Escenario", "(Circumstances and context of the interaction)": "(Circunstancias y contexto de la interacción)", "Character's Note": "Nota del personaje", "(Text to be inserted in-chat @ designated depth and role)": "(El texto se insertará en el chat en la profundidad y función designadas)", "@ Depth": "@ Profundidad", "Role": "Role", "Talkativeness": "Habladuría", "How often the character speaks in group chats!": "¡Con qué frecuencia habla el personaje en los chats grupales!", "How often the character speaks in": "¿Con qué frecuencia habla el personaje?", "group chats!": "chats grupales!", "Shy": "<PERSON><PERSON><PERSON><PERSON>", "Normal": "Normal", "Chatty": "Parlan<PERSON><PERSON>", "Examples of dialogue": "Ejemplos de diálogo", "Important to set the character's writing style.": "Importante para establecer el estilo de escritura del personaje.", "(Examples of chat dialog. Begin each example with START on a new line.)": "(Ejemplos de diálogo de chat. Comience cada ejemplo con START en una nueva línea.)", "Save": "Guardar", "Chat History": "Historial de chat", "Import Chat": "Importar chat", "Copy to system backgrounds": "Copiar a fondos del sistema", "Rename background": "Cambiar nombre de fondo", "Lock": "<PERSON><PERSON><PERSON>", "Unlock": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Delete background": "Eliminar fondo", "Chat Scenario Override": "Anulación del escenario de chat", "Remove": "Eliminar", "Type here...": "Escriba aquí...", "Chat Lorebook": "Libro de conocimientos de chat para", "Chat Lorebook for": "Libro de conocimientos de chat para", "chat_world_template_txt": "Una información mundial seleccionada estará vinculada a este chat. Al generar una respuesta de IA,\n                    se combinará con las entradas de los libros de historia globales y de personajes.", "Select a World Info file for": "Seleccionar un archivo de Información de Mundo (WI) para", "Primary Lorebook": "Libro de Historia primario", "A selected World Info will be bound to this character as its own Lorebook.": "Una Información de Mundo (WI) seleccionada se vinculará a este personaje como su propio Libro de Historia.", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "Al generar una respuesta de IA, se combinará con las entradas de un selector global de Información Mundial.", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "Exportar un personaje también exportaría el archivo de Libro de Historia seleccionado incrustado en los datos JSON.", "Additional Lorebooks": "Libros de Historia Adicionales", "Associate one or more auxillary Lorebooks with this character.": "Asociar uno o más Libros de Historia auxiliares con este personaje.", "NOTE: These choices are optional and won't be preserved on character export!": "NOTA: ¡Estas opciones son opcionales y no se conservarán al exportar el personaje!", "Rename chat file": "Renombrar archivo de chat", "Export JSONL chat file": "Exportar archivo de chat JSONL", "Download chat as plain text document": "Descargar chat como documento de texto sin formato", "Delete chat file": "Eliminar archivo de chat", "Use tag as folder": "Etiquetar como carpeta", "Hide on character card": "Ocultar en la tarjeta del personaje", "Delete tag": "Eliminar etiqueta", "Entry Title/Memo": "Título/Memo", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled": "Estado de entrada a WI:\r🔵 Constante\r🟢Normal\r🔗 Vectorizado\r❌ Deshabilitado", "WI_Entry_Status_Constant": "<PERSON><PERSON><PERSON>", "WI_Entry_Status_Normal": "Normal", "WI_Entry_Status_Vectorized": "Vectorizado", "WI_Entry_Status_Disabled": "Desactivado", "T_Position": "↑Char: antes de definiciones de personajes\n↓Char: después de definiciones de personajes\n↑AN: antes de notas del autor\n↓AN: después de notas del autor\n@D: en profundidad", "Before Char Defs": "Antes de Def. <PERSON>e", "After Char Defs": "Después de Def. de Personaje", "Before EM": "↑EM", "After EM": "↓EM", "Before AN": "Antes de AN", "After AN": "Después de AN", "at Depth System": "@D ⚙️", "at Depth User": "@D 👤", "at Depth AI": "@D 🤖", "Depth": "Profundidad", "Order:": "Orden:", "Order": "Orden:", "Trigger %:": "Desencadenar %:", "Probability": "Probabilidad", "Duplicate world info entry": "Entrada de información mundial duplicada", "Delete world info entry": "Eliminar entrada de información mundial", "Comma separated (required)": "Separado por comas (requerido)", "Primary Keywords": "Palabras Clave Primarias", "Keywords or Regexes": "Palabras clave o expresiones regulares", "Comma separated list": "Lista separada por comas", "Switch to plaintext mode": "Cambiar al modo de texto sin formato", "Logic": "Lógica", "AND ANY": "Y CUALQUIERA", "AND ALL": "Y TODOS", "NOT ALL": "NO TODOS", "NOT ANY": "NO CUALQUIERA", "(ignored if empty)": "(ignorado si está vacío)", "Optional Filter": "Filtro Opcional", "Keywords or Regexes (ignored if empty)": "Palabras clave o expresiones regulares (se ignoran si están vacías)", "Comma separated list (ignored if empty)": "Lista separada por comas (ignorada si está vacía)", "Use global setting": "Usar configuración global", "Case-Sensitive": "Sensible a mayúsculas y minúsculas", "Yes": "Sí", "No": "No", "Can be used to automatically activate Quick Replies": "Se puede utilizar para activar automáticamente las Respuestas rápidas.", "Automation ID": "ID de automatización", "( None )": "( <PERSON><PERSON><PERSON> )", "Content": "Contenido", "Exclude from recursion": "Excluir de la recursión", "Prevent further recursion (this entry will not activate others)": "Evitar una mayor recurrencia (esta entrada no activará otras)", "Delay until recursion (this entry can only be activated on recursive checking)": "Retraso hasta la recursión (esta entrada solo se puede activar en la comprobación recursiva)", "What this keyword should mean to the AI, sent verbatim": "Lo que este palabra clave debería significar para la IA, enviado textualmente", "Filter to Character(s)": "<PERSON>ltrar a Personaje(s)", "Character Exclusion": "Exclusión de Personaje", "-- Characters not found --": "-- <PERSON>aj<PERSON> no encontrados --", "Inclusion Group": "Grupo de Inclusión", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "Los grupos de inclusión garantizan que solo se active una entrada de un grupo a la vez, si se activan varias.\rAdmite múltiples grupos separados por comas.\r\rDocumentación: World Info - Grupo de Inclusión", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "Priorizar esta entrada: cuando está marcada, esta entrada tiene prioridad entre todas las selecciones.\rSi se priorizan varios, se elige el que tenga el 'Orden' más alto.", "Only one entry with the same label will be activated": "Solo se activará una entrada con la misma etiqueta", "A relative likelihood of entry activation within the group": "Una probabilidad relativa de activación de entrada dentro del grupo.", "Group Weight": "Peso del grupo", "Selective": "Selectivo", "Use Probability": "Usar Probabilidad", "Add Memo": "Agregar nota", "Text or token ids": "Texto o [identificadores de token]", "close": "cerca", "prompt_manager_edit": "<PERSON><PERSON>", "prompt_manager_name": "Nombre", "A name for this prompt.": "Un nombre para este mensaje.", "To whom this message will be attributed.": "A quién se le atribuirá este mensaje.", "AI Assistant": "Asistente de IA", "prompt_manager_position": "Posición", "Next to other prompts (relative) or in-chat (absolute).": "Junto a otras indicaciones (relativa) o en el chat (absoluta).", "prompt_manager_relative": "Relativo", "prompt_manager_depth": "Profundidad", "0 = after the last message, 1 = before the last message, etc.": "0 = después del último mensaje, 1 = antes del último mensaje, etc.", "Prompt": "Indicar", "The prompt to be sent.": "El mensaje que se enviará.", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "Este mensaje no puede ser anulado por tarjetas de personaje, incluso si se prefieren las anulaciones.", "prompt_manager_forbid_overrides": "Prohibir anulaciones", "reset": "reiniciar", "save": "guardar", "This message is invisible for the AI": "Este mensaje es invisible para la IA", "Message Actions": "Acciones de mensajes", "Translate message": "<PERSON><PERSON><PERSON><PERSON>", "Generate Image": "Generar imagen", "Narrate": "<PERSON><PERSON><PERSON>", "Exclude message from prompts": "Excluir mensaje de las indicaciones", "Include message in prompts": "Incluir mensaje en las indicaciones", "Embed file or image": "Insertar archivo o imagen", "Create checkpoint": "Crear punto de control", "Create Branch": "<PERSON><PERSON><PERSON>", "Copy": "Copiar", "Open checkpoint chat": "Abrir chat de punto de control", "Edit": "<PERSON><PERSON>", "Confirm": "Confirmar", "Copy this message": "Copiar este mensaje", "Delete this message": "Eliminar este mensaje", "Move message up": "Mover mensaje hacia arriba", "Move message down": "Mover mensaje hacia abajo", "Enlarge": "Ampliar", "Welcome to SillyTavern!": "¡Bienvenido a SillyTavern!", "welcome_message_part_1": "Lee<PERSON> el", "welcome_message_part_2": "Documentación oficial", "welcome_message_part_3": null, "welcome_message_part_4": "Tipo", "welcome_message_part_5": "en el chat para comandos y macros.", "welcome_message_part_6": "Disfruta el", "Discord server": "<PERSON><PERSON><PERSON>", "welcome_message_part_7": "para información y anuncios.", "SillyTavern is aimed at advanced users.": "SillyTavern está dirigido a usuarios avanzados.", "If you're new to this, enable the simplified UI mode below.": "Si eres nuevo en esto, habilita el modo de interfaz de usuario simplificado a continuación.", "Change it later in the 'User Settings' panel.": "Cámbielo más tarde en el panel 'Configuración de usuario'.", "Enable simple UI mode": "Habilitar el modo UI simple", "Looking for AI characters?": "¿Buscas personajes de IA?", "onboarding_import": "Importar", "from supported sources or view": "desde fuentes compatibles o ver", "Sample characters": "Personajes de muestra", "Your Persona": "<PERSON>", "Before you get started, you must select a persona name.": "<PERSON><PERSON> de comenzar, debe seleccionar un nombre de persona.", "welcome_message_part_8": "Esto se puede cambiar en cualquier momento a través del", "welcome_message_part_9": "icono.", "Persona Name:": "Nombre de persona:", "Temporarily disable automatic replies from this character": "Desactivar temporalmente las respuestas automáticas de este personaje", "Enable automatic replies from this character": "Activar respuestas automáticas de este personaje", "Trigger a message from this character": "Desencadenar un mensaje de este personaje", "Move up": "Mover hacia arriba", "Move down": "Mover hacia abajo", "View character card": "Ver tarjeta de personaje", "Remove from group": "Eliminar del grupo", "Add to group": "Agregar al grupo", "Alternate Greetings": "Saludos alternativos", "Alternate_Greetings_desc": "Estos se mostrarán al deslizar el dedo en el primer mensaje al iniciar un nuevo chat.\n                Los miembros del grupo pueden seleccionar uno de ellos para iniciar la conversación.", "Alternate Greetings Hint": "¡Haga clic en el botón para comenzar!", "(This will be the first message from the character that starts every chat)": "(Este será el primer mensaje del personaje que inicia cada chat)", "Forbid Media Override explanation": "Capacidad del personaje/grupo actual de utilizar medios externos en los chats.", "Forbid Media Override subtitle": "Medios: imágenes, vídeos, audio. Externo: no alojado en el servidor local.", "Always forbidden": "siempre prohibido", "Always allowed": "Siempre permitido", "View contents": "Ver contenidos", "Remove the file": "Eliminar el archivo", "Unique to this chat": "Único para este chat", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "Los puntos de control heredan la Nota de su padre y se pueden cambiar individualmente después de eso.", "Include in World Info Scanning": "Incluir en el escaneo de información mundial", "Before Main Prompt / Story String": "Antes del mensaje principal/cadena de historia", "After Main Prompt / Story String": "Después del mensaje principal/cadena de historia", "as": "como", "Insertion Frequency": "Frecuencia de inserción", "(0 = Disable, 1 = Always)": "(0 = Desactivar, 1 = Siempre)", "User inputs until next insertion:": "Entradas del usuario hasta la próxima inserción:", "Character Author's Note (Private)": "Nota del autor del personaje (privado)", "Won't be shared with the character card on export.": "No se compartirá con la tarjeta de personaje al exportar.", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "Se agregará automáticamente como nota del autor para este personaje. Se utilizará en grupos, pero\n                            No se puede modificar cuando un chat grupal está abierto.", "Use character author's note": "Utilice la nota del autor del personaje.", "Replace Author's Note": "Reemplazar la nota del autor", "Default Author's Note": "Nota del autor predeterminada", "Will be automatically added as the Author's Note for all new chats.": "Se agregará automáticamente como nota del autor para todos los chats nuevos.", "Chat CFG": "Chat CFG", "1 = disabled": "1 = deshabilitado", "write short replies, write replies using past tense": "escribir respuestas breves, escribir respuestas usando tiempo pasado", "Positive Prompt": "Aviso positivo", "Use character CFG scales": "Utilice escalas CFG de caracteres", "Character CFG": "Carácter CFG", "Will be automatically added as the CFG for this character.": "Se agregará automáticamente como CFG para este personaje.", "Global CFG": "GFC global", "Will be used as the default CFG options for every chat unless overridden.": "Se utilizarán como opciones CFG predeterminadas para cada chat a menos que se anulen.", "CFG Prompt Cascading": "Cascada de avisos CFG", "Combine positive/negative prompts from other boxes.": "Combine indicaciones positivas/negativas de otros cuadros.", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "<PERSON>r eje<PERSON><PERSON>, al marcar las casillas de chat, global y de caracteres se combinan todas las indicaciones negativas en una cadena separada por comas.", "Always Include": "Incluir siempre", "Chat Negatives": "Negativos del chat", "Character Negatives": "Carácter negativo", "Global Negatives": "Negativos globales", "Custom Separator:": "Separador personalizado:", "Insertion Depth:": "Profundidad de inserción:", "Token Probabilities": "Probabilidades de token", "Select a token to see alternatives considered by the AI.": "Seleccione un token para ver las alternativas consideradas por la IA.", "Not connected to API!": "¡No conectado a la API!", "Type a message, or /? for help": "Escribe un mensaje o /? para obtener ayuda", "Continue script execution": "Continuar la ejecución del script", "Pause script execution": "Pausar la ejecución del script", "Abort script execution": "Cancelar la ejecución del script", "Abort request": "<PERSON><PERSON><PERSON> solicitud", "Continue the last message": "Continuar el último mensaje", "Send a message": "Enviar un mensaje", "Close chat": "<PERSON><PERSON><PERSON> chat", "Toggle Panels": "Paneles de alternancia", "Back to parent chat": "<PERSON><PERSON> al chat principal", "Save checkpoint": "Guardar punto de control", "Convert to group": "Convertir a grupo", "Start new chat": "Iniciar nuevo chat", "Manage chat files": "Gestionar archivos de chat", "Delete messages": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Regenerate": "<PERSON><PERSON><PERSON>", "Ask AI to write your message for you": "Pídele a la IA que escriba tu mensaje por ti", "Impersonate": "Suplantar", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "Bind user name to that avatar": "Vincular nombre de usuario a ese avatar", "Change persona image": "<PERSON><PERSON><PERSON> imagen de persona", "Select this as default persona for the new chats.": "<PERSON><PERSON><PERSON><PERSON>r esta persona como predeterminada para los nuevos chats.", "Delete persona": "Eliminar persona", "These characters are the winners of character design contests and have outstandable quality.": "Estos personajes son los ganadores de concursos de diseño de personajes y tienen una calidad sobresaliente.", "Contest Winners": "Ganadores del concurso", "These characters are the finalists of character design contests and have remarkable quality.": "Estos personajes son finalistas de concursos de diseño de personajes y tienen una calidad destacable.", "Featured Characters": "<PERSON>aj<PERSON>", "Attach a File": "Adjuntar un archivo", "Open Data Bank": "Banco de datos abiertos", "Enter a URL or the ID of a Fandom wiki page to scrape:": "Ingresa una URL o el ID de una página wiki de Fandom para extraer:", "Examples:": "Ejemplos:", "Example:": "Ejemplo:", "Single file": "Fila india", "All articles will be concatenated into a single file.": "Todos los artículos se concatenarán en un único archivo.", "File per article": "Archivo por artículo", "Each article will be saved as a separate file.": "No recomendado. Cada artículo se guardará como un archivo independiente.", "Data Bank": "Banco de datos", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "Estos archivos estarán disponibles para extensiones que admitan archivos adjuntos (por ejemplo, Vector Storage).", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "Tipos de archivos admitidos: texto sin formato, PDF, Markdown, HTML, EPUB.", "Drag and drop files here to upload.": "Arrastre y suelte archivos aquí para cargarlos.", "Date (Newest First)": "Fecha (la más reciente primero)", "Date (Oldest First)": "Fecha (la más antigua primero)", "Name (A-Z)": "Nombre (A-Z)", "Name (Z-A)": "Nombre (Z-A)", "Size (Smallest First)": "Tamaño (el más pequeño primero)", "Size (Largest First)": "Tamaño (el más grande primero)", "Bulk Edit": "Edición masiva", "Select All": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "Select None": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Global Attachments": "Adjuntos globales", "These files are available for all characters in all chats.": "Estos archivos están disponibles para todos los personajes en todos los chats.", "Character Attachments": "<PERSON><PERSON><PERSON> de <PERSON>je<PERSON>", "These files are available the current character in all chats they are in.": "Estos archivos están disponibles para el personaje actual en todos los chats en los que se encuentra.", "Saved locally. Not exported.": "Guardado localmente. No exportado.", "Chat Attachments": "<PERSON><PERSON><PERSON>", "These files are available to all characters in the current chat.": "Estos archivos están disponibles para todos los personajes en el chat actual.", "Enter a base URL of the MediaWiki to scrape.": "Ingrese una URL base de MediaWiki para extraer.", "Don't include the page name!": "¡No incluyas el nombre de la página!", "Enter web URLs to scrape (one per line):": "Ingrese las URL web para extraer (una por línea):", "Enter a video URL to download its transcript.": "Ingrese la URL o ID de un video para descargar su transcripción.", "Expression API": "Extras\nlocales\nLLM", "ext_sum_with": "<PERSON><PERSON><PERSON> con:", "ext_sum_main_api": "API principal", "ext_sum_current_summary": "Resumen actual:", "ext_sum_restore_previous": "Restaurar anterior", "ext_sum_memory_placeholder": "El resumen se generará aquí...", "Trigger a summary update right now.": "<PERSON><PERSON><PERSON>", "ext_sum_force_text": "<PERSON><PERSON><PERSON>", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "Deshabilite las actualizaciones resumidas automáticas. Mientras está en pausa, el resumen permanece tal como está. Aún puedes forzar una actualización presionando el botón Resumir ahora (que solo está disponible con la API principal).", "ext_sum_pause": "Pausa", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "Omita la información mundial y la nota del autor del texto que se va a resumir. Solo tiene efecto cuando se utiliza la API principal. La API de Extras siempre omite WI/AN.", "ext_sum_no_wi_an": "Sin conexión Wi/AN", "ext_sum_settings_tip": "Editar mensaje de resumen, posición de inserción, etc.", "ext_sum_settings": "Configuración de resumen", "ext_sum_prompt_builder": "constructor <PERSON><PERSON><PERSON><PERSON>", "ext_sum_prompt_builder_1_desc": "La extensión creará su propio mensaje utilizando mensajes que aún no se han resumido. Bloquea el chat hasta que se genere el resumen.", "ext_sum_prompt_builder_1": "Crudo, bloqueando", "ext_sum_prompt_builder_2_desc": "La extensión creará su propio mensaje utilizando mensajes que aún no se han resumido. No bloquea el chat mientras se genera el resumen. No todos los servidores admiten este modo.", "ext_sum_prompt_builder_2": "<PERSON><PERSON><PERSON>, sin bloqueo", "ext_sum_prompt_builder_3_desc": "La extensión utilizará el generador de mensajes principal habitual y le agregará la solicitud de resumen como último mensaje del sistema.", "ext_sum_prompt_builder_3": "Clásico, bloqueo", "Summary Prompt": "<PERSON><PERSON><PERSON>", "ext_sum_restore_default_prompt_tip": "Restaurar mensaje predeterminado", "ext_sum_prompt_placeholder": "Este mensaje se enviará a AI para solicitar la generación del resumen. {{words}} se resolverá en el parámetro 'Número de palabras'.", "ext_sum_target_length_1": "Longitud del resumen objetivo", "ext_sum_target_length_2": null, "ext_sum_target_length_3": "palabras)", "ext_sum_api_response_length_1": "Longitud de la respuesta API", "ext_sum_api_response_length_2": null, "ext_sum_api_response_length_3": "fichas)", "ext_sum_0_default": "0 = predeterminado", "ext_sum_raw_max_msg": "[Sin procesar] Máximo de mensajes por solicitud", "ext_sum_0_unlimited": "0 = ilimitado", "Update frequency": "Frecuencia de actualización", "ext_sum_update_every_messages_1": "Actualizar cada", "ext_sum_update_every_messages_2": "mensa<PERSON><PERSON>", "ext_sum_0_disable": "0 = desactivar", "ext_sum_auto_adjust_desc": "Intente ajustar automáticamente el intervalo en función de las métricas del chat.", "ext_sum_update_every_words_1": "Actualizar cada", "ext_sum_update_every_words_2": "palabras", "ext_sum_both_sliders": "Si ambos controles deslizantes son distintos de cero, ambos activarán actualizaciones resumidas en sus respectivos intervalos.", "ext_sum_injection_template": "Plantilla de inyección", "ext_sum_memory_template_placeholder": "{{summary}} se resolverá en el contenido del resumen actual.", "ext_sum_injection_position": "Posición de inyección", "How many messages before the current end of the chat.": "<PERSON>uán<PERSON> mensajes antes del final actual del chat.", "ext_regex_title": "expresión regular", "ext_regex_new_global_script": "+ <PERSON><PERSON><PERSON>", "ext_regex_new_scoped_script": "+ Alcance", "ext_regex_import_script": "Importar", "ext_regex_global_scripts": "Guiones globales", "ext_regex_global_scripts_desc": "Disponible para todos los personajes. Guardado en la configuración local.", "ext_regex_scoped_scripts": "<PERSON><PERSON><PERSON> con alcance", "ext_regex_scoped_scripts_desc": "Sólo disponible para este personaje. Guardado en los datos de la tarjeta.", "Regex Editor": "Editor de expresiones regulares", "Test Mode": "<PERSON><PERSON>", "ext_regex_desc": "Regex es una herramienta para buscar/reemplazar cadenas usando expresiones regulares. Si desea obtener más información, haga clic en ? al lado del título.", "Input": "Aporte", "ext_regex_test_input_placeholder": "Escriba aquí...", "Output": "Producción", "ext_regex_output_placeholder": "Vacío", "Script Name": "Nombre del guión", "Find Regex": "encontrar expresiones regulares", "Replace With": "Reem<PERSON>la<PERSON> con", "ext_regex_replace_string_placeholder": "Utilice {{match}} para incluir el texto coincidente de Find Regex o $1, $2, etc. para grupos de captura.", "Trim Out": "Recortar", "ext_regex_trim_placeholder": "Recorta globalmente cualquier pieza no deseada de una coincidencia de expresiones regulares antes de reemplazarla. Separe cada elemento con una entrada.", "ext_regex_affects": "Afecta", "ext_regex_user_input": "Entrada del usuario", "ext_regex_ai_output": "Salida de IA", "Slash Commands": "Comandos de bar<PERSON>", "ext_regex_min_depth_desc": "Cuando se aplica a indicaciones o visualización, solo afecta a los mensajes que tienen al menos N niveles de profundidad. 0 = último mensaje, 1 = penúltimo mensaje, etc. Solo cuenta las entradas WI @Depth y los mensajes utilizables, es decir, no ocultos ni del sistema.", "Min Depth": "Profundidad mínima", "ext_regex_min_depth_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "ext_regex_max_depth_desc": "Cuando se aplica a indicaciones o visualización, solo afecta a los mensajes que no tengan más de N niveles de profundidad. 0 = último mensaje, 1 = penúltimo mensaje, etc. Solo cuenta las entradas WI @Depth y los mensajes utilizables, es decir, no ocultos ni del sistema.", "ext_regex_other_options": "Otras opciones", "Only Format Display": "Sólo formato de visualización", "ext_regex_only_format_prompt_desc": "El historial de chat no cambiará, solo el aviso cuando se envía la solicitud (al generarse).", "Only Format Prompt (?)": "Sólo formato de mensaje", "Run On Edit": "Ejecutar al editar", "ext_regex_substitute_regex_desc": "Sustituya {{macros}} en Find Regex antes de ejecutarlo", "Substitute Regex": "Sustituir expresión regular", "ext_regex_import_target": "Importar a:", "ext_regex_disable_script": "Deshabilitar secuencia de comandos", "ext_regex_enable_script": "Habilitar secuencia de comandos", "ext_regex_edit_script": "<PERSON><PERSON>", "ext_regex_move_to_global": "Pasar a scripts globales", "ext_regex_move_to_scoped": "Pasar a secuencias de comandos con ámbito", "ext_regex_export_script": "Exportar script", "ext_regex_delete_script": "Eliminar gui<PERSON>", "Trigger Stable Diffusion": "Activar difusión estable", "sd_Yourself": "Tú mismo", "sd_Your_Face": "Tu cara", "sd_Me": "A mí", "sd_The_Whole_Story": "La historia completa", "sd_The_Last_Message": "El último mensaje", "sd_Raw_Last_Message": "Último mensaje sin procesar", "sd_Background": "Fondo", "Image Generation": "Generación de imágenes", "sd_refine_mode": "Permitir editar mensajes manualmente antes de enviarlos a la API de generación", "sd_refine_mode_txt": "Editar mensajes antes de la generación", "sd_interactive_mode": "Genera imágenes automáticamente al enviar mensajes como 'envíame una foto de gato'.", "sd_interactive_mode_txt": "Modo interactivo", "sd_multimodal_captioning": "Utilice subtítulos multimodales para generar indicaciones para retratos de usuarios y personajes basados ​​en sus avatares.", "sd_multimodal_captioning_txt": "Utilice subtítulos multimodales para retratos", "sd_expand": "Ampliar mensajes automáticamente utilizando el modelo de generación de texto", "sd_expand_txt": "Mensajes de mejora automática", "sd_snap": "Solicitudes de generación de instantáneas con una relación de aspecto forzada (retratos, fondos) a la resolución conocida más cercana, mientras se intenta preservar el recuento absoluto de píxeles (recomendado para SDXL).", "sd_snap_txt": "Ajustar resoluciones ajustadas automáticamente", "Source": "Fuente", "sd_auto_url": "Ejemplo: {{auto_url}}", "Authentication (optional)": "Autenticación (opcional)", "Example: username:password": "Ejemplo: nombre de usuario:contraseña", "Important:": "Importante:", "sd_auto_auth_warning_1": "ejecute SD Web UI con el", "sd_auto_auth_warning_2": "¡bandera! Se debe poder acceder al servidor desde la máquina host de SillyTavern.", "sd_drawthings_url": "Ejemplo: {{drawthings_url}}", "sd_drawthings_auth_txt": "¡Ejecute la aplicación DrawThings con el interruptor API HTTP habilitado en la interfaz de usuario! Se debe poder acceder al servidor desde la máquina host de SillyTavern.", "sd_vlad_url": "Ejemplo: {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "Se debe poder acceder al servidor desde la máquina host de <PERSON>llyTavern.", "Hint: Save an API key in AI Horde API settings to use it here.": "Sugerencia: guarde una clave API en la configuración de API de AI Horde para usarla aquí.", "Allow NSFW images from Horde": "<PERSON><PERSON><PERSON> NSFW de Horda", "Sanitize prompts (recommended)": "Indicaciones para desinfectar (recomendado)", "Automatically adjust generation parameters to ensure free image generations.": "Ajuste automáticamente los parámetros de generación para garantizar generaciones de imágenes gratuitas.", "Avoid spending Anlas": "<PERSON><PERSON><PERSON> <PERSON>", "Opus tier": "(<PERSON><PERSON> de obra)", "View my Anlas": "<PERSON>er <PERSON>", "These settings only apply to DALL-E 3": "Estas configuraciones solo se aplican a DALL-E 3", "Image Style": "Estilo <PERSON>", "Image Quality": "Calidad de la imagen", "Standard": "<PERSON><PERSON><PERSON><PERSON>", "HD": "alta definición", "sd_comfy_url": "Ejemplo: {{comfy_url}}", "Open workflow editor": "Abrir el editor de flujo de trabajo", "Create new workflow": "Crear nuevo flujo de trabajo", "Delete workflow": "Eliminar flujo de trabajo", "Enhance": "<PERSON><PERSON><PERSON>", "Refine": "Refinar", "Decrisper": "Descriptor", "Sampling steps": "Pasos de muestreo ()", "Width": "<PERSON><PERSON> ()", "Height": "Altura ()", "Resolution": "Resolución", "Model": "<PERSON><PERSON>", "Sampling method": "Método de muestreo", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON> (no todos los samplers son compatibles)", "SMEA versions of samplers are modified to perform better at high resolution.": "Las versiones SMEA de los muestreadores se modifican para funcionar mejor en alta resolución.", "SMEA": "SMEA (Asociación de Empresas de Medio Ambiente)", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "Las variantes DYN de los muestreadores SMEA a menudo generan resultados más variados, pero pueden fallar en resoluciones muy altas.", "DYN": "DIN", "Scheduler": "Programador", "Restore Faces": "Restaurar caras", "Hires. Fix": "Contrataciones. Arreglo", "Upscaler": "escalador", "Upscale by": "De lujo por", "Denoising strength": "Fuerza de eliminación de ruido", "Hires steps (2nd pass)": "Pasos de contrataciones (2da pasada)", "Preset for prompt prefix and negative prompt": "Preajuste para prefijo de mensaje y mensaje negativo", "Style": "<PERSON><PERSON><PERSON>", "Save style": "Guardar estilo", "Delete style": "Eliminar estilo", "Common prompt prefix": "Prefijo de aviso común", "sd_prompt_prefix_placeholder": "Utilice {prompt} para especificar dónde se insertará el mensaje generado", "Negative common prompt prefix": "Prefijo de mensaje común negativo", "Character-specific prompt prefix": "Prefijo de mensaje específico de carácter", "Won't be used in groups.": "No se utilizará en grupos.", "sd_character_prompt_placeholder": "Cualquier característica que describa al personaje seleccionado actualmente. Se agregará después de un prefijo de aviso común.\nEjemplo: mujer, ojos verdes, cabello castaño, camisa rosa.", "Character-specific negative prompt prefix": "Prefijo de aviso negativo específico del personaje", "sd_character_negative_prompt_placeholder": "Cualquier característica que no debería aparecer para el personaje seleccionado. Se agregará después de un prefijo de mensaje común negativo.\nEjemplo: joyas, zapatos, gafas.", "Shareable": "Compartible", "Image Prompt Templates": "Plantillas de mensajes de imagen", "Vectors Model Warning": "Se recomienda purgar los vectores al cambiar el modelo en medio del chat. De lo contrario, se obtendrán resultados deficientes.", "Translate files into English before processing": "Traducir archivos al inglés antes de procesarlos", "Manager Users": "Administrar usuarios", "New User": "Nuevo Usuario", "Status:": "Estado:", "Created:": "Creado:", "Display Name:": "Nombre para mostrar:", "User Handle:": "Identificador de usuario:", "Password:": "Contraseña:", "Confirm Password:": "Confirma<PERSON>:", "This will create a new subfolder...": "Esto creará una nueva subcarpeta en el directorio /data/ con el identificador del usuario como nombre de la carpeta.", "Current Password:": "Contraseña actual:", "New Password:": "Nueva contraseña:", "Confirm New Password:": "Confirmar nueva contraseña:", "Debug Warning": "Las funciones de esta categoría son sólo para usuarios avanzados. No hagas clic en nada si no estás seguro de las consecuencias.", "Execute": "<PERSON><PERSON><PERSON><PERSON>", "Are you sure you want to delete this user?": "¿Estás seguro de que deseas eliminar este usuario?", "Deleting:": "Eliminando:", "Also wipe user data.": "También borre los datos del usuario.", "Warning:": "Advertencia:", "This action is irreversible.": "Esta acción es irreversible.", "Type the user's handle below to confirm:": "Escriba el nombre del usuario a continuación para confirmar:", "Import Characters": "Importar personajes", "Enter the URL of the content to import": "Ingrese la URL del contenido a importar", "Supported sources:": "Fuentes admitidas:", "char_import_1": "Cará<PERSON> (enlace directo o ID)", "char_import_example": "Ejemplo:", "char_import_2": "<PERSON><PERSON> (enlace directo o ID)", "char_import_3": "Carácter de JanitorAI (enlace directo o UUID)", "char_import_4": "Carácter Pygmalion.chat (enlace directo o UUID)", "char_import_5": "Carácter AICharacterCards.com (enlace directo o ID)", "char_import_6": "Enlace PNG directo (consulte", "char_import_7": "para hosts permitidos)", "char_import_8": "<PERSON><PERSON><PERSON> (Enlace directo)", "char_import_9": "<PERSON><PERSON><PERSON> (Enlace directo)", "Supports importing multiple characters.": "Admite la importación de múltiples caracteres.", "Write each URL or ID into a new line.": "Escriba cada URL o ID en una nueva línea.", "Export for character": "Exportar para personaje", "Export prompts for this character, including their order.": "Exporta indicaciones para este personaje, incluido su orden.", "Export all": "Exportar todo", "Export all your prompts to a file": "Exporta todas tus indicaciones a un archivo", "Insert prompt": "Insertar indicaciones", "Delete prompt": "Eliminar indicaciones", "Import a prompt list": "Importar una lista de indicaciones", "Export this prompt list": "Exportar esta lista de indicaciones", "Reset current character": "Restablecer personaje actual", "New prompt": "Nuevas indicaciones", "Prompts": "Indicaciones", "Total Tokens:": "Tokens totales:", "prompt_manager_tokens": "<PERSON><PERSON><PERSON>", "Are you sure you want to reset your settings to factory defaults?": "¿Está seguro de que desea restablecer la configuración a los valores predeterminados de fábrica?", "Don't forget to save a snapshot of your settings before proceeding.": "No olvide guardar una instantánea de su configuración antes de continuar.", "Settings Snapshots": "Instantáneas de configuración", "Record a snapshot of your current settings.": "Grabe una instantánea de su configuración actual.", "Make a Snapshot": "<PERSON>cer una instantánea", "Restore this snapshot": "Restaurar esta instantánea", "Hi,": "Hola,", "To enable multi-account features, restart the SillyTavern server with": "Para habilitar las funciones de múltiples cuentas, reinicie el servidor SillyTavern con", "set to true in the config.yaml file.": "establecido en verdadero en el archivo config.yaml.", "Account Info": "Informacion de cuenta", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "Para cambiar su avatar de usuario, use los botones a continuación o seleccione una persona predeterminada en el menú Administración de personas.", "Set your custom avatar.": "Configura tu avatar personalizado.", "Remove your custom avatar.": "Elimina tu avatar personalizado.", "Handle:": "Manejar:", "This account is password protected.": "Esta cuenta está protegida con contraseña.", "This account is not password protected.": "Esta cuenta no está protegida con contraseña.", "Account Actions": "Acciones de cuenta", "Change Password": "Cambiar la contraseña", "Manage your settings snapshots.": "Administre sus instantáneas de configuración.", "Download a complete backup of your user data.": "Descarga una copia de seguridad completa de tus datos de usuario.", "Download Backup": "Descargar copia de seguridad", "Danger Zone": "Zona peligrosa", "Reset your settings to factory defaults.": "Restablezca su configuración a los valores predeterminados de fábrica.", "Reset Settings": "<PERSON>ini<PERSON><PERSON> a<PERSON>", "Wipe all user data and reset your account to factory settings.": "Borre todos los datos del usuario y restablezca su cuenta a la configuración de fábrica.", "Reset Everything": "Restablecer todo", "Reset Code:": "Restablecer <PERSON>:", "Want to update?": "¿Quieres actualizar?", "How to start chatting?": "¿Cómo empezar a chatear?", "Click _space": "<PERSON><PERSON> clic", "and select a": "y selecciona una", "Chat API": " API de chat", "and pick a character.": "y elige un personaje.", "You can browse a list of bundled characters in the": "<PERSON>uedes explorar una lista de personajes incluidos en el menú de", "Download Extensions & Assets": "Download Extensions & Assets", "menu within": "dentro de", "Confused or lost?": "¿Confundido o perdido?", "click these icons!": "¡Haz clic en estos iconos!", "in the chat bar": "en la barra de chat", "SillyTavern Documentation Site": "Sitio de documentación de SillyTavern", "Extras Installation Guide": "Guía de instalación de extras", "Still have questions?": "¿Todavía tienes preguntas?", "Join the SillyTavern Discord": "Únete al Discord de SillyTavern", "Post a GitHub issue": "Publicar un problema en GitHub", "Contact the developers": "Contactar a los desarrolladores", "Prome (Visual Novel Extension)": "Prome (Extensión de Modo Waifu/Novela Visual)", "Brought to you by": "Presentado por", "and Prometheus.": "y Prometheus.", "Enable Prome": "Iniciar <PERSON>", "Toggles Prome, VN Mode and other Prome features.": "Activa Prome, el modo VN y otras funciones de Prome.", "Features marked with a": "Las funciones marcadas con un", "require Prome to be enabled.": "requieren que Prome esté iniciado.", "Sheld Configuration": "Configuración de interfaz (Sheld)", "Hide Sheld (Message Box)": "<PERSON><PERSON><PERSON><PERSON> (cuadro de mensajes)", "Hide the message box (sheld) in the ST UI.": "Ocultar el cuadro de mensajes (sheld) en la interfaz de usuario de SillyTavern.", "Enable Traditional VN Mode": "Modo VN tradicional*", "Only Show Last Message in Chat (Requires Prome to be enabled).": "Mostrar solo el último mensaje en el chat (Requiere que Prome esté iniciado).", "Letterbox Configuration*": "Configuración de la franja negra*", "Letterbox Mode": "Modo de franja negra", "Select the letterbox mode for the Prome VN UI.": "Seleccione el modo de franja negra para la interfaz de usuario VN de Prome.", "Horizontal Letterbox": "<PERSON><PERSON><PERSON> negra <PERSON>", "Vertical Letterbox": "<PERSON><PERSON><PERSON> negra <PERSON>", "Letterbox Color": "Color de la franja negra", "Select the color of the letterbox.": "Seleccione el color de la franja negra.", "Letterbox Size": "Tamaño de la franja negra", "Set the size of the letterbox.": "Establezca el tamaño de la franja negra.", "Sprite Configuration": "Configuración de sprites", "Emulate Character Card as Sprite": "[BETA] Emular tarjeta de personaje como sprite", "Emulates the character card of a character to be a sprite. (Requires Prome to be enabled).": "Emula la tarjeta de personaje de un personaje para que sea un sprite. (Requiere que Prome esté iniciado).", "Enable Sprite Shake": "[BETA] Sprite Shake", "Shakes the character sprite when the character is speaking (Only works if Streaming is enabled in Preset Settings).": "Agita el sprite del personaje cuando el personaje está hablando (solo funciona si la transmisión está habilitada en la configuración preestablecida).", "Enable Focus Mode": "Modo de en<PERSON>", "Focuses the current speaking character in chat. (Requires Prome to be enabled).": "Enfoca al personaje que está hablando en el chat. (Requiere que Prome esté iniciado).", "Darken Unfocused Character Sprites": "Oscurecer sprites de personajes", "Darkens non-speaking (unfocused) characters. (Requires Prome to be enabled).": "Oscurece a los personajes que no hablan (no enfocados). (Requiere que Prome esté iniciado).", "Auto-Hide Sprites": "Ocultar sprites automáticamente", "Auto-hides characters from the screen that haven't been in the conversation for a while up to X characters. (Requires Prome to be enabled).": "Oculta automáticamente a los personajes de la pantalla que no han estado en la conversación durante un tiempo hasta X personajes. (Requiere que Prome esté iniciado).", "Max Visible Sprites": "Máxi<PERSON> de personajes visibles", "Set the maximum number of visible sprites that appears in the VN screen.": "Establezca el número máximo de sprites visibles que aparecen en la pantalla VN.", "Sprite Shadow Configuration": "Configuración de sombra de sprites", "Enable Sprite Shadow": "Sombra de sprite", "Adds a shadow to the character sprite.": "Agrega una sombra al sprite del personaje.", "Shadow X Offset": "Desplazamiento X de la sombra", "Set the X offset of the character shadow.": "Establezca el desplazamiento X de la sombra del personaje.", "Shadow Y Offset": "Desplazamiento Y de la sombra", "Set the Y offset of the character shadow.": "Establezca el desplazamiento Y de la sombra del personaje.", "Shadow Blur": "Desenfoque de sombra", "Set the blur of the character shadow.": "Establezca el desenfoque de la sombra del personaje.", "Focus Mode Settings": "Configuración del modo de enfoque", "Focus Mode Animation": "Animación de modo de enfoque", "Select the animation for focus mode.": "Seleccione la animación para el modo de enfoque.", "Focus Mode Animation Speed": "Velocidad de animación del modo de enfoque", "Set the speed of the focus animation.": "Establezca la velocidad de la animación de enfoque.", "User Sprite Configuration": "[BETA] Configuración de sprites de usuario", "Enable User Sprite": "Sprites de usuario", "Enables the ability to use a user sprite for your persona.": "Habilita la capacidad de usar un sprite de usuario para tu persona.", "Sprite set": "Conjunto de sprites", "Type the name of the sprite set to use for your persona. (Place your sprites in the 'characters' folder in SillyTavern).": "Escriba el nombre del conjunto de sprites que desea utilizar para su persona. (Coloque sus sprites en la carpeta 'characters' en SillyTavern).", "Note: Create a sprite folder in the ": "Nota: Cree una carpeta de sprites en el ", " folder of your user directory (typically 'data/default-user'). Place your expressions there.": " carpeta de su directorio de usuario (normalmente 'data/default-user'). Coloque sus expresiones allí.", "Tint Configuration": "Configuración de tono", "Enable Chat Tint": "<PERSON><PERSON>", "Tints the chat background and/or character sprites.": "Tinta el fondo del chat y/o los sprites de personajes.", "Share World Tint With Characters": "Compartir tinte mundial con personajes*", "Applies the world tint to character sprites (Requires Prome to be enabled. This will override your character tint settings).": "Aplica el tinte mundial a los sprites de personajes (Requiere que Prome esté iniciado. Esto anulará la configuración de tinte de su personaje).", "Tint Presets": "Preajustes de tinte", "Select the tint preset to use for the Prome VN UI.": "Seleccione el preajuste de tinte que desea utilizar para la interfaz de usuario VN de Prome.", "World Tint Settings": "Configuración de tinte mundial", "Tints the world background.": "Tinta el fondo mundial.", "Enable World Tint": "<PERSON><PERSON> mund<PERSON>", "Set the strength of the world blur.": "Establezca la fuerza del desenfoque mundial.", "Brightness": "<PERSON><PERSON><PERSON>", "Set the brightness of the world.": "Establezca el brillo del mundo.", "Contrast": "Contraste", "Set the contrast of the world.": "Establezca el contraste del mundial.", "Grayscale": "Escala de grises", "Makes the world black and white.": "Hace que el mundo sea en blanco y negro.", "Hue": "<PERSON><PERSON>", "Set the hue of the world tint.": "Establezca el matiz del tinte mundial.", "Invert": "Invertir", "Inverts the world colors.": "Invierte los colores del mundo.", "Saturate": "Saturar", "Saturates the world colors.": "Satura los colores del mundo.", "Makes the world warmer in color.": "Hace que el mundo sea más cálido en color.", "Character Tint Settings": "Configuración de tinte de sprites*", "Enable Character Tint (Requires Prome to be enabled)": "Tinte de sprites (Requiere que Prome esté iniciado)", "Set the strength of the character blur.": "Establezca la fuerza del desenfoque de personajes.", "Set the brightness of the character.": "Establezca el brillo de personaje.", "Set the contrast of the character.": "Establezca el contraste de personajes.", "Makes the character black and white.": "Hace que el personaje sea en blanco y negro.", "Set the hue of the character.": "Establezca el matiz del tinte de personajes.", "Inverts the character colors.": "Invierte los colores del personaje.", "Saturates the character colors.": "Satura los colores del personaje.", "Makes the character warmer in color.": "Hace que el personaje sea más cálido en color.", "Keybinds": "Atajos de teclado", "Commands": "<PERSON><PERSON><PERSON>", "Prome Keybinds": "Combinaciones de teclas de Prome", "Hide/Show SillyTavern's Sheld (Message Box)": "Ocultar/Mostrar el estante de SillyTavern (cuadro de mensaje)", "Prome Commands": "Comandos de Prome", "Show/Hide the letterbox (black bars) in the VN UI": "Mostrar/Ocultar la franja negra (barras negras) en la interfaz VN", "Toggles focus mode on character sprites": "Alterna el modo de enfoque en los sprites de personajes", "Sets the focus mode animation": "Establece la animación del modo de enfoque", "Toggles the defocus tint on non-speaking character sprites": "Alterna el tinte de desenfoque en los sprites de personajes que no hablan", "Toggles the shake animation when a character speaks on character sprites": "Alterna la animación de sacudida cuando un personaje habla en los sprites de personajes", "Toggles sprite shadows on character sprites": "Alterna las sombras de los sprites de personajes", "Toggles world/character tint on the VN UI": "Alterna el tinte mundial/de personajes en la interfaz VN", "Toggles world tint on the VN UI": "Alterna el tinte mundial en la interfaz VN", "Toggles character tint on the VN UI": "Alterna el tinte de personajes en la interfaz VN", "Toggles sharing world tint with character sprites (This will override Character Tint)": "Alterna el tinte mundial compartido con los sprites de personajes (esto anulará el tinte de personajes)", "Sets the expression of the user sprite": "Establece la expresión del sprite de usuario", "Sets the user sprite set to use for the user sprite": "Establece el conjunto de sprites de usuario para usar en el sprite de usuario", "Toggles the user sprite on the VN UI": "Alterna el sprite de usuario en la interfaz VN"}