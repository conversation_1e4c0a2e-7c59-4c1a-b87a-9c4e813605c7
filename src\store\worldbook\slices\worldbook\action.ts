import { StateCreator } from 'zustand';

import { worldbookService } from '@/services/worldbook';
import type {
  CreateWorldbookData,
  UpdateWorldbookData,
  ImportResultDetail,
  BulkOperationResult,
} from '@/types/worldbook';

import type { WorldbookStore } from '../../store';

export interface WorldbookAction {
  // ====== CRUD Operations ======
  fetchWorldbooks: () => Promise<void>;
  createWorldbook: (data: CreateWorldbookData) => Promise<void>;
  updateWorldbook: (id: string, data: UpdateWorldbookData) => Promise<void>;
  deleteWorldbook: (id: string) => Promise<void>;
  deleteAllWorldbooks: () => Promise<void>;
  
  // ====== Batch Operations ======
  bulkDeleteWorldbooks: (ids: string[]) => Promise<BulkOperationResult>;
  
  // ====== Import/Export Operations ======
  importWorldbook: (data: {
    worldbook: CreateWorldbookData;
    entries: any[];
  }) => Promise<ImportResultDetail>;
  
  // ====== Statistics Operations ======
  fetchWorldbookStats: () => Promise<void>;
  
  // ====== State Management ======
  setCurrentWorldbook: (id: string | null) => void;
  setSelectedWorldbookId: (id: string | null) => void;
  resetWorldbookState: () => void;
  
  // ====== UI State Management ======
  toggleSidebar: () => void;
  setWorldbookManagerVisible: (visible: boolean) => void;
  
  // ====== Internal State Management ======
  internal_setWorldbookLoading: (id: string, loading: boolean) => void;
  internal_setImporting: (importing: boolean) => void;
  internal_setBulkOperationLoading: (loading: boolean) => void;
}

export const createWorldbookSlice: StateCreator<
  WorldbookStore,
  [['zustand/devtools', never]],
  [],
  WorldbookAction
> = (set, get) => ({
  // ====== CRUD Operations ======
  
  fetchWorldbooks: async () => {
    set({ loading: true });
    try {
      console.log('🔍 [WorldbookStore] Fetching worldbooks...');
      const worldbooks = await worldbookService.getWorldbooks();
      console.log('✅ [WorldbookStore] Fetched worldbooks:', worldbooks.length);

      set({
        worldbooks,
        loading: false
      });
    } catch (error) {
      console.error('❌ [WorldbookStore] Failed to fetch worldbooks:', error);
      set({ loading: false });
      throw error;
    }
  },

  createWorldbook: async (data) => {
    try {
      console.log('📚 [WorldbookStore] Creating worldbook:', data.name);
      const newWorldbook = await worldbookService.createWorldbook(data);
      console.log('✅ [WorldbookStore] Created worldbook:', newWorldbook.id);

      set((state) => ({
        worldbooks: [...state.worldbooks, newWorldbook],
      }));
    } catch (error) {
      console.error('❌ [WorldbookStore] Failed to create worldbook:', error);
      throw error;
    }
  },

  updateWorldbook: async (id, data) => {
    get().internal_setWorldbookLoading(id, true);
    try {
      console.log('📝 [WorldbookStore] Updating worldbook:', id);
      const updatedWorldbook = await worldbookService.updateWorldbook(id, data);
      console.log('✅ [WorldbookStore] Updated worldbook:', updatedWorldbook?.id);

      if (updatedWorldbook) {
        set((state) => ({
          worldbooks: state.worldbooks.map((wb) =>
            wb.id === id ? updatedWorldbook : wb
          ),
          currentWorldbook: state.currentWorldbook?.id === id ? updatedWorldbook : state.currentWorldbook,
        }));
      }
    } catch (error) {
      console.error('❌ [WorldbookStore] Failed to update worldbook:', error);
      throw error;
    } finally {
      get().internal_setWorldbookLoading(id, false);
    }
  },

  deleteWorldbook: async (id) => {
    get().internal_setWorldbookLoading(id, true);
    try {
      console.log('🗑️ [WorldbookStore] Deleting worldbook:', id);
      await worldbookService.deleteWorldbook(id);
      console.log('✅ [WorldbookStore] Deleted worldbook:', id);

      set((state) => ({
        worldbooks: state.worldbooks.filter((wb) => wb.id !== id),
        currentWorldbook: state.currentWorldbook?.id === id ? null : state.currentWorldbook,
        selectedWorldbookId: state.selectedWorldbookId === id ? null : state.selectedWorldbookId,
        // 如果删除的是当前世界书，清空条目列表
        entries: state.currentWorldbook?.id === id ? [] : state.entries,
        currentEntry: state.currentWorldbook?.id === id ? null : state.currentEntry,
        selectedEntryIds: state.currentWorldbook?.id === id ? [] : state.selectedEntryIds,
      }));
    } catch (error) {
      console.error('❌ [WorldbookStore] Failed to delete worldbook:', error);
      throw error;
    } finally {
      get().internal_setWorldbookLoading(id, false);
    }
  },

  deleteAllWorldbooks: async () => {
    set({ loading: true });
    try {
      console.log('🗑️ [WorldbookStore] Deleting all worldbooks...');
      // 获取所有世界书ID并批量删除
      const { worldbooks } = get();
      const ids = worldbooks.map(wb => wb.id);
      if (ids.length > 0) {
        await worldbookService.bulkDeleteWorldbooks(ids);
      }
      console.log('✅ [WorldbookStore] Deleted all worldbooks');

      set({
        currentEntry: null,
        currentWorldbook: null,
        entries: [],
        loading: false,
        selectedEntryIds: [],
        selectedWorldbookId: null,
        worldbooks: [],
      });
    } catch (error) {
      console.error('❌ [WorldbookStore] Failed to delete all worldbooks:', error);
      set({ loading: false });
      throw error;
    }
  },

  // ====== Batch Operations ======
  
  bulkDeleteWorldbooks: async (ids) => {
    get().internal_setBulkOperationLoading(true);
    try {
      console.log('🗑️ [WorldbookStore] Bulk deleting worldbooks:', ids);
      const result = await worldbookService.bulkDeleteWorldbooks(ids);
      console.log('✅ [WorldbookStore] Bulk delete result:', result);

      // 更新状态，移除成功删除的世界书
      const successfulIds = ids.filter(id =>
        !result.errors.some(error => error.id === id)
      );

      set((state) => ({
        currentWorldbook: successfulIds.includes(state.currentWorldbook?.id || '')
          ? null : state.currentWorldbook,
        lastBulkOperationResult: result,
        selectedWorldbookId: successfulIds.includes(state.selectedWorldbookId || '')
          ? null : state.selectedWorldbookId,
        worldbooks: state.worldbooks.filter((wb) => !successfulIds.includes(wb.id)),
      }));

      return result;
    } catch (error) {
      console.error('❌ [WorldbookStore] Failed to bulk delete worldbooks:', error);
      throw error;
    } finally {
      get().internal_setBulkOperationLoading(false);
    }
  },

  // ====== Import/Export Operations ======

  importWorldbook: async (data) => {
    get().internal_setImporting(true);
    try {
      console.log('📥 [WorldbookStore] Importing worldbook:', data.worldbook.name);
      const result = await worldbookService.importWorldbook(data);
      console.log('✅ [WorldbookStore] Import result:', result);

      // 重新获取世界书列表
      await get().fetchWorldbooks();

      set({ lastImportResult: result });
      return result;
    } catch (error) {
      console.error('❌ [WorldbookStore] Failed to import worldbook:', error);
      throw error;
    } finally {
      get().internal_setImporting(false);
    }
  },

  // ====== Statistics Operations ======
  
  fetchWorldbookStats: async () => {
    try {
      console.log('📊 [WorldbookStore] Fetching worldbook stats...');
      const stats = await worldbookService.getWorldbookStats();
      console.log('✅ [WorldbookStore] Fetched stats:', stats);

      set({ stats });
    } catch (error) {
      console.error('❌ [WorldbookStore] Failed to fetch stats:', error);
      throw error;
    }
  },

  // ====== Internal State Management ======

  internal_setBulkOperationLoading: (loading) => {
    set({ bulkOperationLoading: loading });
  },

  internal_setImporting: (importing) => {
    set({ importing });
  },

  internal_setWorldbookLoading: (id, loading) => {
    set((state) => ({
      worldbookLoadingIds: loading
        ? [...state.worldbookLoadingIds, id]
        : state.worldbookLoadingIds.filter((loadingId) => loadingId !== id),
    }));
  },

  // ====== State Management ======

  resetWorldbookState: () => {
    set({
      currentWorldbook: null,
      loading: false,
      selectedWorldbookId: null,
      stats: null,
      worldbookLoadingIds: [],
      worldbooks: [],
    });
  },

  setCurrentWorldbook: (id) => {
    const worldbook = id ? get().worldbooks.find((wb) => wb.id === id) : null;
    set({
      currentEntry: null,
      currentWorldbook: worldbook || null,
      entries: [],
      entriesHasMore: false,
      entriesPage: 1,
      entriesTotal: 0,
      selectedEntryIds: [],
      selectedWorldbookId: id,
    });
  },

  setSelectedWorldbookId: (id) => {
    set({ selectedWorldbookId: id });
  },

  setWorldbookManagerVisible: (visible) => {
    set({ worldbookManagerVisible: visible });
  },

  toggleSidebar: () => {
    set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed }));
  },
});
