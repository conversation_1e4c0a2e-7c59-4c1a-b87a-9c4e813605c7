'use client';

import { ActionIcon, Tag } from '@lobehub/ui';
import { Dropdown, App } from 'antd';
import type { MenuProps } from 'antd';
import { createStyles } from 'antd-style';
import { BookO<PERSON>, Eye, EyeOff, MoreHorizontal, Edit, Trash2 } from 'lucide-react';
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { useQueryRoute } from '@/hooks/useQueryRoute';
import { useWorldbookStore } from '@/store/worldbook';
import type { Worldbook } from '@/types/worldbook';
// Hook模式已移除，改为通过props接收回调函数

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    padding: 12px 16px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: ${token.colorFillTertiary};
    }

    &.active {
      background: ${token.colorPrimaryBg};
      border-color: ${token.colorPrimary};
    }
  `,
  description: css`
    color: ${token.colorTextSecondary};
    font-size: 12px;
    line-height: 1.4;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  `,
  entryCount: css`
    color: ${token.colorTextTertiary};
    font-size: 11px;
  `,
  exclusiveAgent: css`
    display: flex;
    align-items: center;
    // gap: 8px;
    margin-top: 8px;
  `,
  exclusiveTag: css`
    max-width: calc(100% - 16px);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    line-height: 16px;
    background: ${token.purple1};
    color: ${token.purple7};
    border: 1px solid ${token.purple3};
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;

    &:hover {
      background: ${token.purple2};
      color: ${token.purple8};
      border-color: ${token.purple4};
    }
  `,
  header: css`
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  `,
  meta: css`
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
  `,
  title: css`
    font-weight: 500;
    color: ${token.colorText};
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  `,
}));

interface WorldbookItemProps {
  worldbook: Worldbook;
  isActive?: boolean;
  onClick?: () => void;
  onEdit?: (worldbook: Worldbook) => void;
}

const WorldbookItem = memo<WorldbookItemProps>(({ worldbook, isActive, onClick, onEdit }) => {
  const { t } = useTranslation('worldbook');
  const { styles } = useStyles();
  const router = useQueryRoute();
  const { modal, message } = App.useApp();

  const { updateWorldbook, deleteWorldbook } = useWorldbookStore((s) => ({
    updateWorldbook: s.updateWorldbook,
    deleteWorldbook: s.deleteWorldbook,
  }));

  const handleClick = () => {
    // 导航到世界书详情页面
    router.push(`/worldbooks/${worldbook.id}`);
    // 同时调用传入的点击回调
    onClick?.();
  };

  const handleToggleEnabled = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await updateWorldbook(worldbook.id, {
        enabled: !worldbook.enabled,
      });
    } catch (error) {
      console.error('Failed to toggle worldbook enabled state:', error);
    }
  };

  const handleEdit = () => {
    onEdit?.(worldbook);
  };

  const handleDelete = () => {
    modal.confirm({
      title: t('delete.confirm'),
      content: t('delete.confirmDescription', { name: worldbook.name }),
      okText: t('delete', { ns: 'common' }),
      cancelText: t('cancel', { ns: 'common' }),
      okButtonProps: { danger: true },
      onOk: async () => {
        try {
          await deleteWorldbook(worldbook.id);
          message.success(t('delete.success'));
        } catch (error) {
          console.error('Failed to delete worldbook:', error);
          message.error(t('delete.error'));
        }
      },
    });
  };

  const menuItems: MenuProps['items'] = [
    {
      key: 'edit',
      label: t('edit', { ns: 'common' }),
      icon: <Edit size={14} />,
      onClick: ({ domEvent }) => {
        domEvent.stopPropagation();
        handleEdit();
      },
    },
    {
      key: 'delete',
      label: t('delete', { ns: 'common' }),
      icon: <Trash2 size={14} />,
      danger: true,
      onClick: ({ domEvent }) => {
        domEvent.stopPropagation();
        handleDelete();
      },
    },
  ];

  return (
    <div className={`${styles.container} ${isActive ? 'active' : ''}`} onClick={handleClick}>
      <div className={styles.header}>
        <h4 className={styles.title} title={worldbook.name}>
          {worldbook.name}
        </h4>

        <Flexbox gap={4} horizontal>
          <ActionIcon
            icon={worldbook.enabled ? <Eye size={14} /> : <EyeOff size={14} />}
            onClick={handleToggleEnabled}
            size="small"
            title={worldbook.enabled ? t('entry.enabled') : t('entry.disabled')}
          />
          <Dropdown
            menu={{ items: menuItems }}
            placement="bottomRight"
            trigger={['click']}
          >
            <ActionIcon
              icon={<MoreHorizontal size={14} />}
              onClick={(e) => {
                e.stopPropagation();
              }}
              size="small"
              title={t('more', { ns: 'common' })}
            />
          </Dropdown>
        </Flexbox>
      </div>

      {worldbook.description && (
        <p className={styles.description} title={worldbook.description}>
          {worldbook.description}
        </p>
      )}

      <div className={styles.meta}>
        <Tag color={worldbook.enabled ? 'green' : 'default'} size="small">
          <BookOpen size={10} style={{ marginRight: 4 }} />
          {worldbook.enabled ? t('entry.enabled') : t('entry.disabled')}
        </Tag>

        {worldbook.entryCount !== undefined && (
          <span className={styles.entryCount}>
            {worldbook.entryCount} {t('entry.title')}
          </span>
        )}

        <span className={styles.entryCount}>
          {new Date(worldbook.updatedAt).toLocaleDateString()}
        </span>
      </div>

      {/* 专属agent显示 */}
      {worldbook.primaryAgent && (
        <div className={styles.exclusiveAgent}>
          <span
            className={styles.exclusiveTag}
            title={`${t('exclusive.tooltip')}: ${worldbook.primaryAgent.title}`}
          >
            【{t('exclusive.label')}】{worldbook.primaryAgent.title}
          </span>
        </div>
      )}

      {/* Modal组件已移至页面级别统一管理 */}
    </div>
  );
});

WorldbookItem.displayName = 'WorldbookItem';

export default WorldbookItem;
