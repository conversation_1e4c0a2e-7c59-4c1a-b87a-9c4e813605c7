/* eslint-disable sort-keys-fix/sort-keys-fix  */
import {
  boolean,
  index,
  integer,
  jsonb,
  pgTable,
  text,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { createInsertSchema } from 'drizzle-zod';

import { idGenerator } from '@/database/utils/idGenerator';
import type { MatchSources } from '@/types/worldbook';

import { createdAt, updatedAt } from './_helpers';
import { chunks } from './rag';
import { users } from './user';

/**
 * 世界书表
 * 整合现有功能和新功能
 */
export const worldbooks = pgTable(
  'worldbooks',
  {
    id: text('id')
      .$defaultFn(() => idGenerator('worldbooks'))
      .primaryKey(),
    
    name: text('name').notNull(),
    description: text('description'),
    enabled: boolean('enabled').default(true),
    
    userId: text('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),
    clientId: text('client_id'),
    
    createdAt: createdAt(),
    updatedAt: updatedAt(),
  },
  (table) => ({
    userIdIdx: index('worldbooks_user_id_idx').on(table.userId),
    nameIdx: index('worldbooks_name_idx').on(table.name),
    enabledIdx: index('worldbooks_enabled_idx').on(table.enabled),
  }),
);

/**
 * 世界书条目表
 * 通过chunks表关联内容，支持向量化搜索
 * 整合现有字段和新增字段，保持向后兼容
 */
export const worldbookChunks = pgTable(
  'worldbook_chunks',
  {
    // 基础标识字段
    id: text('id')
      .$defaultFn(() => idGenerator('worldbookChunks'))
      .primaryKey(),
    title: text('title').notNull(),
    enabled: boolean('enabled').default(true),

    // 关联字段
    worldbookId: text('worldbook_id')
      .references(() => worldbooks.id, { onDelete: 'cascade' })
      .notNull(),
    chunkId: uuid('chunk_id')
      .references(() => chunks.id, { onDelete: 'cascade' })
      .notNull(),

    // 激活规则字段
    keys: text('keys').array().default([]),
    keysSecondary: text('keys_secondary').array().default([]),
    selectiveLogic: text('selective_logic').default('and_any'),

    // 激活模式字段
    activationMode: text('activation_mode').default('keyword'),

    // 位置控制字段
    position: text('position').default('after'),
    depth: integer('depth').default(4), // AT_DEPTH时使用
    role: text('role', {
      enum: ['system', 'user', 'assistant', 'tool']
    }).default('system'), // AT_DEPTH时使用
    order: integer('order').default(100),
    
    // 匹配配置字段
    matchSources: jsonb('match_sources').$type<MatchSources>().default({
      personaDescription: false,
      characterDescription: false,
      characterPersonality: false,
      characterDepthPrompt: false,
      scenario: false,
      creatorNotes: false,
    }),
    caseSensitive: boolean('case_sensitive').default(false),
    matchWholeWords: boolean('match_whole_words').default(false),
    useRegex: boolean('use_regex').default(false),

    // 概率控制字段
    probability: integer('probability').default(100),
    useProbability: boolean('use_probability').default(true),
    
    // 时间效果字段
    sticky: integer('sticky'),
    cooldown: integer('cooldown'),
    delay: integer('delay'),
    
    // 递归控制字段
    excludeRecursion: boolean('exclude_recursion').default(false),
    preventRecursion: boolean('prevent_recursion').default(false),
    delayUntilRecursion: integer('delay_until_recursion').default(0),

    // 分组功能字段
    groupName: text('group_name'),
    groupWeight: integer('group_weight').default(100),
    groupOverride: boolean('group_override').default(false),
    useGroupScoring: boolean('use_group_scoring').default(false),
    
    // 扫描控制字段
    scanDepth: integer('scan_depth'),
    
    // 显示控制字段
    displayIndex: integer('display_index').default(0),
    addMemo: boolean('add_memo').default(false),

    // 装饰器字段
    decorators: text('decorators').array().default([]),

    // 角色过滤字段
    characterFilterNames: text('character_filter_names').array().default([]),
    characterFilterTags: text('character_filter_tags').array().default([]),
    characterFilterExclude: boolean('character_filter_exclude').default(false),

    // 用户关联字段
    userId: text('user_id')
      .references(() => users.id, { onDelete: 'cascade' })
      .notNull(),
    clientId: text('client_id'),

    // 时间戳字段
    createdAt: createdAt(),
    updatedAt: updatedAt(),
  },
  (table) => ({
    // 基础索引
    worldbookIdIdx: index('worldbook_chunks_worldbook_id_idx').on(table.worldbookId),
    userIdIdx: index('worldbook_chunks_user_id_idx').on(table.userId),
    enabledIdx: index('worldbook_chunks_enabled_idx').on(table.enabled),
    orderIdx: index('worldbook_chunks_order_idx').on(table.order),
    chunkIdIdx: index('worldbook_chunks_chunk_id_idx').on(table.chunkId),

    // 查询优化索引 (新增)
    activationModeIdx: index('worldbook_chunks_activation_mode_idx').on(table.activationMode),
    positionIdx: index('worldbook_chunks_position_idx').on(table.position),
    probabilityIdx: index('worldbook_chunks_probability_idx').on(table.probability),
    groupNameIdx: index('worldbook_chunks_group_name_idx').on(table.groupName),

    // 全文搜索索引 (新增)
    titleSearchIdx: index('worldbook_chunks_title_search_idx').using('gin', table.title),
    keysSearchIdx: index('worldbook_chunks_keys_search_idx').using('gin', table.keys),

    // 复合索引优化 (新增)
    activationSearchIdx: index('worldbook_chunks_activation_search_idx').on(
      table.worldbookId,
      table.enabled,
      table.activationMode,
    ),
    orderSortIdx: index('worldbook_chunks_order_sort_idx').on(
      table.worldbookId,
      table.order,
      table.displayIndex,
    ),

  }),
);

// Zod schemas
export const insertWorldbooksSchema = createInsertSchema(worldbooks);
export const insertWorldbookChunksSchema = createInsertSchema(worldbookChunks);

// 类型推导
export type NewWorldbook = typeof worldbooks.$inferInsert;
export type WorldbookItem = typeof worldbooks.$inferSelect;
export type NewWorldbookChunk = typeof worldbookChunks.$inferInsert;
export type WorldbookChunkItem = typeof worldbookChunks.$inferSelect;
