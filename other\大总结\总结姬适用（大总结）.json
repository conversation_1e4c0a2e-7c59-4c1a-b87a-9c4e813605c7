{"version": 2, "name": "总结姬适用（大总结）", "disableSend": false, "placeBeforeInput": false, "injectInput": false, "color": "rgba(0, 0, 0, 0)", "onlyBorderColor": false, "qrList": [{"id": 4, "showLabel": false, "label": "大总结", "title": "", "message": "/let key=聊天使用预设名 {:/preset |:}()||\n/let key=聊天使用模型名 {:/model |:}()||\n/let key=总结破限预设名 \"Gemini Summary（大总结）\"||\n/let key=总结使用模型名 \"Gemini 2.0 Flash Experimental\"|| \n\n/getchatlore |\n/setvar key=chatlore ||\n/findentry file={{getvar::chatlore}} field=comment \"剧情大总结\" |\n/if left={{pipe}} right=\"\" rule=neq else={:\n\t/createentry file={{getvar::chatlore}} |\n\t/setvar key=cacheUID(大总结) | \n\t/setentryfield file={{getvar::chatlore}} uid={{getvar::cacheUID(大总结)}} field=comment \"剧情大总结\" |\n\t/setentryfield file={{getvar::chatlore}} uid={{getvar::cacheUID(大总结)}} field=constant true |\n\t/setentryfield file={{getvar::chatlore}} uid={{getvar::cacheUID(大总结)}} field=position 1|\n:} {:\n\t/setvar key=cache_big_A {:/getentryfield file={{getvar::chatlore}} field=content {{getvar::cacheUID(大总结)}}|:}()|\n:}|\n\t\n/findentry file={{getvar::chatlore}} field=comment \"聊天记录缓存\" |\n/setvar key=cacheUID|\n/if left={{pipe}} right=\"\" rule=neq else={:\n\t/echo 没有聊天记录缓存|\n:} {:\n\t/getentryfield file={{getvar::chatlore}} field=content {{getvar::cacheUID}} |\n\t/setvar key=cache |                \n\t/preset \"{{var::总结破限预设名}}\"|\n\t/model quiet=true \"{{var::总结使用模型名}}\"|\n\t/wait 2000 ||\n\t/let key=GenResult {:/gen lock=on |:}()|\n\t/let key=result_check true||\n\t/if left=GenResult right=0 {:/var key=result_check false|:}|\n\t/if left=GenResult right=\"{{noop}}\" {:/var key=result_check false|:}|\n\t/if left=GenResult right=[{{noop}}] {:/var key=result_check false|:}|\n\t/if left=GenResult right={{{noop}}} {:/var key=result_check false|:}|\n\t/if left=result_check else={:\n\t\t/preset \"{{var::聊天使用预设名}}\" |\n\t\t/model quiet=true \"{{var::聊天使用模型名}}\"|\n\t\t/flushvar cache|\n\t\t/echo \"总结失败\" |\n\t:} {:\n\t\t/setvar key=cache_big_B \"{{getvar::cache_big_A}}{{newline}}{{var::GenResult}}\"|\n\t\t/setentryfield file={{getvar::chatlore}} uid={{getvar::cacheUID(大总结)}} field=content \"{{getvar::cache_big_B}}\"|\n\t\t/preset \"{{var::聊天使用预设名}}\" |\n\t\t/model quiet=true \"{{var::聊天使用模型名}}\"|\n\t\t/flushvar cache_big_B|\n\t\t/echo 已完成大总结 |\n\t:}|\n:}|\n\n\n\n\n                          \n                                                    \n                                                                              \n                                                                                                        \n                                                                                                                                ", "contextList": [], "preventAutoExecute": true, "isHidden": false, "executeOnStartup": false, "executeOnUser": false, "executeOnAi": false, "executeOnChatChange": false, "executeOnGroupMemberDraft": false, "executeOnNewChat": false, "automationId": ""}], "idIndex": 4}