'use client';

import { Button, Form, type FormGroupItemType, SliderWithInput } from '@lobehub/ui';
import { Spin, Switch, message } from 'antd';
import { createStyles } from 'antd-style';
import { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import InfoTooltip from '@/components/InfoTooltip';
import { FORM_STYLE } from '@/const/layoutTokens';
import { useUserStore } from '@/store/user';
import { worldbookSettingsSelectors } from '@/store/user/slices/settings/selectors';

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    display: flex;
    flex-direction: column;
    height: 70vh;
    max-height: 70vh;
  `,
  content: css`
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    min-height: 0;

    /* 优化表单项布局 */
    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-form-item-label {
      white-space: nowrap;
      min-width: 120px;
      width: 120px;
      flex-shrink: 0;
    }

    .ant-form-item-explain {
      white-space: normal;
      word-wrap: break-word;
      hyphens: auto;
    }

    /* 确保描述文字有足够空间 */
    .ant-form-item-control {
      min-width: 0;
      flex: 1;
    }

    /* 开关靠右对齐 */
    .ant-form-item:has(.ant-switch) .ant-form-item-control-input-content {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  `,
  footer: css`
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    padding: 16px 24px;
    border-top: 1px solid ${token.colorBorderSecondary};
    background: ${token.colorBgContainer};
    flex-shrink: 0;
  `,
}));

interface SettingsFormProps {
  onClose?: () => void;
  onSuccess?: () => void;
}

const SettingsForm = memo<SettingsFormProps>(({ onClose, onSuccess }) => {
  const { styles } = useStyles();
  const { t } = useTranslation('worldbook');
  const [form] = Form.useForm();
  const [saving, setSaving] = useState(false);

  // 从store获取当前设置和更新方法
  const [worldbookConfig, updateWorldbookConfig, isLoaded] = useUserStore((s) => [
    worldbookSettingsSelectors.config(s),
    s.updateWorldbookConfig,
    s.isLoaded,
  ]);

  // 同步表单数据
  useEffect(() => {
    if (worldbookConfig && isLoaded) {
      form.setFieldsValue(worldbookConfig);
    }
  }, [form, worldbookConfig, isLoaded]);

  const handleSubmit = async (values: any) => {
    setSaving(true);
    try {
      await updateWorldbookConfig(values);
      message.success(t('settings.messages.saveSuccess'));
      onSuccess?.();
    } catch (error) {
      console.error('Failed to save settings:', error);
      message.error(t('settings.messages.saveError'));
    } finally {
      setSaving(false);
    }
  };

  // 定义表单项组
  const basicSettings: FormGroupItemType = {
    children: [
      {
        children: <Switch />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.basic.enabled.label')}
            <InfoTooltip title={t('settings.basic.enabled.tooltip')} />
          </Flexbox>
        ),
        name: 'enabled',
        valuePropName: 'checked',
      },

      {
        children: <SliderWithInput max={20} min={1} />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.basic.scanDepth.label')}
            <InfoTooltip title={t('settings.basic.scanDepth.tooltip')} />
          </Flexbox>
        ),
        name: 'scanDepth',
      },
      {
        children: <SliderWithInput max={100} min={1} />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.basic.budgetPercent.label')}
            <InfoTooltip title={t('settings.basic.budgetPercent.tooltip')} />
          </Flexbox>
        ),
        name: 'budgetPercent',
      },
    ],
    title: t('settings.basic.title'),
  };

  const advancedSettings: FormGroupItemType = {
    children: [

      {
        children: <SliderWithInput max={5000} min={500} step={100} />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.advanced.budgetCap.label')}
            <InfoTooltip title={t('settings.advanced.budgetCap.tooltip')} />
          </Flexbox>
        ),
        name: 'budgetCap',
      },
      {
        children: <Switch />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.advanced.recursiveEnabled.label')}
            <InfoTooltip title={t('settings.advanced.recursiveEnabled.tooltip')} />
          </Flexbox>
        ),
        name: 'recursiveEnabled',
        valuePropName: 'checked',
      },
      {
        children: <SliderWithInput max={20} min={0} />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.advanced.minActivations.label')}
            <InfoTooltip title={t('settings.advanced.minActivations.tooltip')} />
          </Flexbox>
        ),
        name: 'minActivations',
      },
      {
        children: <SliderWithInput max={20} min={0} />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.advanced.minActivationsDepthMax.label')}
            <InfoTooltip title={t('settings.advanced.minActivationsDepthMax.tooltip')} />
          </Flexbox>
        ),
        name: 'minActivationsDepthMax',
      },
      {
        children: <Switch />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.advanced.useGroupScoring.label')}
            <InfoTooltip title={t('settings.advanced.useGroupScoring.tooltip')} />
          </Flexbox>
        ),
        name: 'useGroupScoring',
        valuePropName: 'checked',
      },
      {
        children: <SliderWithInput max={10} min={1} />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.advanced.maxRecursionDepth.label')}
            <InfoTooltip title={t('settings.advanced.maxRecursionDepth.tooltip')} />
          </Flexbox>
        ),
        name: 'maxRecursionDepth',
      },
    ],
    title: t('settings.advanced.title'),
  };

  const defaultSettings: FormGroupItemType = {
    children: [
      {
        children: <Switch />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.defaults.caseSensitive.label')}
            <InfoTooltip title={t('settings.defaults.caseSensitive.tooltip')} />
          </Flexbox>
        ),
        name: 'caseSensitive',
        valuePropName: 'checked',
      },
      {
        children: <Switch />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.defaults.matchWholeWords.label')}
            <InfoTooltip title={t('settings.defaults.matchWholeWords.tooltip')} />
          </Flexbox>
        ),
        name: 'matchWholeWords',
        valuePropName: 'checked',
      },
      {
        children: <Switch />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.defaults.includeNames.label')}
            <InfoTooltip title={t('settings.defaults.includeNames.tooltip')} />
          </Flexbox>
        ),
        name: 'includeNames',
        valuePropName: 'checked',
      },
      {
        children: <Switch />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.defaults.overflowAlert.label')}
            <InfoTooltip title={t('settings.defaults.overflowAlert.tooltip')} />
          </Flexbox>
        ),
        name: 'overflowAlert',
        valuePropName: 'checked',
      },
      {
        children: <SliderWithInput max={2} min={0} />,
        label: (
          <Flexbox align={'center'} gap={8} horizontal>
            {t('settings.defaults.characterStrategy.label')}
            <InfoTooltip title={t('settings.defaults.characterStrategy.tooltip')} />
          </Flexbox>
        ),
        name: 'characterStrategy',
      },
    ],
    title: t('settings.defaults.title'),
  };

  // 如果数据还没有加载完成，显示加载状态
  if (!isLoaded) {
    return (
      <div className={styles.container}>
        <div className={styles.content}>
          <Flexbox align="center" height="200px" justify="center">
            <Spin size="large" />
          </Flexbox>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <Form
          form={form}
          items={[basicSettings, advancedSettings, defaultSettings]}
          itemsType="group"
          onFinish={handleSubmit}
          variant="borderless"
          {...FORM_STYLE}
        />
      </div>

      <div className={styles.footer}>
        <Button disabled={saving} onClick={onClose}>
          {t('settings.buttons.cancel')}
        </Button>
        <Button
          htmlType="submit"
          loading={saving}
          onClick={() => form.submit()}
          type="primary"
        >
          {saving ? t('settings.buttons.saving') : t('settings.buttons.save')}
        </Button>
      </div>
    </div>
  );
});

SettingsForm.displayName = 'SettingsForm';

export default SettingsForm;
