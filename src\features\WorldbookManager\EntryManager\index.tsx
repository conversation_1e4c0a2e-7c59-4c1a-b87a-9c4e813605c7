'use client';

import { createStyles } from 'antd-style';
import { memo } from 'react';
import { Flexbox } from 'react-layout-kit';

import { useWorldbookStore } from '@/store/worldbook';

import EntryList from './EntryList';

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    height: 100%;
    background: ${token.colorBgLayout};
  `,
}));

interface EntryManagerProps {
  worldbookId: string;
  onCreateEntry?: (worldbookId: string) => void;
  onEditEntry?: (worldbookId: string, entry: any) => void;
}

const EntryManager = memo<EntryManagerProps>(({ worldbookId, onCreateEntry, onEditEntry }) => {
  const { styles } = useStyles();

  return (
    <div className={styles.container}>
      <EntryList
        worldbookId={worldbookId}
        onCreateEntry={onCreateEntry}
        onEditEntry={onEditEntry}
      />
    </div>
  );
});

EntryManager.displayName = 'EntryManager';

export default EntryManager;
