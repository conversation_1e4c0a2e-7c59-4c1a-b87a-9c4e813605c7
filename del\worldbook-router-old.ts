import { z } from 'zod';

import { WorldbookModel } from '@/database/models/worldbook';
import { insertWorldbooksSchema } from '@/database/schemas';
import { authedProcedure, router } from '@/libs/trpc/lambda';
import { serverDatabase } from '@/libs/trpc/lambda/middleware';
import { Worldbook } from '@/types/worldbook';

const worldbookProcedure = authedProcedure.use(serverDatabase).use(async (opts) => {
  const { ctx } = opts;

  return opts.next({
    ctx: {
      worldbookModel: new WorldbookModel(ctx.serverDB, ctx.userId),
    },
  });
});

export const worldbookRouter = router({
  // 获取世界书列表
  getWorldbooks: worldbookProcedure.query(async ({ ctx }): Promise<Worldbook[]> => {
    console.log('🔍 [DEBUG] getWorldbooks called for userId:', ctx.userId);
    const result = await ctx.worldbookModel.query();
    console.log('🔍 [DEBUG] getWorldbooks result:', result);
    return result;
  }),

  // 根据ID获取世界书
  getWorldbookById: worldbookProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }): Promise<Worldbook | undefined> => {
      return ctx.worldbookModel.findById(input.id);
    }),

  // 创建世界书
  createWorldbook: worldbookProcedure
    .input(
      z.object({
        name: z.string(),
        description: z.string().optional(),
        enabled: z.boolean().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const data = await ctx.worldbookModel.create({
        name: input.name,
        description: input.description,
        enabled: input.enabled,
      });

      return data?.id;
    }),

  // 更新世界书
  updateWorldbook: worldbookProcedure
    .input(
      z.object({
        id: z.string(),
        value: insertWorldbooksSchema.partial(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return ctx.worldbookModel.update(input.id, input.value);
    }),

  // 导入世界书（接收项目标准格式，创建世界书和条目）
  importWorldbook: worldbookProcedure
    .input(
      z.object({
        entries: z.array(
          z.object({
            caseSensitive: z.boolean().optional(),
            constant: z.boolean().optional(),
            content: z.string(),
            cooldown: z.number().optional(),
            delay: z.number().optional(),
            delayUntilRecursion: z.number().optional(),
            excludeRecursion: z.boolean().optional(),
            groupName: z.string().optional(),
            groupWeight: z.number().optional(),
            keys: z.array(z.string()),
            keysSecondary: z.array(z.string()).optional(),
            matchWholeWords: z.boolean().optional(),
            order: z.number().optional(),
            position: z.number().optional(),
            probability: z.number().optional(),
            scanDepth: z.number().optional(),
            selectiveLogic: z.number().optional(),
            sticky: z.number().optional(),
            title: z.string(),
            useRegex: z.boolean().optional(),
          }),
        ),
        version: z.string().optional(),
        worldbook: z.object({
          description: z.string().optional(),
          name: z.string(),
        }),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      console.log('🚀 [API] importWorldbook called with:', {
        entriesCount: input.entries.length,
        userId: ctx.userId,
        version: input.version,
        worldbookName: input.worldbook.name,
      });

      // 准备条目数据
      const entriesWithDefaults = input.entries.map(entry => ({
        ...entry,
        constant: entry.constant || false,
        delayUntilRecursion: entry.delayUntilRecursion || 0,
        enabled: true, // 导入时默认启用
        excludeRecursion: entry.excludeRecursion || false,
        groupWeight: entry.groupWeight || 100,
        keysSecondary: entry.keysSecondary || [],
        order: entry.order || 100,
        position: entry.position || 0,
        probability: entry.probability || 100,
        selectiveLogic: entry.selectiveLogic || 0,
        useRegex: entry.useRegex || false,
      }));

      const result = await ctx.worldbookModel.importWorldbook({
        entries: entriesWithDefaults,
        worldbook: input.worldbook,
      });

      console.log('🎉 [API] Import completed:', result);
      return result;
    }),

  // 删除世界书（内部处理所有相关数据的删除）
  removeWorldbook: worldbookProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      console.log('🗑️ [API] Removing worldbook:', input.id);

      const result = await ctx.worldbookModel.delete(input.id);

      console.log('✅ [API] Worldbook and all related data removed:', input.id);
      return result;
    }),

});
