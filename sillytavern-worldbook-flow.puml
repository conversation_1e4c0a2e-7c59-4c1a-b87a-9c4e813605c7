@startuml SillyTavern世界书完整链路
!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontSize 10
skinparam maxMessageSize 150

title SillyTavern 世界书完整激活链路

participant "Chat Generation" as Chat
participant "getWorldInfoPrompt" as GWIP
participant "checkWorldInfo" as CWI
participant "WorldInfoBuffer" as Buffer
participant "WorldInfoTimedEffects" as TimedEffects
participant "VectorExtension" as Vector
participant "getSortedEntries" as GSE

== 初始化阶段 ==

Chat -> GWIP: 调用世界书处理
note right: 传入参数:\n- chat: 消息数组\n- maxContext: 最大上下文\n- isDryRun: 是否干运行\n- globalScanData: 全局扫描数据

GWIP -> CWI: checkWorldInfo(chat, maxContext, isDryRun, globalScanData)

CWI -> Buffer: new WorldInfoBuffer(chat, globalScanData)
note right: 初始化扫描缓冲区:\n- depthBuffer: 深度消息缓冲\n- recurseBuffer: 递归内容缓冲\n- injectBuffer: 注入内容缓冲\n- globalScanData: 全局扫描数据

CWI -> GSE: await getSortedEntries()
note right: 获取排序后的世界书条目\n按 order 字段排序

CWI -> TimedEffects: new WorldInfoTimedEffects(chat, sortedEntries, isDryRun)
note right: 初始化时间效果管理器:\n- sticky: 粘性效果缓冲\n- cooldown: 冷却效果缓冲\n- delay: 延迟效果缓冲

== 向量化预处理 ==

Vector -> Vector: activateWorldInfo(chat)
note right: 向量化扩展处理:\n1. 过滤 vectorized=true 的条目\n2. 按 world 字段分组\n3. 执行语义搜索\n4. 获取相似度匹配结果

Vector -> CWI: eventSource.emit(WORLDINFO_FORCE_ACTIVATE, entries)
note right: 强制激活向量化匹配的条目\n存储到 WorldInfoBuffer.externalActivations

== 主激活循环 ==

CWI -> CWI: 初始化扫描状态
note right: 扫描状态:\n- INITIAL: 初始扫描\n- RECURSION: 递归扫描\n- MIN_ACTIVATIONS: 最小激活扫描\n- NONE: 结束扫描

loop 多轮扫描循环 (while scanState)
    
    CWI -> CWI: count++, 循环计数器
    note right: 检查最大递归步数:\nworld_info_max_recursion_steps
    
    CWI -> CWI: let activatedNow = new Set()
    note right: 当前轮次激活的条目集合
    
    loop 遍历所有排序条目 (for entry of sortedEntries)
        
        == 前置检查阶段 ==
        
        CWI -> CWI: 检查已处理条目
        note right: 跳过条件:\n- failedProbabilityChecks.has(entry)\n- allActivatedEntries.has(entry)
        
        CWI -> CWI: 检查禁用状态
        note right: if (entry.disable == true) continue
        
        CWI -> CWI: 检查角色过滤
        note right: characterFilter 检查:\n- names: 角色名过滤\n- tags: 角色标签过滤\n- isExclude: 排除逻辑
        
        CWI -> TimedEffects: 检查时间效果
        note right: 时间效果检查:\n- isSticky: 粘性效果\n- isCooldown: 冷却效果\n- isDelay: 延迟效果
        
        alt isDelay
            CWI -> CWI: continue (跳过延迟中的条目)
        end
        
        alt isCooldown && !isSticky
            CWI -> CWI: continue (跳过冷却中的非粘性条目)
        end
        
        CWI -> CWI: 检查递归控制
        note right: 递归控制检查:\n- delayUntilRecursion: 延迟到递归\n- excludeRecursion: 排除递归\n- preventRecursion: 阻止递归
        
        alt scanState != RECURSION && delayUntilRecursion && !isSticky
            CWI -> CWI: continue (延迟到递归)
        end
        
        alt scanState == RECURSION && delayUntilRecursion > currentLevel && !isSticky
            CWI -> CWI: continue (递归级别不够)
        end
        
        alt scanState == RECURSION && excludeRecursion && !isSticky
            CWI -> CWI: continue (排除递归)
        end
        
        == 装饰器检查阶段 ==
        
        CWI -> CWI: 检查强制激活装饰器
        note right: if (entry.decorators.includes('@@activate'))
        alt @@activate
            CWI -> CWI: activatedNow.add(entry), continue
        end
        
        CWI -> CWI: 检查禁用装饰器
        note right: if (entry.decorators.includes('@@dont_activate'))
        alt @@dont_activate
            CWI -> CWI: continue (跳过)
        end
        
        == 外部激活检查 ==
        
        CWI -> Buffer: getExternallyActivated(entry)
        note right: 检查向量化激活的条目\nWorldInfoBuffer.externalActivations
        
        alt 外部激活
            CWI -> CWI: activatedNow.add(entry), continue
            note right: 向量化激活的条目在此处理\n跳过后续关键词匹配
        end
        
        == 激活模式检查 ==
        
        CWI -> CWI: 检查常驻激活
        note right: if (entry.constant)
        alt constant
            CWI -> CWI: activatedNow.add(entry), continue
        end
        
        CWI -> CWI: 检查粘性激活
        note right: if (isSticky)
        alt sticky
            CWI -> CWI: activatedNow.add(entry), continue
        end
        
        == 关键词匹配阶段 ==
        
        CWI -> CWI: 检查关键词数组
        note right: if (!Array.isArray(entry.key) || !entry.key.length)
        alt 无关键词
            CWI -> CWI: continue (跳过)
        end
        
        CWI -> Buffer: textToScan = buffer.get(entry, scanState)
        note right: 构建扫描文本:\n- 深度消息缓冲\n- 匹配源内容\n- 递归缓冲内容\n- 注入缓冲内容
        
        CWI -> CWI: 主关键词匹配
        note right: 主关键词匹配:\nentry.key.find(key => \n  buffer.matchKeys(textToScan, key, entry))
        
        CWI -> Buffer: matchKeys(textToScan, key, entry)
        note right: 关键词匹配选项:\n- useRegex: 正则表达式匹配\n- caseSensitive: 大小写敏感\n- matchWholeWords: 全词匹配
        
        alt 主关键词不匹配
            CWI -> CWI: continue (跳过)
        end
        
        CWI -> CWI: 检查次关键词
        note right: hasSecondaryKeywords = \n  entry.selective && \n  Array.isArray(entry.keysecondary) && \n  entry.keysecondary.length
        
        alt 无次关键词
            CWI -> CWI: activatedNow.add(entry), continue
        end
        
        CWI -> CWI: 次关键词逻辑检查
        note right: selectiveLogic 逻辑:\n- AND_ANY: 任一匹配\n- NOT_ALL: 非全部匹配\n- NOT_ANY: 全部不匹配\n- AND_ALL: 全部匹配
        
        loop 遍历次关键词 (for keysecondary)
            CWI -> Buffer: matchKeys(textToScan, keysecondary, entry)
            CWI -> CWI: 更新匹配状态 (hasAnyMatch, hasAllMatch)
        end
        
        alt 次关键词逻辑匹配
            CWI -> CWI: activatedNow.add(entry), continue
        else 次关键词逻辑不匹配
            CWI -> CWI: continue (跳过)
        end
        
    end
    
    == 激活后处理阶段 ==
    
    CWI -> CWI: 排序激活条目
    note right: 排序规则:\n1. sticky 条目优先\n2. 按 sortedEntries 中的顺序
    
    CWI -> CWI: filterByInclusionGroups(newEntries, ...)
    note right: 分组过滤处理:\n- 按 group 字段分组\n- sticky 条目优先\n- groupOverride 优先级\n- groupWeight 权重随机
    
    loop 遍历激活条目进行概率检查
        
        CWI -> CWI: verifyProbability(entry)
        note right: 概率检查:\n- useProbability: 是否使用概率\n- probability: 概率值 (0-100)\n- sticky 条目跳过概率检查
        
        alt 概率检查失败
            CWI -> CWI: failedProbabilityChecks.add(entry), continue
        end
        
        CWI -> CWI: 替换宏参数
        note right: entry.content = substituteParams(entry.content)
        
        CWI -> CWI: 预算检查
        note right: Token 预算检查:\n- world_info_budget: 预算百分比\n- world_info_budget_cap: 预算上限\n- 累计 token 数量检查
        
        alt 预算溢出
            CWI -> CWI: token_budget_overflowed = true, break
        end
        
        CWI -> CWI: allActivatedEntries.set(entry)
        note right: 成功激活条目
        
    end
    
    == 递归准备阶段 ==
    
    CWI -> CWI: 过滤递归条目
    note right: successfulNewEntriesForRecursion = \n  successfulNewEntries.filter(x => !x.preventRecursion)
    
    CWI -> CWI: 确定下一轮扫描状态
    note right: 下一轮扫描条件:\n- world_info_recursive && 有新递归条目\n- MIN_ACTIVATIONS 模式检查\n- 最小激活数检查\n- 延迟递归级别检查
    
    alt 需要递归扫描
        CWI -> Buffer: addRecurse(递归内容)
        note right: 将激活条目内容加入递归缓冲区
        CWI -> CWI: scanState = RECURSION
    else 需要最小激活扫描
        CWI -> Buffer: advanceScan()
        note right: 增加扫描深度
        CWI -> CWI: scanState = MIN_ACTIVATIONS
    else 有延迟递归级别
        CWI -> CWI: scanState = RECURSION, 更新递归级别
    else 扫描完成
        CWI -> CWI: scanState = NONE
    end
    
end

== 构建提示阶段 ==

CWI -> CWI: 按位置分组激活条目
note right: 位置分组:\n- WIBeforeEntries: before\n- WIAfterEntries: after\n- ANTopEntries: ANTop\n- ANBottomEntries: ANBottom\n- EMEntries: EMTop/EMBottom\n- WIDepthEntries: atDepth

loop 遍历所有激活条目
    CWI -> CWI: 处理正则表达式替换
    note right: getRegexedString(entry.content, ...)
    
    CWI -> CWI: 根据位置插入内容
    note right: 位置处理:\n- position: 插入位置\n- depth: 插入深度 (atDepth)\n- role: 角色类型 (atDepth)
end

CWI -> TimedEffects: setTimedEffects(激活条目)
note right: 设置时间效果:\n- sticky 效果设置\n- cooldown 效果设置\n- delay 效果设置

CWI -> Buffer: resetExternalEffects()
note right: 清理外部激活状态

CWI -> GWIP: 返回激活结果
note right: 返回数据:\n- worldInfoBefore: 前置内容\n- worldInfoAfter: 后置内容\n- WIDepthEntries: 深度条目\n- EMEntries: 示例消息条目\n- ANBeforeEntries/ANAfterEntries: AN条目\n- allActivatedEntries: 所有激活条目

GWIP -> Chat: 返回格式化的世界书内容

== 数据结构详情 ==

note over Buffer
**WorldInfoBuffer 数据结构**
- depthBuffer: string[] (深度消息缓冲)
- recurseBuffer: string[] (递归内容缓冲)
- injectBuffer: string[] (注入内容缓冲)
- globalScanData: WIGlobalScanData
  - personaDescription: string
  - characterDescription: string
  - characterPersonality: string
  - characterDepthPrompt: string
  - scenario: string
  - creatorNotes: string
- static externalActivations: Map<string, object>

**关键方法**
- get(entry, scanState): 构建扫描文本
- matchKeys(haystack, needle, entry): 关键词匹配
- addRecurse(message): 添加递归内容
- addInject(message): 添加注入内容
- getExternallyActivated(entry): 获取外部激活条目
end note

note over TimedEffects
**WorldInfoTimedEffects 数据结构**
- buffer: Record<TimedEffectType, WIScanEntry[]>
  - sticky: WIScanEntry[] (粘性效果缓冲)
  - cooldown: WIScanEntry[] (冷却效果缓冲)
  - delay: WIScanEntry[] (延迟效果缓冲)
- chat: string[] (聊天消息)
- entries: WIScanEntry[] (世界书条目)
- isDryRun: boolean (是否干运行)

**关键方法**
- isEffectActive(effectType, entry): 检查效果是否激活
- setTimedEffects(activatedEntries): 设置时间效果
- checkTimedEffects(): 检查时间效果
end note

note over CWI
**世界书条目完整字段结构**

**基础字段**
- uid: string (唯一标识)
- world: string (世界书名称)
- key: string[] (主关键词数组)
- keysecondary: string[] (次关键词数组)
- comment: string (条目标题/注释)
- content: string (条目内容)

**激活控制字段**
- constant: boolean (常驻激活)
- vectorized: boolean (向量化激活)
- selective: boolean (选择性激活，默认true)
- selectiveLogic: number (次关键词逻辑)
  - 0: AND_ANY (任一匹配)
  - 1: NOT_ALL (非全部匹配)
  - 2: NOT_ANY (全部不匹配)
  - 3: AND_ALL (全部匹配)

**位置控制字段**
- position: number (插入位置)
  - 0: before (消息前)
  - 1: after (消息后)
  - 2: ANTop (作者注释顶部)
  - 3: ANBottom (作者注释底部)
  - 4: atDepth (指定深度)
  - 5: EMTop (示例消息顶部)
  - 6: EMBottom (示例消息底部)
- order: number (优先级，数值越大优先级越高)
- depth: number (插入深度，用于atDepth位置)
- role: number (角色类型，用于atDepth位置)
  - 0: SYSTEM
  - 1: USER
  - 2: ASSISTANT

**匹配控制字段**
- disable: boolean (禁用状态)
- caseSensitive: boolean (大小写敏感)
- matchWholeWords: boolean (全词匹配)
- useGroupScoring: boolean (使用分组评分)
- scanDepth: number (扫描深度)

**匹配源字段**
- matchPersonaDescription: boolean (匹配人格描述)
- matchCharacterDescription: boolean (匹配角色描述)
- matchCharacterPersonality: boolean (匹配角色性格)
- matchCharacterDepthPrompt: boolean (匹配角色深度提示)
- matchScenario: boolean (匹配场景)
- matchCreatorNotes: boolean (匹配创作者笔记)

**概率控制字段**
- probability: number (激活概率 0-100)
- useProbability: boolean (是否使用概率)

**时间效果字段**
- sticky: number (粘性效果持续轮数)
- cooldown: number (冷却时间轮数)
- delay: number (延迟激活轮数)

**递归控制字段**
- excludeRecursion: boolean (排除递归)
- preventRecursion: boolean (阻止递归)
- delayUntilRecursion: number|boolean (延迟到递归)

**分组字段**
- group: string (分组名称，逗号分隔多组)
- groupOverride: boolean (分组优先级)
- groupWeight: number (分组权重)

**角色过滤字段**
- characterFilter: object
  - names: string[] (角色名过滤)
  - tags: string[] (角色标签过滤)
  - isExclude: boolean (排除逻辑)

**装饰器字段**
- decorators: string[] (装饰器数组)
  - "@@activate": 强制激活
  - "@@dont_activate": 禁止激活

**其他字段**
- addMemo: boolean (添加备忘录)
- automationId: string (自动化标识)
end note

== 全局配置参数 ==

note over CWI
**世界书全局配置参数**

**扫描控制**
- world_info_depth: number (默认扫描深度)
- world_info_recursive: boolean (是否启用递归)
- world_info_max_recursion_steps: number (最大递归步数)
- world_info_include_names: boolean (消息是否包含角色名)

**激活控制**
- world_info_min_activations: number (最小激活数)
- world_info_min_activations_depth_max: number (最小激活最大深度)

**预算控制**
- world_info_budget: number (预算百分比)
- world_info_budget_cap: number (预算上限)
- world_info_overflow_alert: boolean (预算溢出警告)

**匹配控制**
- world_info_case_sensitive: boolean (全局大小写敏感)
- world_info_match_whole_words: boolean (全局全词匹配)
- world_info_use_group_scoring: boolean (全局分组评分)
- world_info_character_strategy: number (角色策略)

**扫描状态枚举**
- scan_state.INITIAL: 0 (初始扫描)
- scan_state.RECURSION: 1 (递归扫描)
- scan_state.MIN_ACTIVATIONS: 2 (最小激活扫描)
- scan_state.NONE: 3 (结束扫描)

**常量定义**
- MAX_SCAN_DEPTH: 1000 (最大扫描深度)
- DEFAULT_DEPTH: 4 (默认深度)
- DEFAULT_WEIGHT: 100 (默认权重)
end note

== 向量化扩展详情 ==

note over Vector
**向量化扩展配置**
- enabled_world_info: boolean (启用世界书向量化)
- enabled_for_all: boolean (为所有条目启用，忽略vectorized字段)
- max_entries: number (最大激活条目数)
- score_threshold: number (相似度阈值)

**向量化处理流程**
1. 过滤条件检查:
   - !entry.world: 跳过孤立条目
   - entry.disable: 跳过禁用条目
   - !entry.content: 跳过无内容条目
   - !entry.vectorized && !enabled_for_all: 跳过非向量化条目

2. 按world字段分组:
   - 每个world创建独立的向量集合
   - collectionId = `world_${getStringHash(world)}`

3. 向量同步:
   - 新条目: 计算向量并插入
   - 删除条目: 从向量集合中移除

4. 语义搜索:
   - 构建查询文本: getQueryText(chat, 'world-info')
   - 多集合查询: queryMultipleCollections()
   - 相似度过滤: score >= score_threshold
   - 数量限制: 最多max_entries个结果

5. 强制激活:
   - 发送WORLDINFO_FORCE_ACTIVATE事件
   - 条目存储到WorldInfoBuffer.externalActivations
end note

== 错误处理和边界情况 ==

note over CWI
**错误处理机制**

**条目验证**
- 孤立条目检查: !entry.world
- 空内容检查: !entry.content
- 无效UID检查: !entry.uid
- 关键词数组检查: !Array.isArray(entry.key)

**扫描深度验证**
- 最小深度: depth <= startDepth
- 最大深度: depth > MAX_SCAN_DEPTH
- 负数深度: depth < 0
- 超出消息数: depth > chat.length

**递归保护**
- 最大递归步数检查
- 递归循环检测
- 递归深度限制
- 递归内容长度限制

**预算保护**
- Token计数异常处理
- 预算溢出警告
- 内存使用监控
- 处理时间限制

**概率验证**
- 概率范围检查: 0 <= probability <= 100
- 随机数生成异常处理
- 概率计算精度问题

**正则表达式安全**
- 正则表达式语法验证
- 执行时间限制
- 复杂度检查
- 异常捕获和降级
end note

note over Buffer
**缓冲区边界情况**

**深度缓冲区**
- 空消息数组处理
- 消息长度超限处理
- 特殊字符处理
- 编码问题处理

**递归缓冲区**
- 递归内容累积限制
- 重复内容检测
- 循环引用检测
- 内存泄漏防护

**匹配源处理**
- 空匹配源处理
- 匹配源组合逻辑
- 特殊字符转义
- 长文本截断

**外部激活管理**
- 重复激活检测
- 激活状态清理
- 内存管理
- 并发访问保护
end note

note over TimedEffects
**时间效果边界情况**

**效果持续时间**
- 负数时间处理
- 零时间效果
- 超长时间效果
- 时间溢出处理

**效果状态管理**
- 状态不一致检测
- 状态恢复机制
- 状态持久化
- 状态清理

**聊天元数据**
- 元数据缺失处理
- 元数据格式验证
- 元数据版本兼容
- 元数据损坏恢复

**效果冲突解决**
- 多效果同时激活
- 效果优先级冲突
- 效果取消机制
- 效果覆盖逻辑
end note

== 性能优化点 ==

note over CWI
**性能关键点**

**扫描优化**
- 早期退出条件
- 缓存机制使用
- 批量处理
- 并行处理可能性

**内存优化**
- 对象复用
- 垃圾回收优化
- 内存池使用
- 大对象处理

**算法优化**
- 排序算法选择
- 搜索算法优化
- 正则表达式编译缓存
- 字符串操作优化

**I/O优化**
- 批量数据库操作
- 异步处理
- 缓存策略
- 预加载机制
end note

note over Vector
**向量化性能优化**

**向量计算优化**
- 批量向量生成
- 向量缓存机制
- 相似度计算优化
- 索引结构优化

**存储优化**
- 向量压缩
- 分片存储
- 增量更新
- 清理策略

**查询优化**
- 查询缓存
- 结果预过滤
- 并行查询
- 查询合并
end note

== 调试和监控 ==

note over CWI
**调试信息输出**

**激活日志**
- 条目激活原因
- 跳过条件详情
- 匹配过程追踪
- 时间效果状态

**性能监控**
- 扫描耗时统计
- 内存使用监控
- 激活条目统计
- 错误率监控

**状态检查**
- 扫描状态变化
- 缓冲区状态
- 时间效果状态
- 配置参数状态

**错误追踪**
- 异常堆栈信息
- 错误上下文保存
- 错误恢复记录
- 错误统计分析
end note

@enduml
