{"firstRun": true, "username": "User", "api_server": "http://127.0.0.1:5000/api", "preset_settings": "Recovered<PERSON><PERSON><PERSON>", "user_avatar": "user-default.png", "amount_gen": 350, "max_context": 8192, "main_api": "koboldhorde", "world_info_settings": {"world_info": {"globalSelect": []}, "world_info_depth": 2, "world_info_budget": 25, "world_info_include_names": true, "world_info_recursive": true, "world_info_overflow_alert": false, "world_info_case_sensitive": false, "world_info_match_whole_words": true, "world_info_character_strategy": 1, "world_info_budget_cap": 0}, "textgenerationwebui_settings": {"temp": 0.5, "top_p": 0.9, "top_k": 0, "top_a": 0, "tfs": 1, "epsilon_cutoff": 0, "eta_cutoff": 0, "typical_p": 1, "rep_pen": 1.1, "rep_pen_range": 0, "no_repeat_ngram_size": 0, "penalty_alpha": 0, "num_beams": 1, "length_penalty": 1, "min_length": 0, "encoder_rep_pen": 1, "do_sample": true, "early_stopping": false, "seed": -1, "preset": "<PERSON><PERSON><PERSON>", "add_bos_token": true, "stopping_strings": [], "ban_eos_token": false, "skip_special_tokens": true, "streaming": false, "sampler_priority": ["temperature", "dynamic_temperature", "quadratic_sampling", "top_k", "top_p", "typical_p", "epsilon_cutoff", "eta_cutoff", "tfs", "top_a", "min_p", "mi<PERSON><PERSON>"], "samplers": ["top_k", "tfs_z", "typical_p", "top_p", "min_p", "temperature"], "mirostat_mode": 0, "mirostat_tau": 5, "mirostat_eta": 0.1, "guidance_scale": 1, "negative_prompt": "", "rep_pen_size": 0}, "swipes": true, "horde_settings": {"models": [], "auto_adjust_response_length": true, "auto_adjust_context_length": false, "trusted_workers_only": false}, "power_user": {"tokenizer": 99, "token_padding": 64, "collapse_newlines": false, "pin_examples": false, "strip_examples": false, "trim_sentences": false, "always_force_name2": true, "user_prompt_bias": "", "show_user_prompt_bias": true, "markdown_escape_strings": "", "fast_ui_mode": true, "avatar_style": 0, "chat_display": 0, "chat_width": 50, "never_resize_avatars": false, "show_card_avatar_urls": false, "play_message_sound": false, "play_sound_unfocused": true, "auto_save_msg_edits": false, "confirm_message_delete": true, "sort_field": "name", "sort_order": "asc", "sort_rule": null, "font_scale": 1, "blur_strength": 10, "shadow_width": 2, "main_text_color": "rgba(220, 220, 210, 1)", "italics_text_color": "rgba(145, 145, 145, 1)", "underline_text_color": "rgba(188, 231, 207, 1)", "quote_text_color": "rgba(225, 138, 36, 1)", "chat_tint_color": "rgba(23, 23, 23, 1)", "blur_tint_color": "rgba(23, 23, 23, 1)", "user_mes_blur_tint_color": "rgba(30, 30, 30, 0.9)", "bot_mes_blur_tint_color": "rgba(30, 30, 30, 0.9)", "shadow_color": "rgba(0, 0, 0, 1)", "waifuMode": false, "movingUI": false, "movingUIState": {}, "movingUIPreset": "<PERSON><PERSON><PERSON>", "noShadows": true, "theme": "Dark Lite", "auto_swipe": false, "auto_swipe_minimum_length": 0, "auto_swipe_blacklist": [], "auto_swipe_blacklist_threshold": 2, "auto_scroll_chat_to_bottom": true, "auto_fix_generated_markdown": false, "send_on_enter": 0, "console_log_prompts": false, "allow_name1_display": false, "allow_name2_display": false, "hotswap_enabled": true, "timer_enabled": false, "timestamps_enabled": true, "timestamp_model_icon": true, "mesIDDisplay_enabled": false, "hideChatAvatars_enabled": false, "max_context_unlocked": false, "prefer_character_prompt": true, "prefer_character_jailbreak": true, "quick_continue": false, "continue_on_send": false, "trim_spaces": true, "relaxed_api_urls": false, "instruct": {"enabled": false, "preset": "Alpaca", "input_sequence": "### Instruction:", "output_sequence": "### Response:", "last_output_sequence": "", "system_sequence": "### Input:", "stop_sequence": "", "wrap": true, "macro": true, "names_behavior": "force", "activation_regex": "", "system_sequence_prefix": "", "system_sequence_suffix": "", "first_output_sequence": "", "skip_examples": false, "output_suffix": "\n\n", "input_suffix": "\n\n", "system_suffix": "\n\n", "user_alignment_message": "", "system_same_as_user": false}, "sysprompt": {"enabled": true, "name": "Neutral - Chat", "content": "Write {{char}}'s next reply in a fictional chat between {{char}} and {{user}}."}, "context": {"preset": "<PERSON><PERSON><PERSON>", "story_string": "{{#if system}}{{system}}\n{{/if}}{{#if wiBefore}}{{wiBefore}}\n{{/if}}{{#if description}}{{description}}\n{{/if}}{{#if personality}}{{char}}'s personality: {{personality}}\n{{/if}}{{#if scenario}}Scenario: {{scenario}}\n{{/if}}{{#if wiAfter}}{{wiAfter}}\n{{/if}}{{#if persona}}{{persona}}\n{{/if}}", "chat_start": "***", "example_separator": "***", "use_stop_strings": true}, "personas": {}, "default_persona": null, "persona_descriptions": {}, "persona_description": "", "persona_description_position": 0, "persona_show_notifications": true, "custom_stopping_strings": "", "custom_stopping_strings_macro": true, "fuzzy_search": true, "encode_tags": false, "enableLabMode": false, "enableZenSliders": false, "ui_mode": 1, "forbid_external_media": true, "stscript": {"parser": {"flags": {"1": true, "2": true}}}}, "extension_settings": {"apiUrl": "http://localhost:5100", "apiKey": "", "autoConnect": false, "disabledExtensions": [], "expressionOverrides": [], "memory": {"minLongMemory": 16, "maxLongMemory": 1024, "longMemoryLength": 128, "shortMemoryLength": 512, "minShortMemory": 128, "maxShortMemory": 1024, "shortMemoryStep": 16, "longMemoryStep": 8, "repetitionPenaltyStep": 0.05, "repetitionPenalty": 1.2, "maxRepetitionPenalty": 2, "minRepetitionPenalty": 1, "temperature": 1, "minTemperature": 0.1, "maxTemperature": 2, "temperatureStep": 0.05, "lengthPenalty": 1, "minLengthPenalty": -4, "maxLengthPenalty": 4, "lengthPenaltyStep": 0.1, "memoryFrozen": false, "source": "extras", "prompt": "Ignore previous instructions. Summarize the most important facts and events in the story so far. If a summary already exists in your memory, use that as a base and expand with new facts. Limit the summary to {{words}} words or less. Your response should include nothing but the summary.", "promptWords": 200, "promptMinWords": 25, "promptMaxWords": 1000, "promptWordsStep": 25, "promptInterval": 10, "promptMinInterval": 1, "promptMaxInterval": 100, "promptIntervalStep": 1, "template": "[Summary: {{summary}}]", "position": 0, "depth": 2, "promptForceWords": 0, "promptForceWordsStep": 100, "promptMinForceWords": 0, "promptMaxForceWords": 10000}, "note": {"default": "", "chara": [], "wiAddition": []}, "caption": {"refine_mode": false}, "expressions": {"showDefault": false}, "dice": {}, "regex": [], "tts": {"voiceMap": "", "ttsEnabled": false, "currentProvider": "System", "auto_generation": true, "ElevenLabs": {}, "System": {}}, "sd": {"scale_min": 1, "scale_max": 30, "scale_step": 0.5, "scale": 7, "steps_min": 1, "steps_max": 150, "steps_step": 1, "steps": 20, "dimension_min": 64, "dimension_max": 2048, "dimension_step": 64, "width": 512, "height": 512, "prompt_prefix": "best quality, absurdres, masterpiece,", "negative_prompt": "lowres, bad anatomy, bad hands, text, error, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry", "sampler": "DDIM", "model": "", "restore_faces": false, "enable_hr": false, "horde": true, "horde_nsfw": false, "horde_karras": true, "refine_mode": false, "prompts": {"0": "In the next response I want you to provide only a detailed comma-delimited list of keywords and phrases which describe {{char}}. The list must include all of the following items in this order: name, species and race, gender, age, clothing, occupation, physical features and appearances. Do not include descriptions of non-visual qualities such as personality, movements, scents, mental traits, or anything which could not be seen in a still photograph. Do not write in full sentences. Prefix your description with the phrase 'full body portrait,'", "1": "Ignore previous instructions and provide a detailed description of {{user}}'s physical appearance from the perspective of {{char}} in the form of a comma-delimited list of keywords and phrases. The list must include all of the following items in this order: name, species and race, gender, age, clothing, occupation, physical features and appearances. Do not include descriptions of non-visual qualities such as personality, movements, scents, mental traits, or anything which could not be seen in a still photograph. Do not write in full sentences. Prefix your description with the phrase 'full body portrait,'. Ignore the rest of the story when crafting this description. Do not reply as {{char}} when writing this description, and do not attempt to continue the story.", "2": "Ignore previous instructions and provide a detailed description for all of the following: a brief recap of recent events in the story, {{char}}'s appearance, and {{char}}'s surroundings. Do not reply as {{char}} when writing this description, and do not attempt to continue the story.", "3": "Ignore previous instructions and provide <PERSON><PERSON><PERSON> the last chat message string back to me verbatim. Do not write anything after the string. Do not reply as {{char}} when writing this description, and do not attempt to continue the story.", "4": "Ignore previous instructions. Your next response must be formatted as a single comma-delimited list of concise keywords.  The list will describe of the visual details included in the last chat message.\n\n    Only mention characters by using pronouns ('he','his','she','her','it','its') or neutral nouns ('male', 'the man', 'female', 'the woman').\n\n    Ignore non-visible things such as feelings, personality traits, thoughts, and spoken dialog.\n\n    Add keywords in this precise order:\n    a keyword to describe the location of the scene,\n    a keyword to mention how many characters of each gender or type are present in the scene (minimum of two characters:\n    {{user}} and {{char}}, example: '2 men ' or '1 man 1 woman ', '1 man 3 robots'),\n\n    keywords to describe the relative physical positioning of the characters to each other (if a commonly known term for the positioning is known use it instead of describing the positioning in detail) + 'POV',\n\n    a single keyword or phrase to describe the primary act taking place in the last chat message,\n\n    keywords to describe {{char}}'s physical appearance and facial expression,\n    keywords to describe {{char}}'s actions,\n    keywords to describe {{user}}'s physical appearance and actions.\n\n    If character actions involve direct physical interaction with another character, mention specifically which body parts interacting and how.\n\n    A correctly formatted example response would be:\n    '(location),(character list by gender),(primary action), (relative character position) POV, (character 1's description and actions), (character 2's description and actions)'", "5": "In the next response I want you to provide only a detailed comma-delimited list of keywords and phrases which describe {{char}}. The list must include all of the following items in this order: name, species and race, gender, age, facial features and expressions, occupation, hair and hair accessories (if any), what they are wearing on their upper body (if anything). Do not describe anything below their neck. Do not include descriptions of non-visual qualities such as personality, movements, scents, mental traits, or anything which could not be seen in a still photograph. Do not write in full sentences. Prefix your description with the phrase 'close up facial portrait,'", "7": "Ignore previous instructions and provide a detailed description of {{char}}'s surroundings in the form of a comma-delimited list of keywords and phrases. The list must include all of the following items in this order: location, time of day, weather, lighting, and any other relevant details. Do not include descriptions of characters and non-visual qualities such as names, personality, movements, scents, mental traits, or anything which could not be seen in a still photograph. Do not write in full sentences. Prefix your description with the phrase 'background,'. Ignore the rest of the story when crafting this description. Do not reply as {{user}} when writing this description, and do not attempt to continue the story."}, "character_prompts": {}}, "chromadb": {}, "translate": {"target_language": "en", "internal_language": "en", "provider": "google", "auto_mode": "none"}, "objective": {"customPrompts": {"default": {"createTask": "Ignore previous instructions and generate a list of tasks to complete an objective. Your next response must be formatted as a numbered list of plain text entries. Do not include anything but the numbered list. The list must be prioritized in the order that tasks must be completed.\n\nThe objective that you must make a numbered task list for is: [{{objective}}].\nThe tasks created should take into account the character traits of {{char}}. These tasks may or may not involve {{user}} directly. Be sure to include the objective as the final task.\n\nGiven an example objective of 'Make me a four course dinner', here is an example output:\n1. Determine what the courses will be\n2. Find recipes for each course\n3. Go shopping for supplies with {{user}}\n4. Cook the food\n5. Get {{user}} to set the table\n6. Serve the food\n7. Enjoy eating the meal with {{user}}\n    ", "checkTaskCompleted": "Ignore previous instructions. Determine if this task is completed: [{{task}}].\nTo do this, examine the most recent messages. Your response must only contain either true or false, nothing other words.\nExample output:\ntrue\n    ", "currentTask": "Your current task is [{{task}}]. Balance existing story with completing this task."}}}, "quickReply": {"quickReplyEnabled": false, "numberOfSlots": 5, "quickReplySlots": [{"mes": "", "label": "", "enabled": true}, {"mes": "", "label": "", "enabled": true}, {"mes": "", "label": "", "enabled": true}, {"mes": "", "label": "", "enabled": true}, {"mes": "", "label": "", "enabled": true}]}, "randomizer": {"controls": [], "fluctuation": 0.1, "enabled": false}, "speech_recognition": {"currentProvider": "None", "messageMode": "append", "messageMappingText": "", "messageMapping": [], "messageMappingEnabled": false, "None": {}}, "rvc": {"enabled": false, "model": "", "pitchOffset": 0, "pitchExtraction": "dio", "indexRate": 0.88, "filterRadius": 3, "rmsMixRate": 1, "protect": 0.33, "voicMapText": "", "voiceMap": {}}, "cfg": {"global": {"guidance_scale": 1, "negative_prompt": ""}, "chara": []}}, "tags": [{"id": "1345561466591", "name": "ST Default", "color": "rgba(108, 32, 32, 1)"}], "tag_map": {"default_Seraphina.png": ["1345561466591"]}, "nai_settings": {"temperature": 1.5, "repetition_penalty": 2.25, "repetition_penalty_range": 2048, "repetition_penalty_slope": 0.09, "repetition_penalty_frequency": 0, "repetition_penalty_presence": 0.005, "tail_free_sampling": 0.975, "top_k": 10, "top_p": 0.75, "top_a": 0.08, "typical_p": 0.975, "min_length": 1, "model_novel": "clio-v1", "preset_settings_novel": "Talker-<PERSON><PERSON><PERSON><PERSON><PERSON>", "streaming_novel": true, "preamble": "[ Style: chat, complex, sensory, visceral ]", "banned_tokens": "", "order": [1, 5, 0, 2, 3, 4], "logit_bias": []}, "kai_settings": {"temp": 1, "rep_pen": 1.1, "rep_pen_range": 600, "top_p": 0.95, "top_a": 0, "top_k": 0, "typical": 1, "tfs": 1, "rep_pen_slope": 0, "streaming_kobold": false, "sampler_order": [6, 0, 1, 2, 3, 4, 5], "mirostat": 0, "mirostat_tau": 5, "mirostat_eta": 0.1, "use_default_badwordsids": false, "grammar": ""}, "oai_settings": {"preset_settings_openai": "<PERSON><PERSON><PERSON>", "temp_openai": 1.0, "freq_pen_openai": 0, "pres_pen_openai": 0, "top_p_openai": 1, "top_k_openai": 0, "stream_openai": true, "openai_max_context": 4095, "openai_max_tokens": 300, "wrap_in_quotes": false, "prompts": [{"name": "Main Prompt", "system_prompt": true, "role": "system", "content": "Write {{char}}'s next reply in a fictional chat between {{char}} and {{user}}.", "identifier": "main"}, {"name": "Auxiliary Prompt", "system_prompt": true, "role": "system", "content": "", "identifier": "nsfw"}, {"identifier": "dialogue<PERSON><PERSON><PERSON>s", "name": "Chat Examples", "system_prompt": true, "marker": true}, {"name": "Post-History Instructions", "system_prompt": true, "role": "system", "content": "", "identifier": "jailbreak"}, {"identifier": "chatHistory", "name": "Chat History", "system_prompt": true, "marker": true}, {"identifier": "worldInfoAfter", "name": "World Info (after)", "system_prompt": true, "marker": true}, {"identifier": "worldInfoBefore", "name": "World Info (before)", "system_prompt": true, "marker": true}, {"identifier": "enhanceDefinitions", "role": "system", "name": "Enhance Definitions", "content": "If you have more knowledge of {{char}}, add to the character's lore and personality to enhance them but keep the Character Sheet's definitions absolute.", "system_prompt": true, "marker": false}, {"identifier": "charDescription", "name": "Char Description", "system_prompt": true, "marker": true}, {"identifier": "charPersonality", "name": "Char Personality", "system_prompt": true, "marker": true}, {"identifier": "scenario", "name": "<PERSON><PERSON><PERSON>", "system_prompt": true, "marker": true}], "prompt_order": [{"character_id": 100000, "order": [{"identifier": "main", "enabled": true}, {"identifier": "worldInfoBefore", "enabled": true}, {"identifier": "charDescription", "enabled": true}, {"identifier": "charPersonality", "enabled": true}, {"identifier": "scenario", "enabled": true}, {"identifier": "enhanceDefinitions", "enabled": false}, {"identifier": "nsfw", "enabled": true}, {"identifier": "worldInfoAfter", "enabled": true}, {"identifier": "dialogue<PERSON><PERSON><PERSON>s", "enabled": true}, {"identifier": "chatHistory", "enabled": true}, {"identifier": "jailbreak", "enabled": true}]}], "send_if_empty": "", "impersonation_prompt": "[Write your next reply from the point of view of {{user}}, using the chat history so far as a guideline for the writing style of {{user}}. Write 1 reply only in internet RP style. Don't write as {{char}} or system. Don't describe actions of {{char}}.]", "new_chat_prompt": "[Start a new Chat]", "new_group_chat_prompt": "[Start a new group chat. Group members: {{group}}]", "new_example_chat_prompt": "[Example Chat]", "continue_nudge_prompt": "[Continue your last message without repeating its original content.]", "bias_preset_selected": "<PERSON><PERSON><PERSON> (none)", "bias_presets": {"Default (none)": [], "Anti-bond": [{"id": "22154f79-dd98-41bc-8e34-87015d6a0eaf", "text": " bond", "value": -50}, {"id": "8ad2d5c4-d8ef-49e4-bc5e-13e7f4690e0f", "text": " future", "value": -50}, {"id": "52a4b280-0956-4940-ac52-4111f83e4046", "text": " bonding", "value": -50}, {"id": "e63037c7-c9d1-4724-ab2d-7756008b433b", "text": " connection", "value": -25}]}, "wi_format": "{0}", "openai_model": "gpt-4-turbo", "claude_model": "claude-3-5-sonnet-20240620", "ai21_model": "jamba-1.5-large", "windowai_model": "", "openrouter_model": "OR_Website", "reverse_proxy": "", "chat_completion_source": "openai", "max_context_unlocked": false, "api_url_scale": "", "show_external_models": false, "proxy_password": "", "assistant_prefill": "", "assistant_impersonation": ""}}