import { StateCreator } from 'zustand';

import { worldbookService } from '@/services/worldbook/client';
import type { CreateEntryData, UpdateEntryData, WorldbookEntry } from '@/types/worldbook';

import type { WorldbookStore } from '../../store';

export interface WorldbookEntryAction {
  bulkDeleteEntries: (ids: string[]) => Promise<void>;
  // 批量操作
  bulkUpdateEntries: (ids: string[], data: Partial<WorldbookEntry>) => Promise<void>;
  clearEntrySelection: () => void;
  createEntry: (worldbookId: string, data: CreateEntryData) => Promise<void>;
  deleteEntry: (id: string) => Promise<void>;

  // 条目操作
  fetchEntries: (worldbookId: string, params?: any) => Promise<void>;
  loadMoreEntries: (worldbookId: string) => Promise<void>;
  resetEntries: () => void;
  internal_toggleEntryLoading: (id: string, loading: boolean) => void;
  internal_toggleEntryUpdating: (id: string, loading: boolean) => void;
  selectAllEntries: () => void;
  setCurrentEntry: (id: string | null) => void;
  setSelectedEntryIds: (ids: string[]) => void;
  toggleEntrySelection: (id: string) => void;
  updateEntry: (id: string, data: UpdateEntryData) => Promise<void>;

  // 暂时移除SWR hooks，避免服务端渲染问题
  // useFetchEntries: (worldbookId: string) => SWRResponse<WorldbookEntry[]>;
  // useFetchEntry: (id: string) => SWRResponse<WorldbookEntry | undefined>;
}

export const createWorldbookEntrySlice: StateCreator<
  WorldbookStore,
  [['zustand/devtools', never]],
  [],
  WorldbookEntryAction
> = (set, get) => ({
  bulkDeleteEntries: async (ids) => {
    try {
      await worldbookService.bulkDeleteEntries(ids);
      set((state) => ({
        entries: state.entries.filter((entry) => !ids.includes(entry.id)),
        selectedEntryIds: state.selectedEntryIds.filter((id) => !ids.includes(id)),
      }));
    } catch (error) {
      console.error('Failed to bulk delete entries:', error);
      throw error;
    }
  },

  bulkUpdateEntries: async (ids, data) => {
    try {
      await worldbookService.bulkUpdateEntries(ids, data);
      set((state) => ({
        entries: state.entries.map((entry) =>
          ids.includes(entry.id) ? { ...entry, ...data } : entry,
        ),
      }));
    } catch (error) {
      console.error('Failed to bulk update entries:', error);
      throw error;
    }
  },

  clearEntrySelection: () => {
    set({ selectedEntryIds: [] });
  },

  createEntry: async (worldbookId, data) => {
    try {
      const entry = await worldbookService.createEntry(worldbookId, data);
      set((state) => ({
        entries: [...state.entries, entry],
      }));
    } catch (error) {
      console.error('Failed to create entry:', error);
      throw error;
    }
  },

  deleteEntry: async (id) => {
    get().internal_toggleEntryLoading(id, true);
    try {
      await worldbookService.deleteEntry(id);
      set((state) => ({
        currentEntry: state.currentEntry?.id === id ? null : state.currentEntry,
        entries: state.entries.filter((entry) => entry.id !== id),
        selectedEntryIds: state.selectedEntryIds.filter((entryId) => entryId !== id),
      }));
    } catch (error) {
      console.error('Failed to delete entry:', error);
      throw error;
    } finally {
      get().internal_toggleEntryLoading(id, false);
    }
  },

  fetchEntries: async (worldbookId, params = {}) => {
    set({ entriesLoading: true });
    try {
      const state = get();
      const searchParams = {
        page: params.page || state.entriesPage,
        pageSize: params.pageSize || state.entriesPageSize,
        query: params.query,
        enabled: params.enabled,
        constant: params.constant,
        hasKeys: params.hasKeys,
        hasSecondaryKeys: params.hasSecondaryKeys,
        groupName: params.groupName,
        selectiveLogic: params.selectiveLogic,
        orderMin: params.orderMin,
        orderMax: params.orderMax,
        probabilityMin: params.probabilityMin,
        probabilityMax: params.probabilityMax,
        sortBy: params.sortBy || 'order',
        sortOrder: params.sortOrder || 'asc',
      };

      const response = await worldbookService.getEntries(worldbookId, searchParams);

      // 如果是第一页，替换数据；否则追加数据（用于无限滚动）
      const entries = searchParams.page === 1
        ? response.items
        : [...state.entries, ...response.items];

      set({
        entries,
        entriesTotal: response.total,
        entriesPage: response.page,
        entriesPageSize: response.pageSize,
        entriesHasMore: response.hasMore,
        entriesLoading: false
      });
    } catch (error) {
      console.error('Failed to fetch entries:', error);
      set({ entriesLoading: false });
      throw error;
    }
  },

  // 加载更多条目（分页）
  loadMoreEntries: async (worldbookId) => {
    const state = get();
    if (!state.entriesHasMore || state.entriesLoading) return;

    await get().fetchEntries(worldbookId, { page: state.entriesPage + 1 });
  },

  // 重置条目列表（用于新的搜索）
  resetEntries: () => {
    set({
      entries: [],
      entriesPage: 1,
      entriesTotal: 0,
      entriesHasMore: false
    });
  },

  selectAllEntries: () => {
    const { entries } = get();
    set({ selectedEntryIds: entries.map((entry) => entry.id) });
  },

  setCurrentEntry: (id) => {
    const entry = id ? get().entries.find((e) => e.id === id) : null;
    set({ currentEntry: entry || null });
  },

  setSelectedEntryIds: (ids) => {
    set({ selectedEntryIds: ids });
  },

  toggleEntrySelection: (id) => {
    set((state) => ({
      selectedEntryIds: state.selectedEntryIds.includes(id)
        ? state.selectedEntryIds.filter((entryId) => entryId !== id)
        : [...state.selectedEntryIds, id],
    }));
  },

  updateEntry: async (id, data) => {
    get().internal_toggleEntryUpdating(id, true);
    try {
      console.log('🔄 [Store] Updating entry:', id, 'with data:', data);
      const updatedEntry = await worldbookService.updateEntry(id, data);
      console.log('✅ [Store] Received updated entry from service:', updatedEntry);
      console.log('📝 [Store] Updated entry content:', updatedEntry?.content);

      set((state) => {
        const oldEntry = state.entries.find(e => e.id === id);
        console.log('🔍 [Store] Old entry in state:', oldEntry);
        console.log('📝 [Store] Old entry content:', oldEntry?.content);

        const newEntries = state.entries.map((entry) => (entry.id === id ? updatedEntry : entry));
        const updatedEntryInList = newEntries.find(e => e.id === id);
        console.log('🆕 [Store] Updated entry in new list:', updatedEntryInList);
        console.log('📝 [Store] Updated entry content in list:', updatedEntryInList?.content);

        return {
          currentEntry: state.currentEntry?.id === id ? updatedEntry : state.currentEntry,
          entries: newEntries,
        };
      });
    } catch (error) {
      console.error('Failed to update entry:', error);
      throw error;
    } finally {
      get().internal_toggleEntryUpdating(id, false);
    }
  },

  // 加载状态管理
  internal_toggleEntryLoading: (id, loading) => {
    set(
      (state) => {
        if (loading) return { entryLoadingIds: [...state.entryLoadingIds, id] };
        return { entryLoadingIds: state.entryLoadingIds.filter((i) => i !== id) };
      },
      false,
      'toggleEntryLoading',
    );
  },

  internal_toggleEntryUpdating: (id, loading) => {
    set(
      (state) => {
        if (loading) return { entryUpdatingIds: [...state.entryUpdatingIds, id] };
        return { entryUpdatingIds: state.entryUpdatingIds.filter((i) => i !== id) };
      },
      false,
      'toggleEntryUpdating',
    );
  },

  // 暂时移除SWR hooks实现，避免服务端渲染问题
  // 后续会在客户端组件中直接使用SWR
});
