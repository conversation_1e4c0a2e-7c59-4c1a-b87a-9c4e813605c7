'use client';

import { Button, Input, Select, SliderWithInput } from '@lobehub/ui';
import { Popover } from 'antd';
import { createStyles } from 'antd-style';
import { Filter } from 'lucide-react';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { SelectiveLogic } from '@/types/worldbook';

const useStyles = createStyles(({ css, token }) => ({
  actions: css`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid ${token.colorBorderSecondary};
  `,
  badge: css`
    position: absolute;
    top: -4px;
    right: -4px;
    width: 8px;
    height: 8px;
    background: ${token.colorPrimary};
    border-radius: 50%;
  `,
  content: css`
    width: 320px;
    padding: 16px;
  `,
  formItem: css`
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  `,
  label: css`
    color: ${token.colorTextSecondary};
    font-size: 12px;
    margin-bottom: 4px;
    display: block;
  `,
  section: css`
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  `,
  sectionTitle: css`
    font-weight: 500;
    color: ${token.colorText};
    margin-bottom: 8px;
    font-size: 14px;
  `,
  trigger: css`
    position: relative;
  `,
}));

export interface FilterOptions {
  enabled?: boolean | null;
  groupName?: string;
  hasKeys?: boolean | null;
  hasSecondaryKeys?: boolean | null;
  orderRange?: [number, number];
  probabilityRange?: [number, number];
  selectiveLogic?: SelectiveLogic | null;
}

interface AdvancedFilterProps {
  onChange?: (filters: FilterOptions) => void;
  value?: FilterOptions;
}

const AdvancedFilter = memo<AdvancedFilterProps>(({ value = {}, onChange }) => {
  const { t } = useTranslation('worldbook');
  const { styles } = useStyles();
  const [open, setOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<FilterOptions>(value);

  const selectiveLogicOptions = [
    { label: t('filter.all'), value: null },
    { label: t('entry.selectiveLogic.andAny'), value: SelectiveLogic.AND_ANY },
    { label: t('entry.selectiveLogic.notAll'), value: SelectiveLogic.NOT_ALL },
    { label: t('entry.selectiveLogic.notAny'), value: SelectiveLogic.NOT_ANY },
    { label: t('entry.selectiveLogic.andAll'), value: SelectiveLogic.AND_ALL },
  ];

  const enabledOptions = [
    { label: t('filter.all'), value: null },
    { label: t('entry.enabled'), value: true },
    { label: t('entry.disabled'), value: false },
  ];



  const hasKeysOptions = [
    { label: t('filter.all'), value: null },
    { label: t('filter.hasKeys'), value: true },
    { label: t('filter.noKeys'), value: false },
  ];

  const updateFilter = (key: keyof FilterOptions, filterValue: any) => {
    const newFilters = { ...localFilters, [key]: filterValue };
    setLocalFilters(newFilters);
  };

  const handleApply = () => {
    onChange?.(localFilters);
    setOpen(false);
  };

  const handleReset = () => {
    const resetFilters = {};
    setLocalFilters(resetFilters);
    onChange?.(resetFilters);
    setOpen(false);
  };

  const hasActiveFilters = Object.values(value).some(v => v !== null && v !== undefined && v !== '');

  const content = (
    <div className={styles.content}>
      {/* 状态过滤 */}
      <div className={styles.section}>
        <div className={styles.sectionTitle}>{t('filter.status')}</div>
        
        <div className={styles.formItem}>
          <label className={styles.label}>{t('entry.enabled')}</label>
          <Select
            options={enabledOptions}
            value={localFilters.enabled}
            onChange={(value) => updateFilter('enabled', value)}
            style={{ width: '100%' }}
          />
        </div>


      </div>

      {/* 激活规则过滤 */}
      <div className={styles.section}>
        <div className={styles.sectionTitle}>激活规则</div>

        <div className={styles.formItem}>
          <label className={styles.label}>{t('entry.selectiveLogic.title')}</label>
          <Select
            options={selectiveLogicOptions}
            value={localFilters.selectiveLogic}
            onChange={(value) => updateFilter('selectiveLogic', value)}
            style={{ width: '100%' }}
          />
        </div>

        <div className={styles.formItem}>
          <label className={styles.label}>{t('entry.keys.title')}</label>
          <Select
            options={hasKeysOptions}
            value={localFilters.hasKeys}
            onChange={(value) => updateFilter('hasKeys', value)}
            style={{ width: '100%' }}
          />
        </div>
      </div>

      {/* 数值范围过滤 */}
      <div className={styles.section}>
        <div className={styles.sectionTitle}>{t('filter.ranges')}</div>
        
        <div className={styles.formItem}>
          <label className={styles.label}>{t('entry.order')}</label>
          <SliderWithInput
            max={1000}
            min={0}
            range
            value={localFilters.orderRange || [0, 1000]}
            onChange={(value) => updateFilter('orderRange', value)}
          />
        </div>

        <div className={styles.formItem}>
          <label className={styles.label}>{t('entry.probability')}</label>
          <SliderWithInput
            max={100}
            min={0}
            range
            value={localFilters.probabilityRange || [0, 100]}
            onChange={(value) => updateFilter('probabilityRange', value)}
          />
        </div>
      </div>

      {/* 分组过滤 */}
      <div className={styles.section}>
        <div className={styles.sectionTitle}>{t('filter.group')}</div>
        
        <div className={styles.formItem}>
          <label className={styles.label}>{t('filter.groupName')}</label>
          <Input
            placeholder={t('filter.groupNamePlaceholder')}
            value={localFilters.groupName || ''}
            onChange={(e) => updateFilter('groupName', e.target.value || null)}
          />
        </div>
      </div>

      {/* 操作按钮 */}
      <div className={styles.actions}>
        <Button onClick={handleReset} size="small">
          {t('filter.reset')}
        </Button>
        <Button onClick={handleApply} size="small" type="primary">
          {t('filter.apply')}
        </Button>
      </div>
    </div>
  );

  return (
    <Popover
      content={content}
      open={open}
      onOpenChange={setOpen}
      placement="bottomRight"
      trigger="click"
    >
      <Button
        className={styles.trigger}
        icon={<Filter size={14} />}
        size="small"
      >
        {hasActiveFilters && <div className={styles.badge} />}
        {t('filter.title')}
      </Button>
    </Popover>
  );
});

AdvancedFilter.displayName = 'AdvancedFilter';

export default AdvancedFilter;
