import { Icon, Modal } from '@lobehub/ui';
import { BookText } from 'lucide-react';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { WorldbookEntry } from '@/types/worldbook';

import EntryForm from './EntryForm';

interface EntryModalProps {
  entry?: WorldbookEntry;
  onSuccess?: () => void;
  worldbookId: string;
  open: boolean;
  onClose: () => void;
}

const Title = ({ isEdit }: { isEdit: boolean }) => {
  const { t } = useTranslation('worldbook');
  return (
    <Flexbox gap={8} horizontal>
      <Icon icon={BookText} />
      {isEdit ? t('edit', { ns: 'common' }) : t('entry.create.title')}
    </Flexbox>
  );
};

const EntryModal = memo<EntryModalProps>(({ worldbookId, entry, onSuccess, open, onClose }) => {
  const isEdit = !!entry;

  console.log('EntryModal render - open:', open, 'entry:', entry?.id);

  return (
    <Modal
      centered
      footer={null}
      onCancel={onClose}
      open={open}
      styles={{
        body: { padding: 0 },
        content: {
          maxHeight: '80vh',
          overflow: 'hidden',
        },
      }}
      title={<Title isEdit={isEdit} />}
      width={800}
    >
      <EntryForm
        entry={entry}
        onClose={onClose}
        onSuccess={() => {
          onSuccess?.();
          onClose();
        }}
        worldbookId={worldbookId}
      />
    </Modal>
  );
});

export const useEntryModal = () => {
  const [modalState, setModalState] = useState<{
    open: boolean;
    props?: Omit<EntryModalProps, 'open' | 'onClose'>;
  }>({ open: false });

  const open = (props: Omit<EntryModalProps, 'open' | 'onClose'>) => {
    setModalState({ open: true, props });
  };

  const close = () => {
    setModalState({ open: false });
  };

  return {
    open,
    modal: modalState.open && modalState.props ? (
      <EntryModal
        {...modalState.props}
        open={modalState.open}
        onClose={close}
      />
    ) : null,
  };
};

export default EntryModal;
