{"index.html": "______________________________________________________________________________________________________________________________________________", "Memory Enhancement (Tables)": "Memory Enhancement (Tables)", "Click to update": "Click to update", "Project link": "Project link", "Read tutorial": "Read tutorial", "Logs": "Logs", "Plugin settings": "Plugin settings", "Import, export and reset plugin": "Import, export and reset plugin", "[title]Import table presets": "Import table presets", "[title]Export table presets": "Export table presets", "[title]Reset table presets": "Reset table presets", "[title]Revert to previous version": "Revert to previous version", "Plugin switches": "Plugin switches", "Enable plugin": "Enable plugin", "Debug mode": "Debug mode", "Debug mode description": "(Enabling will cause some performance loss, only for code development debugging)", "Custom independent API": "Custom independent API", "Custom API temperature setting": "Custom API temperature setting", "Run strategy": "Run strategy", "AI read table switch": "AI read table switch", "AI edit table switch": "AI edit table switch", "Enable step-by-step table filling": "Enable step-by-step table filling", "Step-by-step table filling description": "(Automatically calls API to summarize tables after each conversation)", "Injection": "Injection", "Turn off injection": "Turn off injection", "Depth": "De<PERSON><PERSON>", "Message template": "Message template", "Message template description": "(Used to explain to AI how to use and modify tables, use <code>{{tableData}}</code> macro to select where to insert table data)", "Use main API": "Use main API", "Trigger threshold characters": "Trigger threshold characters", "Trigger threshold description": "(Step-by-step table filling is triggered when reply exceeds this value)", "Accumulate character count": "Accumulate character count", "Accumulate character count description": "(Accumulate character count across multiple rounds for threshold comparison)", "Confirm before execution": "Confirm before execution", "Confirm before execution description": "(A confirmation dialog will appear at checkpoints during step-by-step process)", "Reorganize tables": "Reorganize tables", "Custom table organization": "Custom table organization", "Reference recent chat history": "Reference recent chat history", "Token limit": "Token limit", "Use token limit instead of chat history limit": "Use token limit instead of chat history limit", "Only reference AI replies": "Only reference AI replies", "When reorganizing, don't delete, only modify and add": "When reorganizing, don't delete, only modify and add", "When reorganizing, don't delete description": "(Only applicable to reorganization types marked as <code>**old**</code>)", "Skip confirmation dialog after reorganizing": "Skip confirmation dialog after reorganizing", "Reorganize tables now": "Reorganize tables now", "Frontend table (status bar)": "Frontend table (status bar)", "Access tables from extensions menu": "Access tables from extensions menu", "Render table view in conversation": "Render table view in conversation", "Render table view in conversation description": "(When enabled, custom tables will be displayed in the conversation)", "Data editable": "Data editable", "Data editable description": "(Edit data in tables rendered in conversation)", "Render to": "Render to", "Bottom of context": "Bottom of context", "Inside last message": "Inside last message", "Use custom display location": "Use <code>{{sheetsView}}</code> custom display location", "Edit style of tables rendered in conversation": "Edit style of tables rendered in conversation", "Advanced rendering settings": "Advanced rendering settings", "Disable dynamic effects in table plugin": "Disable dynamic effects in table plugin", "Disable dynamic effects description": "(If you experience lag when the plugin is enabled, try enabling this)", "Disable GPU table rendering": "Disable GPU table rendering", "Disable GPU table rendering description": "(Manually disable GPU rendering for tables)", "appHeaderTableDrawer.html": "______________________________________________________________________________________________________________________________________________", "[title]Event table": "Event table", "Data": "Data", "Template": "Template", "Settings": "Settings", "manager.html": "______________________________________________________________________________________________________________________________________________", "managerDescription": "This is a table for storing data. The model can update the table based on the provided prompts and use the data in the table as a reference for generating the next conversation.", "[title]View table data statistics": "View table data statistics", "[title]View table edit history": "View table edit history", "[title]Completely rebuild table": "Completely rebuild table", "[title]Copy table": "Copy table", "[title]Import table": "Import table", "[title]Export table": "Export table", "[title]Clear table": "Clear table", "editor.html": "______________________________________________________________________________________________________________________________________________", "editorDescription": "This is the data table editor interface. You can use the following tools to edit and manage table data.", "Scope:": "Scope:", "ScopeGlobal": "Global", "[title]New table template": "New table template", "customSheetStyle.html": "______________________________________________________________________________________________________________________________________________", "customTableStyleTitle": "Custom Table Style", "customTableStyleDesc": "Customize the display style of the table. When the style content is empty, the original table is displayed by default.<br>Supports defining structure and style using HTML and CSS, and uses <code>\\$\\w\\s+</code> to locate cells.<br>For example, <code>$A0</code> represents the 1st column, 1st row (header), and <code>$A1</code> represents the 1st column, 2nd row (first row of table content).", "enableCustomStyleLabel": "Enable custom style for this table", "pushToChatLabel": "Push to conversation", "pushToChatDesc": "(When enabled, the style in the preview below for this table will be pushed to the conversation)", "replacementStyleLabel": "Replacement Style:", "stylePreviewLabel": "Style Preview:", "customPushSheetsToChatStyle.html (includes reused keys from customSheetStyle.html)": "______________________________________________________________________________________________________________________________________________", "presetLabel": "Preset", "presetDefault": "<PERSON><PERSON><PERSON>", "insertLastMessageLabel": "Insert into the last message", "insertLastMessageDesc": "(Insert at the bottom of the last message block instead of the bottom of the context)", "regexReplaceLabel": "Use regex to replace content in the conversation:", "___defaultSettings___": "______________________________________________________________________________________________________________________________________________", "__defaultSettings__": {"isExtensionAble": true, "tableDebugModeAble": false, "isAiReadTable": true, "isAiWriteTable": true, "injection_mode": "deep_system", "deep": 2, "message_template": "# dataTable Description\n  ## Purpose\n  - dataTable is a CSV format table that stores data and status, serving as an important reference for generating subsequent text.\n  - Newly generated subsequent text should be based on the dataTable and allow for table updates.\n  ## Data and Format\n  - You can view all table data, related descriptions, and trigger conditions for modifying tables here.\n  - Naming Format:\n      - Table Name: [tableIndex:TableName] (Example: [2:Character Feature Table])\n      - Column Name: [colIndex:ColumnName] (Example: [2:Example Column])\n      - Row Name: [rowIndex]\n\n  {{tableData}}\n\n  # Methods for Adding, Deleting, and Modifying dataTable:\n  - After generating the main text, you need to review each table based on the [Add/Delete/Modify Trigger Conditions] to determine if modifications are needed. If modifications are required, use JavaScript function call syntax within the <tableEdit> tag, following the OperateRule below.\n\n  ## Operation Rules (Must be strictly followed)\n  <OperateRule>\n  - When inserting a new row into a table, use the insertRow function:\n  insertRow(tableIndex:number, data:{[colIndex:number]:string|number})\n  Example: insertRow(0, {0: \"2021-09-01\", 1: \"12:00\", 2: \"Balcony\", 3: \"Xiao Hua\"})\n  - When deleting a row from a table, use the deleteRow function:\n  deleteRow(tableIndex:number, rowIndex:number)\n  Example: deleteRow(0, 0)\n  - When updating a row in a table, use the updateRow function:\n  updateRow(tableIndex:number, rowIndex:number, data:{[colIndex:number]:string|number})\n  Example: updateRow(0, 0, {3: \"Megumin\"})\n  </OperateRule>\n\n  # Important Operational Principles (Must be followed)\n  - When <user> requests table modifications, <user>'s request has the highest priority.\n  - Each response must perform add, delete, or modify operations at the correct position based on the plot. Fabricating information and filling in unknowns is prohibited.\n  - When using the insertRow function to insert a row, please provide corresponding data for all known columns. Also, check if the data:{[colIndex:number]:string|number} parameter includes all colIndexes.\n  - Commas are prohibited in cells; use / for semantic separation.\n  - Double quotes are prohibited within strings.\n  - Attitudes towards <user> are prohibited in the Social Table (tableIndex: 2). Counterexample (Prohibited): insertRow(2, {\"0\":\"<user>\",\"1\":\"Unknown\",\"2\":\"None\",\"3\":\"Low\"})\n  - Comments within the <tableEdit> tag must use <!-- --> markers.\n\n  # Output Example:\n  <tableEdit>\n  <!--\n  insertRow(0, {\"0\":\"October\",\"1\":\"Winter/Snowing\",\"2\":\"School\",\"3\":\"<user>/Yoyo\"})\n  deleteRow(1, 2)\n  insertRow(1, {0:\"Yoyo\", 1:\"Weight 60kg/Black long hair\", 2:\"Cheerful and lively\", 3:\"Student\", 4:\"Badminton\", 5:\"Demon Slayer\", 6:\"Dormitory\", 7:\"Sports Club Captain\"})\n  insertRow(1, {0:\"<user>\", 1:\"Uniform/Short hair\", 2:\"Melancholic\", 3:\"Student\", 4:\"Singing\", 5:\"Jujutsu Kaisen\", 6:\"Own home\", 7:\"Student Council President\"})\n  insertRow(2, {0:\"Yoyo\", 1:\"Classmate\", 2:\"Dependent/Likes\", 3:\"High\"})\n  updateRow(4, 1, {0: \"Xiao Hua\", 1: \"Failed confession sabotage\", 2: \"October\", 3: \"School\",4:\"Angry\"})\n  insertRow(4, {0: \"<user>/Yoyo\", 1: \"Yoyo confesses to <user>\", 2: \"2021-10-05\", 3: \"Classroom\",4:\"Moved\"})\n  insertRow(5, {\"0\":\"<user>\",\"1\":\"Club competition prize\",\"2\":\"Trophy\",\"3\":\"First place in competition\"})\n  -->\n  </tableEdit>\n  ", "isTableToChat": false, "show_settings_in_extension_menu": true, "show_drawer_in_extension_list": true, "table_to_chat_can_edit": false, "table_to_chat_mode": "context_bottom", "table_cell_width_mode": "wide1_2_cell", "to_chat_container": "<div class=\"table-preview-bar\"><details>\n    <summary style=\"display: flex; justify-content: space-between\"> <span>Memory Enhanced Table</span> </summary>\n    $0\n    </details></div>\n\n    <style>\n    .table-preview-bar {\n        padding: 0 8px;\n        border-radius: 10px;\n        color: #888;\n        font-size: 0.8rem;\n    }\n    .f5-reload-window {\n        bottom: 10px;\n        left: 10px;\n        padding: 0 10px;\n        border-radius: 5px;\n        background: none;\n        border: 1px solid var(--SmartThemeBorderColor);\n        color: var(--SmartThemeBodyColor);\n        z-index: 999;\n        cursor: pointer;\n    }\n    </style>", "confirm_before_execution": true, "use_main_api": true, "custom_temperature": 1.0, "custom_max_tokens": 2048, "custom_top_p": 1, "bool_ignore_del": true, "ignore_user_sent": false, "clear_up_stairs": 9, "use_token_limit": true, "rebuild_token_limit_value": 10000, "refresh_system_message_template": "You are a professional table organization assistant. Please strictly follow the user's instructions and format requirements to process table data.", "refresh_user_message_template": "Organize the table according to the following rules:\n<Organization Rules>\n    1. Correct format errors, delete all rows where data[0] is empty. This operation only allows whole row operations!\n    2. Complete blank/unknown content, but fabricating information is prohibited.\n    3. When the \"Important Event History Table\" (tableIndex: 4) exceeds 10 rows, check for duplicate or similar content rows, merge or delete redundant rows appropriately. This operation only allows whole row operations!\n    4. Duplicate character names are prohibited in the \"Character & User Social Table\" (tableIndex: 2). If duplicates exist, the entire row needs to be deleted. This operation only allows whole row operations!\n    5. The \"Spacetime Table\" (tableIndex: 0) must only contain one row. Delete all old content. This operation only allows whole row operations!\n    6. If a cell contains more than 15 characters, simplify it to not exceed 15 characters; if a cell contains more than 4 items separated by slashes, simplify to retain no more than 4 items.\n    7. Unify the time format to YYYY-MM-DD HH:MM (Unknown parts can be omitted, e.g., 2023-10-01 12:00 or 2023-10-01 or 12:00).\n    8. Location format is Continent>Country>City>Specific Location (Unknown parts can be omitted, e.g., Continent>China>Beijing>Forbidden City or Otherworld>Tavern).\n    9. Commas are prohibited in cells; use / for semantic separation.\n    10. Double quotes are prohibited within cell strings.\n    11. Inserting rows identical to existing table content is prohibited. Check existing table data before deciding whether to insert.\n</Organization Rules>\n\n<Chat History>\n    $1\n</Chat History>\n\n<Current Table>\n    $0\n</Current Table>\n\nPlease reply with the list of operations in pure JSON format, ensuring that:\n    1. All key names must be enclosed in double quotes, e.g., \"action\" not action.\n    2. Numeric key names (used as strings) must be enclosed in double quotes, e.g., \"0\" not 0.\n    3. Use double quotes, not single quotes, e.g., \"value\" not 'value'.\n    4. Slashes (/) must be escaped as \\/.\n    5. Do not include comments or extraneous Markdown markup.\n    6. Place all delete operations at the end, and when deleting, send operations with larger row values first.\n    7. Valid format:\n        [{\n            \"action\": \"insert/update/delete\",\n            \"tableIndex\": number,\n            \"rowIndex\": number (required for delete/update),\n            \"data\": {columnIndex: \"value\"} (required for insert/update)\n        }]\n    8. Emphasis: delete operation does not include \"data\", insert operation does not include \"rowIndex\".\n    9. Emphasis: The values for tableIndex and rowIndex are numbers, without double quotes, e.g., 0 not \"0\".\n\n<Correct Response Example>\n    [\n        {\n            \"action\": \"update\",\n            \"tableIndex\": 0,\n            \"rowIndex\": 0,\n            \"data\": {\n            \"0\": \"2023-10-01\",\n            \"1\": \"12:00\",\n            \"2\": \"Continent>China>Beijing>Forbidden City\"\n            }\n        },\n        {\n            \"action\": \"insert\",\n            \"tableIndex\": 0,\n            \"data\": {\n            \"0\": \"2023-10-01\",\n            \"1\": \"12:00\",\n            \"2\": \"Continent>China>Beijing>Forbidden City\"\n            }\n        },\n        {\n            \"action\": \"delete\",\n            \"tableIndex\": 0,\n            \"rowIndex\": 0\n        }\n    ]\n</Correct Response Example>", "step_by_step": false, "step_by_step_use_main_api": true, "bool_silent_refresh": false, "tableStructure": [{"tableName": "Spacetime Table", "tableIndex": 0, "columns": ["Date", "Time", "Location (Current Description)", "Characters Here"], "enable": true, "Required": true, "asStatus": true, "toChat": true, "note": "Table for recording spacetime information, should be kept to one row", "initNode": "This round needs to record current time, location, character information using the insertRow function", "updateNode": "When the described scene, time, or characters change", "deleteNode": "If this table has more than one row, excess rows should be deleted"}, {"tableName": "Character Feature Table", "tableIndex": 1, "columns": ["Character Name", "Physical Features", "Personality", "Occupation", "Hobbies", "Liked Things (Works, Characters, Items, etc.)", "Residence", "Other Important Info"], "enable": true, "Required": true, "asStatus": true, "toChat": true, "note": "CSV table for innate or hard-to-change character traits. Consider if any characters from this table are present this round and how they should react.", "initNode": "This round must find all known characters from the context and insert them using insertRow. Character name cannot be empty.", "insertNode": "When a new character not present in the table appears this round, they should be inserted.", "updateNode": "When a character's body undergoes a persistent change, e.g., scars / When a character develops new hobbies, occupations, liked things / When a character changes residence / When a character mentions important information.", "deleteNode": ""}, {"tableName": "Character & <user> Social Table", "tableIndex": 2, "columns": ["Character Name", "Relationship with <user>", "Attitude towards <user>", "Affinity towards <user>"], "enable": true, "Required": true, "asStatus": true, "toChat": true, "note": "Consider the attitude if a character interacts with <user>.", "initNode": "This round must find all known characters from the context and insert them using insertRow. Character name cannot be empty.", "insertNode": "When a new character not present in the table appears this round, they should be inserted.", "updateNode": "When a character's interaction with <user> no longer matches the existing record / When the relationship between a character and <user> changes.", "deleteNode": ""}, {"tableName": "Tasks, Orders, or Appointments Table", "tableIndex": 3, "columns": ["Character", "Task", "Location", "Duration"], "enable": true, "Required": false, "asStatus": true, "toChat": true, "note": "Consider if a task should be performed or an appointment kept this round.", "insertNode": "When an appointment is made to do something together at a specific time / When a character receives an order or task to do something.", "updateNode": "", "deleteNode": "When everyone meets the appointment / When the task or order is completed / When the task, order, or appointment is cancelled."}, {"tableName": "Important Event History Table", "tableIndex": 4, "columns": ["Character", "Event Summary", "Date", "Location", "Emotion"], "enable": true, "Required": true, "asStatus": true, "toChat": true, "note": "Records important events experienced by <user> or characters.", "initNode": "This round must find insertable events from the context and insert them using insertRow.", "insertNode": "When a character experiences a memorable event, such as a confession, breakup, etc.", "updateNode": "", "deleteNode": ""}, {"tableName": "Important Items Table", "tableIndex": 5, "columns": ["Owner", "Item Description", "Item Name", "Reason for Importance"], "enable": true, "Required": false, "asStatus": true, "toChat": true, "note": "Items that are very valuable to someone or have special commemorative significance.", "insertNode": "When someone acquires a valuable or specially significant item / When an existing item gains special significance.", "updateNode": "", "deleteNode": ""}]}, "___profile_prompts___": "______________________________________________________________________________________________________________________________________________", "__profile_prompts__": {}}