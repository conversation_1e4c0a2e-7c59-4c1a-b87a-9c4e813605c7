import type { MessageRoleType } from '@/types/message';
import type {
  ActivationMode,
  SelectiveLogic,
  WorldbookPosition
} from './enums';

/**
 * 匹配源配置接口
 * 定义世界书条目可以匹配的内容源
 */
export interface MatchSources {
  /** 匹配人格描述 */
  personaDescription: boolean;
  /** 匹配角色描述 */
  characterDescription: boolean;
  /** 匹配角色性格 */
  characterPersonality: boolean;
  /** 匹配角色深度提示 */
  characterDepthPrompt: boolean;
  /** 匹配场景 */
  scenario: boolean;
  /** 匹配创作者备注 */
  creatorNotes: boolean;
}

/**
 * 世界书基础信息接口
 * 整合现有功能和新功能
 */
export interface Worldbook {
  id: string;
  name: string;
  description?: string;
  enabled: boolean;
  entryCount?: number;
  lastActivated?: string;
  userId: string;
  clientId?: string;
  createdAt: string;
  updatedAt: string;

  /** 关联的主要Agent信息 */
  primaryAgent?: {
    id: string;
    title: string;
  };
}

/**
 * 世界书条目接口
 * 整合现有字段和新增字段，保持向后兼容
 */
export interface WorldbookEntry {
  // 基础标识
  id: string;
  title: string;
  content: string;                   // 从chunks表获取的内容
  enabled: boolean;
  worldbookId: string;
  chunkId: string;                   // 必需，关联chunks表

  // 激活规则
  keys: string[];
  keysSecondary: string[];
  selectiveLogic: SelectiveLogic;

  // 激活模式
  activationMode: ActivationMode;

  // 位置控制
  position: WorldbookPosition;
  depth?: number;                    // AT_DEPTH时使用
  role?: MessageRoleType;            // AT_DEPTH时使用
  order: number;

  // 匹配配置
  matchSources: MatchSources;
  caseSensitive?: boolean;
  matchWholeWords?: boolean;
  useRegex: boolean;

  // 概率控制
  probability: number;
  useProbability: boolean;

  // 时间效果
  sticky?: number;
  cooldown?: number;
  delay?: number;

  // 递归控制
  excludeRecursion: boolean;
  preventRecursion: boolean;
  delayUntilRecursion: number;

  // 分组功能
  groupName?: string;
  groupWeight: number;
  groupOverride: boolean;
  useGroupScoring: boolean;

  // 扫描控制
  scanDepth?: number;

  // 显示控制
  displayIndex: number;
  addMemo: boolean;

  // 装饰器支持
  decorators: string[];

  // 角色过滤 (可选)
  characterFilterNames?: string[];
  characterFilterTags?: string[];
  characterFilterExclude?: boolean;

  // 系统字段
  userId: string;
  clientId?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * 创建世界书数据接口
 */
export interface CreateWorldbookData {
  name: string;
  description?: string;
}

/**
 * 更新世界书数据接口
 */
export interface UpdateWorldbookData {
  name?: string;
  description?: string;
  enabled?: boolean;
}

/**
 * 创建条目数据接口
 * 整合现有字段和新增字段
 */
export interface CreateEntryData {
  title: string;
  content: string;                   // 必需字段，将存储到chunks表
  keys: string[];
  keysSecondary?: string[];
  selectiveLogic?: SelectiveLogic;

  // 激活模式
  activationMode?: ActivationMode;

  position?: WorldbookPosition;
  depth?: number;
  role?: MessageRoleType;
  order?: number;

  // 匹配配置
  matchSources?: Partial<MatchSources>;
  caseSensitive?: boolean;
  matchWholeWords?: boolean;
  useRegex?: boolean;

  // 概率控制
  probability?: number;
  useProbability?: boolean;

  // 时间效果
  sticky?: number;
  cooldown?: number;
  delay?: number;

  // 递归控制
  excludeRecursion?: boolean;
  preventRecursion?: boolean;
  delayUntilRecursion?: number;

  // 分组功能
  groupName?: string;
  groupWeight?: number;
  groupOverride?: boolean;
  useGroupScoring?: boolean;

  // 扫描控制
  scanDepth?: number;

  // 显示控制
  displayIndex?: number;
  addMemo?: boolean;

  // 装饰器支持
  decorators?: string[];

  // 角色过滤 (可选)
  characterFilterNames?: string[];
  characterFilterTags?: string[];
  characterFilterExclude?: boolean;
}

/**
 * 更新条目数据接口
 */
export interface UpdateEntryData extends Partial<CreateEntryData> {
  enabled?: boolean;
}

/**
 * 搜索参数接口
 * 整合现有功能和新增功能
 */
export interface SearchParams {
  // 基础分页
  page?: number;
  pageSize?: number;

  // 搜索条件
  query?: string;

  // 过滤条件
  enabled?: boolean;
  activationMode?: ActivationMode;
  position?: WorldbookPosition;
  groupName?: string;
  hasKeys?: boolean;
  hasSecondaryKeys?: boolean;
  selectiveLogic?: SelectiveLogic;

  // 范围过滤
  orderMin?: number;
  orderMax?: number;
  probabilityMin?: number;
  probabilityMax?: number;

  // 排序
  sortBy?: 'order' | 'title' | 'createdAt' | 'updatedAt' | 'probability';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

/**
 * 导入结果详情接口
 */
export interface ImportResultDetail {
  worldbookId: string;
  worldbookName: string;
  totalEntries: number;
  successfulEntries: number;
  failedEntries: number;
  errors: Array<{
    entryIndex: number;
    entryTitle?: string;
    error: string;
  }>;
}

/**
 * 激活状态接口
 */
export interface ActivationStatus {
  activeEntries: string[];
  lastActivated: string;
  worldbookId: string;
}

/**
 * 批量操作结果接口
 */
export interface BulkOperationResult {
  successCount: number;
  failedCount: number;
  errors: Array<{
    id: string;
    error: string;
  }>;
}

// 重新导出枚举类型
export {
  ActivationMode,
  SelectiveLogic,
  WorldbookPosition
} from './enums';

// MessageRoleType是类型别名，使用type导出
export type { MessageRoleType } from './enums';