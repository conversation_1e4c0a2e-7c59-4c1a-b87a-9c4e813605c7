<!-- sheetTemplateEditor.html (This is a conceptual editor interface based on your request) -->
<div class="wide100p padding5 dataBankAttachments" id="table_editor_container">
    <!--    <h2 class="marginBot5"><span>数据表编辑器</span></h2>-->
    <div id="tableEditTips">
        <small data-i18n="editorDescription">
            这是数据表格编辑器界面。您可以使用以下工具编辑和管理表格数据。
        </small>
        <hr />
        <div class="flex-container alignitemscenter gap3px" style="display: flex; justify-content: flex-start">
            <label for="structure_setting_scope" data-i18n="Scope:">域：</label>
            <select id="structure_setting_scope" style="height: 30px; max-width: 120px; margin: 0">
                <!--                <option value="global">不过滤 All</option>-->
                <option value="chat" data-i18n="ScopeGlobal">聊天 Chat</option>
                <option value="global" data-i18n="ScopeGlobal">全局 Global</option>
            </select>
            <div style="display: flex; justify-content: space-between; flex-wrap: wrap; flex-grow: 1; ">
                <div style=" display: flex; justify-content: flex-start; flex-wrap: wrap; width: fit-content; gap: 4px;">
                    <div style="display: flex; justify-content: center;" id="add_table_template_button">
                        <i class="menu_button menu_button_icon fa-solid fa-add" style="height: 30px; width: 30px" title="新建表格模板" data-i18n="[title]New table template"></i>
                    </div>
                    <!--            <div style="display: flex; justify-content: center;" id="switch_table_display_mode_button">-->
                    <!--                <i class="menu_button menu_button_icon fa-solid fa-swatchbook" style="height: 30px; width: 30px" title="切换显示模式"></i>-->
                    <!--            </div>-->
                    <!--                    <div style="display: flex; justify-content: center;" id="clear_table_template_button">-->
                    <!--                        <i class="menu_button menu_button_icon fa-solid fa-broom" style="height: 30px; width: 30px" title="清空当前表格（不会删除）"></i>-->
                    <!--                    </div>-->
                    <!--            <div style="display: flex; justify-content: center;" id="destroy_table_template_button">-->
                    <!--                <i class="menu_button menu_button_icon fa-solid fa-trash-can" style="height: 30px; width: 30px" title="删除所有表格"></i>-->
                    <!--            </div>-->

                </div>
                <div></div>
                <div style=" display: flex; justify-content: flex-end; flex-wrap: wrap; width: fit-content; gap: 4px;">
                    <!--            <div style="display: flex; justify-content: center;" id="table_template_history_button">-->
                    <!--                <i class="menu_button menu_button_icon fa-solid fa-history" style="height: 30px; width: 30px" title="查看表格编辑历史记录"></i>-->
                    <!--            </div>-->
                    <!-- <div style="display: flex; justify-content: center;" id="import_table_template_button">
                        <i class="menu_button menu_button_icon fa-solid fa-file-import" style="height: 30px; width: 30px" title="导入表格模板"></i>
                    </div>
                    <div style="display: flex; justify-content: center;" id="export_table_template_button">
                        <i class="menu_button menu_button_icon fa-solid fa-file-export" style="height: 30px; width: 30px" title="导出表格模板"></i>
                    </div>
                    <div style="display: flex; justify-content: center;" id="table_edit_full_screen_button">
                        <i class="menu_button menu_button_icon fa-solid fa-up-right-and-down-left-from-center" style="height: 30px; width: 30px" title="全屏编辑"></i>
                    </div> -->
                </div>
            </div>
        </div>
        <hr />
    </div>

    <hr />
    <div id="contentContainer" style="outline: 3px solid #41b681; border-radius: 3px; height: 100%">
        <div id="tableContainer" style="">
        </div>
    </div>
</div>
