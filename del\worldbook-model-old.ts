import { and, desc, eq } from 'drizzle-orm/expressions';
import { count } from 'drizzle-orm';

import { LobeChatDatabase, Transaction } from '@/database/type';

import { NewWorldbook, WorldbookItem, worldbookChunks, worldbooks } from '../schemas';
import { agents, agentsWorldbooks } from '../schemas/agent';
import { WorldbookChunkModel, CreateChunkData } from './worldbookChunk';

export class WorldbookModel {
  private userId: string;
  private db: LobeChatDatabase;

  constructor(db: LobeChatDatabase, userId: string) {
    this.userId = userId;
    this.db = db;
  }

  // create
  create = async (params: Omit<NewWorldbook, 'userId'>) => {
    const [result] = await this.db
      .insert(worldbooks)
      .values({ ...params, userId: this.userId })
      .returning();

    return result;
  };

  // delete - 删除世界书及其所有相关数据
  delete = async (id: string, trx?: Transaction) => {
    const executeDelete = async (tx: Transaction) => {
      // 1. 先删除所有相关的chunks（这会级联删除embeddings和worldbook_chunks）
      const worldbookChunkModel = new WorldbookChunkModel(this.db, this.userId);
      await worldbookChunkModel.deleteByWorldbookId(id, tx);

      // 2. 删除世界书记录
      return tx
        .delete(worldbooks)
        .where(and(eq(worldbooks.id, id), eq(worldbooks.userId, this.userId)));
    };

    // 如果已经在事务中，直接执行；否则创建新事务
    return trx ? executeDelete(trx) : this.db.transaction(executeDelete);
  };

  deleteAll = async (trx?: Transaction) => {
    const executeDeleteAll = async (tx: Transaction) => {
      // 1. 获取所有世界书ID
      const worldbookList = await this.findByUserId(this.userId);

      // 2. 删除每个世界书的chunks
      const worldbookChunkModel = new WorldbookChunkModel(this.db, this.userId);
      for (const worldbook of worldbookList) {
        await worldbookChunkModel.deleteByWorldbookId(worldbook.id, tx);
      }

      // 3. 删除所有世界书记录
      return tx.delete(worldbooks).where(eq(worldbooks.userId, this.userId));
    };

    // 如果已经在事务中，直接执行；否则创建新事务
    return trx ? executeDeleteAll(trx) : this.db.transaction(executeDeleteAll);
  };

  // 导入世界书（创建世界书和条目）
  importWorldbook = async (data: {
    entries: CreateChunkData[];
    worldbook: { description?: string; name: string };
  }) => {
    return this.db.transaction(async (_trx) => {
      // 1. 创建世界书
      console.log('📚 [WorldbookModel] Creating worldbook...');
      const worldbook = await this.create({
        description: data.worldbook.description || '',
        enabled: true,
        name: data.worldbook.name,
      });

      console.log('✅ [WorldbookModel] Worldbook created:', worldbook);

      if (!worldbook) {
        throw new Error('Failed to create worldbook');
      }

      // 2. 批量创建条目
      console.log('📝 [WorldbookModel] Creating entries...');
      const entriesWithWorldbookId = data.entries.map(entry => ({
        ...entry,
        worldbookId: worldbook.id,
      }));

      const worldbookChunkModel = new WorldbookChunkModel(this.db, this.userId);
      const createdEntries = await worldbookChunkModel.bulkCreate(entriesWithWorldbookId);

      console.log('✅ [WorldbookModel] Entries created:', createdEntries.length);

      return {
        errors: [], // 如果有错误，可以在这里记录
        failedEntries: data.entries.length - createdEntries.length,
        successfulEntries: createdEntries.length,
        totalEntries: data.entries.length,
        worldbookId: worldbook.id,
        worldbookName: worldbook.name,
      };
    });
  };

  // query
  query = async () => {
    const data = await this.db
      .select({
        id: worldbooks.id,
        name: worldbooks.name,
        description: worldbooks.description,
        enabled: worldbooks.enabled,
        userId: worldbooks.userId,
        createdAt: worldbooks.createdAt,
        updatedAt: worldbooks.updatedAt,
        // 专属agent信息
        primaryAgentId: agentsWorldbooks.agentId,
        primaryAgentTitle: agents.title,
      })
      .from(worldbooks)
      .leftJoin(
        agentsWorldbooks,
        and(
          eq(agentsWorldbooks.worldbookId, worldbooks.id),
          eq(agentsWorldbooks.isPrimary, true),
          eq(agentsWorldbooks.userId, this.userId)
        )
      )
      .leftJoin(agents, eq(agents.id, agentsWorldbooks.agentId))
      .where(eq(worldbooks.userId, this.userId))
      .orderBy(desc(worldbooks.updatedAt));

    // 转换为前端期望的格式
    return data.map(item => ({
      id: item.id,
      name: item.name,
      description: item.description || undefined,
      enabled: item.enabled || false,
      userId: item.userId,
      createdAt: item.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: item.updatedAt?.toISOString() || new Date().toISOString(),
      entryCount: 0, // TODO: 可以后续添加条目计数查询
      lastActivated: undefined, // TODO: 可以后续添加激活时间追踪
      // 专属agent信息
      primaryAgent: item.primaryAgentId ? {
        id: item.primaryAgentId,
        title: item.primaryAgentTitle || '未命名Agent'
      } : undefined,
    }));
  };

  findById = async (id: string) => {
    const data = await this.db.query.worldbooks.findFirst({
      where: and(eq(worldbooks.id, id), eq(worldbooks.userId, this.userId)),
    });

    if (!data) return undefined;

    // 转换为前端期望的格式
    return {
      id: data.id,
      name: data.name,
      description: data.description || undefined,
      enabled: data.enabled || false,
      userId: data.userId,
      createdAt: data.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: data.updatedAt?.toISOString() || new Date().toISOString(),
      entryCount: 0, // TODO: 可以后续添加条目计数查询
      lastActivated: undefined, // TODO: 可以后续添加激活时间追踪
    };
  };

  findByUserId = async (userId: string) => {
    return this.db.query.worldbooks.findMany({
      where: eq(worldbooks.userId, userId),
      orderBy: desc(worldbooks.updatedAt),
    });
  };

  // update
  update = async (id: string, value: Partial<WorldbookItem>) =>
    this.db
      .update(worldbooks)
      .set({ ...value, updatedAt: new Date() })
      .where(and(eq(worldbooks.id, id), eq(worldbooks.userId, this.userId)));

  // static methods for cross-user access (admin functions)
  static findById = async (db: LobeChatDatabase, id: string) =>
    db.query.worldbooks.findFirst({
      where: eq(worldbooks.id, id),
    });
}
