import { and, asc, count, desc, eq, gte, ilike, inArray, lte, or, sql } from 'drizzle-orm';

import { LobeChatDatabase, Transaction } from '@/database/type';
import type {
  CreateEntryData,
  PaginatedResponse,
  SearchParams,
  UpdateEntryData,
  WorldbookEntry,
} from '@/types/worldbook';

import { NewChunkItem, WorldbookChunkItem, chunks, embeddings, worldbookChunks } from '../schemas';

export class WorldbookChunkModel {
  private userId: string;
  private db: LobeChatDatabase;

  constructor(db: LobeChatDatabase, userId: string) {
    this.userId = userId;
    this.db = db;
  }

  // ====== CRUD Operations ======

  /**
   * 创建世界书条目（包含内容）
   */
  create = async (data: CreateEntryData & { worldbookId: string }): Promise<WorldbookEntry> => {
    return this.db.transaction(async (trx) => {
      // 1. 创建chunk记录存储内容
      const [chunkResult] = await trx
        .insert(chunks)
        .values({
          text: data.content,
          type: 'worldbook',
          userId: this.userId,
          metadata: {
            worldbookId: data.worldbookId,
            title: data.title,
          },
        } as NewChunkItem)
        .returning();

      // 2. 创建worldbook_chunks记录
      const [worldbookChunkResult] = await trx
        .insert(worldbookChunks)
        .values({
          worldbookId: data.worldbookId,
          chunkId: chunkResult.id,
          title: data.title,
          keys: data.keys,
          keysSecondary: data.keysSecondary || [],
          selectiveLogic: data.selectiveLogic || 'and_any',
          activationMode: data.activationMode || 'keyword',
          position: data.position || 'after',
          depth: data.depth || 4,
          role: data.role || 'system',
          order: data.order || 100,
          matchSources: data.matchSources || {
            personaDescription: false,
            characterDescription: false,
            characterPersonality: false,
            characterDepthPrompt: false,
            scenario: false,
            creatorNotes: false,
          },
          caseSensitive: data.caseSensitive || false,
          matchWholeWords: data.matchWholeWords || false,
          useRegex: data.useRegex || false,
          probability: data.probability || 100,
          useProbability: data.useProbability || true,
          sticky: data.sticky,
          cooldown: data.cooldown,
          delay: data.delay,
          excludeRecursion: data.excludeRecursion || false,
          preventRecursion: data.preventRecursion || false,
          delayUntilRecursion: data.delayUntilRecursion || 0,
          groupName: data.groupName,
          groupWeight: data.groupWeight || 100,
          groupOverride: data.groupOverride || false,
          useGroupScoring: data.useGroupScoring || false,
          scanDepth: data.scanDepth,
          displayIndex: data.displayIndex || 0,
          addMemo: data.addMemo || false,
          decorators: data.decorators || [],
          characterFilterNames: data.characterFilterNames || [],
          characterFilterTags: data.characterFilterTags || [],
          characterFilterExclude: data.characterFilterExclude || false,
          userId: this.userId,
        })
        .returning();

      return this.transformToWorldbookEntry({
        ...worldbookChunkResult,
        content: chunkResult.text,
      });
    });
  };

  /**
   * 更新世界书条目
   */
  update = async (id: string, data: UpdateEntryData & { content?: string }): Promise<WorldbookEntry | null> => {
    return this.db.transaction(async (trx) => {
      // 获取当前记录
      const worldbookChunk = await trx.query.worldbookChunks.findFirst({
        where: and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)),
      });

      if (!worldbookChunk) return null;

      // 如果包含 content 字段，更新 chunks 表
      if (data.content !== undefined) {
        await trx
          .update(chunks)
          .set({ text: data.content, updatedAt: new Date() })
          .where(eq(chunks.id, worldbookChunk.chunkId));
      }

      // 更新 worldbook_chunks 表（排除 content 字段）
      const { content, ...worldbookChunkData } = data;
      if (Object.keys(worldbookChunkData).length > 0) {
        await trx
          .update(worldbookChunks)
          .set({ ...worldbookChunkData, updatedAt: new Date() })
          .where(and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)));
      } else if (data.content !== undefined) {
        // 如果只更新了内容，也要更新 worldbook_chunks 的时间戳
        await trx
          .update(worldbookChunks)
          .set({ updatedAt: new Date() })
          .where(and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)));
      }

      // 查询更新后的完整条目数据
      const result = await this.findById(id);
      return result;
    });
  };

  /**
   * 删除世界书条目
   */
  delete = async (id: string): Promise<void> => {
    await this.db.transaction(async (trx) => {
      // 1. 获取要删除的worldbook_chunk记录
      const worldbookChunk = await trx.query.worldbookChunks.findFirst({
        where: and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)),
      });

      if (!worldbookChunk) return;

      // 2. 删除对应的chunk（这会级联删除embeddings）
      await trx.delete(chunks).where(eq(chunks.id, worldbookChunk.chunkId));

      // 3. 删除worldbook_chunks记录（可能已经被级联删除，但为了确保一致性还是执行）
      await trx
        .delete(worldbookChunks)
        .where(and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)));
    });
  };

  /**
   * 删除整个世界书的所有chunks
   */
  deleteByWorldbookId = async (worldbookId: string): Promise<void> => {
    // 1. 查询获取chunk IDs
    const worldbookChunkList = await this.db.query.worldbookChunks.findMany({
      columns: { chunkId: true },
      where: and(eq(worldbookChunks.worldbookId, worldbookId), eq(worldbookChunks.userId, this.userId)),
    });

    if (worldbookChunkList.length === 0) return;

    const chunkIds = worldbookChunkList.map(item => item.chunkId);

    // 2. 删除操作需要事务保证原子性
    await this.db.transaction(async (trx) => {
      // 先删除embeddings，再删除chunks
      await trx.delete(embeddings).where(inArray(embeddings.chunkId, chunkIds));
      await trx.delete(chunks).where(inArray(chunks.id, chunkIds));
      // worldbook_chunks会通过外键约束自动级联删除
    });

    console.log(`🗑️ [WorldbookChunk] Deleted ${chunkIds.length} chunks for worldbook ${worldbookId}`);
  };

  /**
   * 批量创建条目
   */
  bulkCreate = async (entries: (CreateEntryData & { worldbookId: string })[]): Promise<WorldbookEntry[]> => {
    return this.db.transaction(async (trx) => {
      const results = [];

      for (const entry of entries) {
        const worldbookChunkModel = new WorldbookChunkModel(trx, this.userId);
        const result = await worldbookChunkModel.create(entry);
        results.push(result);
      }

      return results;
    });
  };

  // ====== Query Operations ======

  /**
   * 根据ID查找条目
   */
  findById = async (id: string): Promise<WorldbookEntry | null> => {
    const result = await this.db
      .select({
        id: worldbookChunks.id,
        worldbookId: worldbookChunks.worldbookId,
        chunkId: worldbookChunks.chunkId,
        title: worldbookChunks.title,
        enabled: worldbookChunks.enabled,
        keys: worldbookChunks.keys,
        keysSecondary: worldbookChunks.keysSecondary,
        selectiveLogic: worldbookChunks.selectiveLogic,
        activationMode: worldbookChunks.activationMode,
        position: worldbookChunks.position,
        depth: worldbookChunks.depth,
        role: worldbookChunks.role,
        order: worldbookChunks.order,
        matchSources: worldbookChunks.matchSources,
        caseSensitive: worldbookChunks.caseSensitive,
        matchWholeWords: worldbookChunks.matchWholeWords,
        useRegex: worldbookChunks.useRegex,
        probability: worldbookChunks.probability,
        useProbability: worldbookChunks.useProbability,
        sticky: worldbookChunks.sticky,
        cooldown: worldbookChunks.cooldown,
        delay: worldbookChunks.delay,
        excludeRecursion: worldbookChunks.excludeRecursion,
        preventRecursion: worldbookChunks.preventRecursion,
        delayUntilRecursion: worldbookChunks.delayUntilRecursion,
        groupName: worldbookChunks.groupName,
        groupWeight: worldbookChunks.groupWeight,
        groupOverride: worldbookChunks.groupOverride,
        useGroupScoring: worldbookChunks.useGroupScoring,
        scanDepth: worldbookChunks.scanDepth,
        displayIndex: worldbookChunks.displayIndex,
        addMemo: worldbookChunks.addMemo,
        decorators: worldbookChunks.decorators,
        characterFilterNames: worldbookChunks.characterFilterNames,
        characterFilterTags: worldbookChunks.characterFilterTags,
        characterFilterExclude: worldbookChunks.characterFilterExclude,
        userId: worldbookChunks.userId,
        clientId: worldbookChunks.clientId,
        createdAt: worldbookChunks.createdAt,
        updatedAt: worldbookChunks.updatedAt,
        content: chunks.text,
      })
      .from(worldbookChunks)
      .leftJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(and(eq(worldbookChunks.id, id), eq(worldbookChunks.userId, this.userId)))
      .limit(1);

    if (!result[0]) return null;
    return this.transformToWorldbookEntry(result[0]);
  };

  /**
   * 根据世界书ID查找所有条目
   */
  findByWorldbookId = async (worldbookId: string): Promise<WorldbookEntry[]> => {
    const data = await this.db
      .select({
        id: worldbookChunks.id,
        worldbookId: worldbookChunks.worldbookId,
        chunkId: worldbookChunks.chunkId,
        title: worldbookChunks.title,
        enabled: worldbookChunks.enabled,
        keys: worldbookChunks.keys,
        keysSecondary: worldbookChunks.keysSecondary,
        selectiveLogic: worldbookChunks.selectiveLogic,
        activationMode: worldbookChunks.activationMode,
        position: worldbookChunks.position,
        depth: worldbookChunks.depth,
        role: worldbookChunks.role,
        order: worldbookChunks.order,
        matchSources: worldbookChunks.matchSources,
        caseSensitive: worldbookChunks.caseSensitive,
        matchWholeWords: worldbookChunks.matchWholeWords,
        useRegex: worldbookChunks.useRegex,
        probability: worldbookChunks.probability,
        useProbability: worldbookChunks.useProbability,
        sticky: worldbookChunks.sticky,
        cooldown: worldbookChunks.cooldown,
        delay: worldbookChunks.delay,
        excludeRecursion: worldbookChunks.excludeRecursion,
        preventRecursion: worldbookChunks.preventRecursion,
        delayUntilRecursion: worldbookChunks.delayUntilRecursion,
        groupName: worldbookChunks.groupName,
        groupWeight: worldbookChunks.groupWeight,
        groupOverride: worldbookChunks.groupOverride,
        useGroupScoring: worldbookChunks.useGroupScoring,
        scanDepth: worldbookChunks.scanDepth,
        displayIndex: worldbookChunks.displayIndex,
        addMemo: worldbookChunks.addMemo,
        decorators: worldbookChunks.decorators,
        characterFilterNames: worldbookChunks.characterFilterNames,
        characterFilterTags: worldbookChunks.characterFilterTags,
        characterFilterExclude: worldbookChunks.characterFilterExclude,
        userId: worldbookChunks.userId,
        clientId: worldbookChunks.clientId,
        createdAt: worldbookChunks.createdAt,
        updatedAt: worldbookChunks.updatedAt,
        content: chunks.text,
      })
      .from(worldbookChunks)
      .leftJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(
        and(
          eq(worldbookChunks.worldbookId, worldbookId),
          eq(worldbookChunks.userId, this.userId)
        )
      )
      .orderBy(desc(worldbookChunks.order), desc(worldbookChunks.createdAt));

    return data.map(item => this.transformToWorldbookEntry(item));
  };

  /**
   * 分页查询条目
   */
  findByWorldbookIdWithParams = async (
    worldbookId: string,
    params: SearchParams
  ): Promise<PaginatedResponse<WorldbookEntry>> => {
    const {
      page = 1,
      pageSize = 20,
      query,
      enabled,
      activationMode,
      position,
      groupName,
      hasKeys,
      hasSecondaryKeys,
      selectiveLogic,
      orderMin,
      orderMax,
      probabilityMin,
      probabilityMax,
      sortBy = 'order',
      sortOrder = 'asc'
    } = params;

    // 构建查询条件
    const conditions = [
      eq(worldbookChunks.worldbookId, worldbookId),
      eq(worldbookChunks.userId, this.userId)
    ];

    // 文本搜索
    if (query) {
      conditions.push(
        or(
          ilike(worldbookChunks.title, `%${query}%`),
          ilike(chunks.text, `%${query}%`)
        )!
      );
    }

    // 过滤条件
    if (enabled !== undefined) {
      conditions.push(eq(worldbookChunks.enabled, enabled));
    }
    if (activationMode) {
      conditions.push(eq(worldbookChunks.activationMode, activationMode));
    }
    if (position) {
      conditions.push(eq(worldbookChunks.position, position));
    }
    if (groupName) {
      conditions.push(ilike(worldbookChunks.groupName, `%${groupName}%`));
    }
    if (selectiveLogic) {
      conditions.push(eq(worldbookChunks.selectiveLogic, selectiveLogic));
    }

    // 范围过滤
    if (orderMin !== undefined) {
      conditions.push(gte(worldbookChunks.order, orderMin));
    }
    if (orderMax !== undefined) {
      conditions.push(lte(worldbookChunks.order, orderMax));
    }
    if (probabilityMin !== undefined) {
      conditions.push(gte(worldbookChunks.probability, probabilityMin));
    }
    if (probabilityMax !== undefined) {
      conditions.push(lte(worldbookChunks.probability, probabilityMax));
    }

    // 键值过滤
    if (hasKeys !== undefined) {
      if (hasKeys) {
        conditions.push(sql`array_length(${worldbookChunks.keys}, 1) > 0`);
      } else {
        conditions.push(
          or(
            sql`array_length(${worldbookChunks.keys}, 1) IS NULL`,
            sql`array_length(${worldbookChunks.keys}, 1) = 0`
          )!
        );
      }
    }

    if (hasSecondaryKeys !== undefined) {
      if (hasSecondaryKeys) {
        conditions.push(sql`array_length(${worldbookChunks.keysSecondary}, 1) > 0`);
      } else {
        conditions.push(
          or(
            sql`array_length(${worldbookChunks.keysSecondary}, 1) IS NULL`,
            sql`array_length(${worldbookChunks.keysSecondary}, 1) = 0`
          )!
        );
      }
    }

    // 构建排序
    let orderByClause;
    const isAsc = sortOrder === 'asc';
    switch (sortBy) {
      case 'title':
        orderByClause = isAsc ? asc(worldbookChunks.title) : desc(worldbookChunks.title);
        break;
      case 'createdAt':
        orderByClause = isAsc ? asc(worldbookChunks.createdAt) : desc(worldbookChunks.createdAt);
        break;
      case 'updatedAt':
        orderByClause = isAsc ? asc(worldbookChunks.updatedAt) : desc(worldbookChunks.updatedAt);
        break;
      case 'probability':
        orderByClause = isAsc ? asc(worldbookChunks.probability) : desc(worldbookChunks.probability);
        break;
      case 'order':
      default:
        orderByClause = isAsc ? asc(worldbookChunks.order) : desc(worldbookChunks.order);
        break;
    }

    // 获取总数
    const totalResult = await this.db
      .select({ count: count() })
      .from(worldbookChunks)
      .leftJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(and(...conditions));

    const total = totalResult[0]?.count || 0;

    // 获取分页数据
    const offset = (page - 1) * pageSize;
    const data = await this.db
      .select({
        id: worldbookChunks.id,
        worldbookId: worldbookChunks.worldbookId,
        chunkId: worldbookChunks.chunkId,
        title: worldbookChunks.title,
        enabled: worldbookChunks.enabled,
        keys: worldbookChunks.keys,
        keysSecondary: worldbookChunks.keysSecondary,
        selectiveLogic: worldbookChunks.selectiveLogic,
        activationMode: worldbookChunks.activationMode,
        position: worldbookChunks.position,
        depth: worldbookChunks.depth,
        role: worldbookChunks.role,
        order: worldbookChunks.order,
        matchSources: worldbookChunks.matchSources,
        caseSensitive: worldbookChunks.caseSensitive,
        matchWholeWords: worldbookChunks.matchWholeWords,
        useRegex: worldbookChunks.useRegex,
        probability: worldbookChunks.probability,
        useProbability: worldbookChunks.useProbability,
        sticky: worldbookChunks.sticky,
        cooldown: worldbookChunks.cooldown,
        delay: worldbookChunks.delay,
        excludeRecursion: worldbookChunks.excludeRecursion,
        preventRecursion: worldbookChunks.preventRecursion,
        delayUntilRecursion: worldbookChunks.delayUntilRecursion,
        groupName: worldbookChunks.groupName,
        groupWeight: worldbookChunks.groupWeight,
        groupOverride: worldbookChunks.groupOverride,
        useGroupScoring: worldbookChunks.useGroupScoring,
        scanDepth: worldbookChunks.scanDepth,
        displayIndex: worldbookChunks.displayIndex,
        addMemo: worldbookChunks.addMemo,
        decorators: worldbookChunks.decorators,
        characterFilterNames: worldbookChunks.characterFilterNames,
        characterFilterTags: worldbookChunks.characterFilterTags,
        characterFilterExclude: worldbookChunks.characterFilterExclude,
        userId: worldbookChunks.userId,
        clientId: worldbookChunks.clientId,
        createdAt: worldbookChunks.createdAt,
        updatedAt: worldbookChunks.updatedAt,
        content: chunks.text,
      })
      .from(worldbookChunks)
      .leftJoin(chunks, eq(worldbookChunks.chunkId, chunks.id))
      .where(and(...conditions))
      .orderBy(orderByClause)
      .limit(pageSize)
      .offset(offset);

    return {
      data: data.map(item => this.transformToWorldbookEntry(item)),
      total,
      page,
      pageSize,
      hasMore: offset + pageSize < total
    };
  };

  // ====== Helper Methods ======

  /**
   * 将数据库记录转换为前端类型
   */
  private transformToWorldbookEntry = (item: WorldbookChunkItem & { content: string }): WorldbookEntry => {
    return {
      id: item.id,
      title: item.title,
      content: item.content,
      enabled: item.enabled,
      worldbookId: item.worldbookId,
      chunkId: item.chunkId,
      keys: item.keys,
      keysSecondary: item.keysSecondary,
      selectiveLogic: item.selectiveLogic,
      activationMode: item.activationMode,
      position: item.position,
      depth: item.depth || undefined,
      role: item.role || undefined,
      order: item.order,
      matchSources: item.matchSources,
      caseSensitive: item.caseSensitive || undefined,
      matchWholeWords: item.matchWholeWords || undefined,
      useRegex: item.useRegex,
      probability: item.probability,
      useProbability: item.useProbability,
      sticky: item.sticky || undefined,
      cooldown: item.cooldown || undefined,
      delay: item.delay || undefined,
      excludeRecursion: item.excludeRecursion,
      preventRecursion: item.preventRecursion,
      delayUntilRecursion: item.delayUntilRecursion,
      groupName: item.groupName || undefined,
      groupWeight: item.groupWeight,
      groupOverride: item.groupOverride,
      useGroupScoring: item.useGroupScoring,
      scanDepth: item.scanDepth || undefined,
      displayIndex: item.displayIndex,
      addMemo: item.addMemo,
      decorators: item.decorators,
      characterFilterNames: item.characterFilterNames,
      characterFilterTags: item.characterFilterTags,
      characterFilterExclude: item.characterFilterExclude,
      userId: item.userId,
      clientId: item.clientId || undefined,
      createdAt: item.createdAt.toISOString(),
      updatedAt: item.updatedAt.toISOString(),
    };
  };
}
