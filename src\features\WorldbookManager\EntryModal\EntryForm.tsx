'use client';

import { Button, Form, Input, Select, SliderWithInput, TextArea } from '@lobehub/ui';
import { Select as AntdSelect, Tabs, Collapse, InputNumber, Checkbox } from 'antd';
import { Switch, message } from 'antd';
import { createStyles } from 'antd-style';
import { memo, useEffect, useRef } from 'react';

import { useWorldbookStore } from '@/store/worldbook';
import {
  SelectiveLogic,
  WorldbookEntry,
  ActivationMode,
  WorldbookPosition
} from '@/types/worldbook';

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    display: flex;
    flex-direction: column;
    height: 70vh;
  `,
  scrollArea: css`
    flex: 1;
    overflow-y: auto;
    padding: 24px;
  `,
  footer: css`
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid ${token.colorBorderSecondary};
    background: ${token.colorBgContainer};
    flex-shrink: 0;
  `,
  formItem: css`
    margin-bottom: 16px;

    .ant-form-item-label > label {
      font-weight: 500;
    }
  `,
  switchRow: css`
    display: flex;
    gap: 24px;
    margin-bottom: 16px;
    align-items: flex-start;

    .switch-item {
      flex: 1;
      display: flex;
      flex-direction: column;

      .ant-switch {
        width: fit-content;
      }
    }

    .switch-label {
      font-weight: 500;
      margin-bottom: 4px;
      color: ${token.colorText};
    }

    .ant-form-item {
      margin-bottom: 0 !important;
    }

    .ant-form-item-control {
      margin-bottom: 0 !important;
    }

    @media (max-width: 768px) {
      flex-direction: column;
    }
  `,



  section: css`
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  `,
  sectionTitle: css`
    font-size: 16px;
    font-weight: 600;
    color: ${token.colorText};
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
  `,
  textarea: css`
    min-height: 120px;
  `,

}));

interface EntryFormProps {
  entry?: WorldbookEntry;
  onClose?: () => void;
  onSuccess?: () => void;
  worldbookId: string;
}



const EntryForm = memo<EntryFormProps>(({ worldbookId, entry, onClose, onSuccess }) => {
  const { styles } = useStyles();
  const [form] = Form.useForm();
  const submitRef = useRef<boolean>(false);

  const { createEntry, updateEntry, entryUpdatingIds } = useWorldbookStore((s) => ({
    createEntry: s.createEntry,
    updateEntry: s.updateEntry,
    entryUpdatingIds: s.entryUpdatingIds,
  }));

  const isEdit = !!entry;
  const isUpdating = entry ? entryUpdatingIds.includes(entry.id) : false;

  // 初始化表单数据
  useEffect(() => {
    if (entry) {
      form.setFieldsValue({
        // 基础字段
        title: entry.title || '',
        content: entry.content || '',
        enabled: entry.enabled !== false,

        // 激活规则
        keys: entry.keys || [],
        keysSecondary: entry.keysSecondary || [],
        selectiveLogic: entry.selectiveLogic || SelectiveLogic.AND_ANY,

        // 激活模式
        activationMode: entry.activationMode || ActivationMode.Keyword,

        // 位置控制
        position: entry.position || WorldbookPosition.After,
        depth: entry.depth || 4,
        role: entry.role || 'system',
        order: entry.order || 100,

        // 匹配配置
        matchSources: entry.matchSources || {
          personaDescription: false,
          characterDescription: false,
          characterPersonality: false,
          characterDepthPrompt: false,
          scenario: false,
          creatorNotes: false,
        },
        caseSensitive: entry.caseSensitive || false,
        matchWholeWords: entry.matchWholeWords || false,
        useRegex: entry.useRegex || false,

        // 概率控制
        probability: entry.probability || 100,
        useProbability: entry.useProbability || false,

        // 时间效果
        sticky: entry.sticky || 0,
        cooldown: entry.cooldown || 0,
        delay: entry.delay || 0,

        // 递归控制
        excludeRecursion: entry.excludeRecursion || false,
        preventRecursion: entry.preventRecursion || false,
        delayUntilRecursion: entry.delayUntilRecursion || 0,

        // 分组功能
        groupName: entry.groupName || '',
        groupWeight: entry.groupWeight || 100,
        groupOverride: entry.groupOverride || false,
        useGroupScoring: entry.useGroupScoring || false,

        // 扫描控制
        scanDepth: entry.scanDepth || 10,

        // 显示控制
        displayIndex: entry.displayIndex || 0,
        addMemo: entry.addMemo || false,

        // 装饰器支持
        decorators: entry.decorators || [],

        // 角色过滤
        characterFilterNames: entry.characterFilterNames || [],
        characterFilterTags: entry.characterFilterTags || [],
        characterFilterExclude: entry.characterFilterExclude || false,
      });
    } else {
      // 新建条目的默认值
      form.setFieldsValue({
        // 基础字段
        title: '',
        content: '',
        enabled: true,

        // 激活规则
        keys: [],
        keysSecondary: [],
        selectiveLogic: SelectiveLogic.AND_ANY,

        // 激活模式
        activationMode: ActivationMode.Keyword,

        // 位置控制
        position: WorldbookPosition.After,
        depth: 4,
        role: 'system',
        order: 100,

        // 匹配配置
        matchSources: {
          personaDescription: false,
          characterDescription: false,
          characterPersonality: false,
          characterDepthPrompt: false,
          scenario: false,
          creatorNotes: false,
        },
        caseSensitive: false,
        matchWholeWords: false,
        useRegex: false,

        // 概率控制
        probability: 100,
        useProbability: false,

        // 时间效果
        sticky: 0,
        cooldown: 0,
        delay: 0,

        // 递归控制
        excludeRecursion: false,
        preventRecursion: false,
        delayUntilRecursion: 0,

        // 分组功能
        groupName: '',
        groupWeight: 100,
        groupOverride: false,
        useGroupScoring: false,

        // 扫描控制
        scanDepth: 10,

        // 显示控制
        displayIndex: 0,
        addMemo: false,

        // 装饰器支持
        decorators: [],

        // 角色过滤
        characterFilterNames: [],
        characterFilterTags: [],
        characterFilterExclude: false,
      });
    }
  }, [entry, form]);

  const handleSubmit = async (values: any) => {
    console.log('handleSubmit called with values:', values);
    console.log('submitRef.current:', submitRef.current);
    console.log('isEdit:', isEdit, 'entry:', entry?.id);

    if (submitRef.current) {
      console.log('Submit already in progress, returning');
      return;
    }
    submitRef.current = true;

    try {
      const entryData = {
        // 基础字段
        title: values.title,
        content: values.content,
        enabled: values.enabled,

        // 激活规则
        keys: Array.isArray(values.keys) ? values.keys : [],
        keysSecondary: Array.isArray(values.keysSecondary) ? values.keysSecondary : [],
        selectiveLogic: values.selectiveLogic,

        // 激活模式
        activationMode: values.activationMode,

        // 位置控制
        position: values.position,
        depth: values.depth,
        role: values.role,
        order: values.order,

        // 匹配配置
        matchSources: values.matchSources || {
          personaDescription: false,
          characterDescription: false,
          characterPersonality: false,
          characterDepthPrompt: false,
          scenario: false,
          creatorNotes: false,
        },
        caseSensitive: values.caseSensitive,
        matchWholeWords: values.matchWholeWords,
        useRegex: values.useRegex,

        // 概率控制
        probability: values.probability,
        useProbability: values.useProbability,

        // 时间效果
        sticky: values.sticky,
        cooldown: values.cooldown,
        delay: values.delay,

        // 递归控制
        excludeRecursion: values.excludeRecursion,
        preventRecursion: values.preventRecursion,
        delayUntilRecursion: values.delayUntilRecursion,

        // 分组功能
        groupName: values.groupName || undefined,
        groupWeight: values.groupWeight,
        groupOverride: values.groupOverride,
        useGroupScoring: values.useGroupScoring,

        // 扫描控制
        scanDepth: values.scanDepth,

        // 显示控制
        displayIndex: values.displayIndex,
        addMemo: values.addMemo,

        // 装饰器支持
        decorators: Array.isArray(values.decorators) ? values.decorators : [],

        // 角色过滤
        characterFilterNames: Array.isArray(values.characterFilterNames) ? values.characterFilterNames : [],
        characterFilterTags: Array.isArray(values.characterFilterTags) ? values.characterFilterTags : [],
        characterFilterExclude: values.characterFilterExclude,
      };

      if (isEdit && entry) {
        await updateEntry(entry.id, entryData);
        message.success('条目更新成功');
      } else {
        await createEntry(worldbookId, entryData);
        message.success('条目创建成功');
      }

      onSuccess?.();
    } catch (error) {
      console.error('Failed to save entry:', error);
      message.error(isEdit ? '更新失败，请重试' : '创建失败，请重试');
    } finally {
      submitRef.current = false;
    }
  };

  const handleCancel = () => {
    onClose?.();
  };

  return (
    <div className={styles.container}>
      <div className={styles.scrollArea}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Tabs
            defaultActiveKey="basic"
            items={[
              {
                key: 'basic',
                label: '基本信息',
                children: (
                  <div>
                    <Form.Item
                      className={styles.formItem}
                      label="条目标题"
                      name="title"
                      rules={[
                        { max: 100, message: '标题不能超过100个字符' },
                        { message: '请输入条目标题', required: true },
                      ]}
                    >
                      <Input placeholder="请输入条目标题" />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="条目内容"
                      name="content"
                      rules={[
                        { message: '请输入条目内容', required: true },
                      ]}
                    >
                      <TextArea
                        autoSize={{ minRows: 4, maxRows: 12 }}
                        className={styles.textarea}
                        placeholder="请输入条目内容"
                      />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="启用状态"
                      name="enabled"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </div>
                ),
              },
              {
                key: 'activation',
                label: '激活规则',
                children: (
                  <div>
                    <Form.Item
                      className={styles.formItem}
                      label="主关键词"
                      name="keys"
                      tooltip="输入触发此条目的主要关键词，按回车添加"
                    >
                      <AntdSelect
                        mode="tags"
                        placeholder="输入主关键词，按回车添加"
                        style={{ width: '100%' }}
                        tokenSeparators={[',']}
                      />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="次关键词"
                      name="keysSecondary"
                      tooltip="输入辅助关键词，按回车添加"
                    >
                      <AntdSelect
                        mode="tags"
                        placeholder="输入次关键词，按回车添加"
                        style={{ width: '100%' }}
                        tokenSeparators={[',']}
                      />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="选择逻辑"
                      name="selectiveLogic"
                    >
                      <Select
                        options={[
                          { label: '任意匹配', value: SelectiveLogic.AND_ANY },
                          { label: '非全部匹配', value: SelectiveLogic.NOT_ALL },
                          { label: '非任意匹配', value: SelectiveLogic.NOT_ANY },
                          { label: '全部匹配', value: SelectiveLogic.AND_ALL },
                        ]}
                      />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="激活模式"
                      name="activationMode"
                    >
                      <Select
                        options={[
                          { label: '关键字激活', value: ActivationMode.Keyword },
                          { label: '始终激活', value: ActivationMode.Constant },
                          { label: '向量化激活', value: ActivationMode.Vectorized },
                        ]}
                      />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="优先级"
                      name="order"
                      tooltip="数值越小优先级越高"
                    >
                      <SliderWithInput max={1000} min={0} />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="触发概率"
                      name="probability"
                      tooltip="条目被触发的概率百分比"
                    >
                      <SliderWithInput max={100} min={0} />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="使用概率控制"
                      name="useProbability"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </div>
                ),
              },
              {
                key: 'position',
                label: '位置控制',
                children: (
                  <div>
                    <Form.Item
                      className={styles.formItem}
                      label="注入位置"
                      name="position"
                    >
                      <Select
                        options={[
                          { label: '角色定义前', value: WorldbookPosition.Before },
                          { label: '角色定义后', value: WorldbookPosition.After },
                          { label: '作者注释顶部', value: WorldbookPosition.AuthorNoteTop },
                          { label: '作者注释底部', value: WorldbookPosition.AuthorNoteBottom },
                          { label: '指定深度', value: WorldbookPosition.AtDepth },
                          { label: '示例消息顶部', value: WorldbookPosition.ExampleMessageTop },
                          { label: '示例消息底部', value: WorldbookPosition.ExampleMessageBottom },
                        ]}
                      />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="深度"
                      name="depth"
                      tooltip="当位置为指定深度时使用"
                    >
                      <InputNumber min={1} max={20} />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="角色类型"
                      name="role"
                      tooltip="当位置为指定深度时使用"
                    >
                      <Select
                        options={[
                          { label: '系统', value: 'system' },
                          { label: '用户', value: 'user' },
                          { label: '助手', value: 'assistant' },
                          { label: '工具', value: 'tool' },
                        ]}
                      />
                    </Form.Item>
                  </div>
                ),
              },
              {
                key: 'matching',
                label: '匹配选项',
                children: (
                  <div>
                    <div className={styles.formItem}>
                      <div style={{ fontWeight: 500, marginBottom: '8px' }}>
                        匹配源配置
                        <span style={{ color: '#999', fontSize: '12px', marginLeft: '8px' }}>
                          配置条目可以匹配的内容源
                        </span>
                      </div>
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                        <Form.Item name={['matchSources', 'personaDescription']} valuePropName="checked" style={{ marginBottom: 0 }}>
                          <Checkbox>人格描述</Checkbox>
                        </Form.Item>
                        <Form.Item name={['matchSources', 'characterDescription']} valuePropName="checked" style={{ marginBottom: 0 }}>
                          <Checkbox>角色描述</Checkbox>
                        </Form.Item>
                        <Form.Item name={['matchSources', 'characterPersonality']} valuePropName="checked" style={{ marginBottom: 0 }}>
                          <Checkbox>角色性格</Checkbox>
                        </Form.Item>
                        <Form.Item name={['matchSources', 'characterDepthPrompt']} valuePropName="checked" style={{ marginBottom: 0 }}>
                          <Checkbox>角色深度提示</Checkbox>
                        </Form.Item>
                        <Form.Item name={['matchSources', 'scenario']} valuePropName="checked" style={{ marginBottom: 0 }}>
                          <Checkbox>场景</Checkbox>
                        </Form.Item>
                        <Form.Item name={['matchSources', 'creatorNotes']} valuePropName="checked" style={{ marginBottom: 0 }}>
                          <Checkbox>创作者备注</Checkbox>
                        </Form.Item>
                      </div>
                    </div>

                    <Form.Item
                      className={styles.formItem}
                      label="大小写敏感"
                      name="caseSensitive"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="全词匹配"
                      name="matchWholeWords"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="使用正则表达式"
                      name="useRegex"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      className={styles.formItem}
                      label="扫描深度"
                      name="scanDepth"
                      tooltip="扫描多少条历史消息"
                    >
                      <SliderWithInput max={50} min={1} />
                    </Form.Item>
                  </div>
                ),
              },
              {
                key: 'advanced',
                label: '高级设置',
                children: (
                  <div>
                    <Collapse
                      items={[
                        {
                          key: 'timing',
                          label: '时间效果',
                          children: (
                            <div>
                              <Form.Item
                                className={styles.formItem}
                                label="粘性时间"
                                name="sticky"
                                tooltip="条目保持激活的时间（轮次）"
                              >
                                <InputNumber min={0} max={100} />
                              </Form.Item>

                              <Form.Item
                                className={styles.formItem}
                                label="冷却时间"
                                name="cooldown"
                                tooltip="条目激活后的冷却时间（轮次）"
                              >
                                <InputNumber min={0} max={100} />
                              </Form.Item>

                              <Form.Item
                                className={styles.formItem}
                                label="延迟时间"
                                name="delay"
                                tooltip="条目激活前的延迟时间（轮次）"
                              >
                                <InputNumber min={0} max={100} />
                              </Form.Item>
                            </div>
                          ),
                        },
                        {
                          key: 'recursion',
                          label: '递归控制',
                          children: (
                            <div>
                              <Form.Item
                                className={styles.formItem}
                                label="排除递归"
                                name="excludeRecursion"
                                valuePropName="checked"
                              >
                                <Switch />
                              </Form.Item>

                              <Form.Item
                                className={styles.formItem}
                                label="防止递归"
                                name="preventRecursion"
                                valuePropName="checked"
                              >
                                <Switch />
                              </Form.Item>

                              <Form.Item
                                className={styles.formItem}
                                label="递归延迟"
                                name="delayUntilRecursion"
                                tooltip="递归扫描的延迟时间"
                              >
                                <InputNumber min={0} max={100} />
                              </Form.Item>
                            </div>
                          ),
                        },
                        {
                          key: 'grouping',
                          label: '分组功能',
                          children: (
                            <div>
                              <Form.Item
                                className={styles.formItem}
                                label="分组名称"
                                name="groupName"
                                tooltip="条目所属的分组"
                              >
                                <Input placeholder="输入分组名称" />
                              </Form.Item>

                              <Form.Item
                                className={styles.formItem}
                                label="分组权重"
                                name="groupWeight"
                                tooltip="在分组中的权重"
                              >
                                <SliderWithInput max={1000} min={0} />
                              </Form.Item>

                              <Form.Item
                                className={styles.formItem}
                                label="分组覆盖"
                                name="groupOverride"
                                valuePropName="checked"
                              >
                                <Switch />
                              </Form.Item>

                              <Form.Item
                                className={styles.formItem}
                                label="使用分组评分"
                                name="useGroupScoring"
                                valuePropName="checked"
                              >
                                <Switch />
                              </Form.Item>
                            </div>
                          ),
                        },
                        {
                          key: 'display',
                          label: '显示控制',
                          children: (
                            <div>
                              <Form.Item
                                className={styles.formItem}
                                label="显示索引"
                                name="displayIndex"
                                tooltip="在UI中的显示顺序"
                              >
                                <InputNumber min={0} max={1000} />
                              </Form.Item>

                              <Form.Item
                                className={styles.formItem}
                                label="添加备注"
                                name="addMemo"
                                valuePropName="checked"
                              >
                                <Switch />
                              </Form.Item>
                            </div>
                          ),
                        },
                        {
                          key: 'decorators',
                          label: '装饰器',
                          children: (
                            <div>
                              <Form.Item
                                className={styles.formItem}
                                label="装饰器"
                                name="decorators"
                                tooltip="应用于此条目的装饰器"
                              >
                                <AntdSelect
                                  mode="tags"
                                  placeholder="输入装饰器，按回车添加"
                                  style={{ width: '100%' }}
                                  tokenSeparators={[',']}
                                />
                              </Form.Item>
                            </div>
                          ),
                        },
                        {
                          key: 'character',
                          label: '角色过滤',
                          children: (
                            <div>
                              <Form.Item
                                className={styles.formItem}
                                label="角色名称过滤"
                                name="characterFilterNames"
                                tooltip="限制此条目只对特定角色生效"
                              >
                                <AntdSelect
                                  mode="tags"
                                  placeholder="输入角色名称，按回车添加"
                                  style={{ width: '100%' }}
                                  tokenSeparators={[',']}
                                />
                              </Form.Item>

                              <Form.Item
                                className={styles.formItem}
                                label="角色标签过滤"
                                name="characterFilterTags"
                                tooltip="限制此条目只对特定标签的角色生效"
                              >
                                <AntdSelect
                                  mode="tags"
                                  placeholder="输入角色标签，按回车添加"
                                  style={{ width: '100%' }}
                                  tokenSeparators={[',']}
                                />
                              </Form.Item>

                              <Form.Item
                                className={styles.formItem}
                                label="排除模式"
                                name="characterFilterExclude"
                                valuePropName="checked"
                                tooltip="是否排除指定的角色"
                              >
                                <Switch />
                              </Form.Item>
                            </div>
                          ),
                        },
                      ]}
                    />
                  </div>
                ),
              },
            ]}
          />
        </Form>
      </div>

      {/* 固定在底部的按钮区域 */}
      <div className={styles.footer}>
        <Button onClick={handleCancel}>
          取消
        </Button>
        <Button loading={isUpdating} onClick={() => form.submit()} type="primary">
          {isEdit ? '更新条目' : '创建条目'}
        </Button>
      </div>
    </div>
  );
});

EntryForm.displayName = 'EntryForm';

export default EntryForm;
