import { DEFAULT_WORLDBOOK_CONFIG } from '@/const/settings/worldbook';
import type { UserStore } from '@/store/user';
import { merge } from '@/utils/merge';

import { currentSettings } from './settings';

const worldbookConfig = (s: UserStore) =>
  merge(DEFAULT_WORLDBOOK_CONFIG, currentSettings(s).worldbook);

const enabled = (s: UserStore) => worldbookConfig(s).enabled;
const scanDepth = (s: UserStore) => worldbookConfig(s).scanDepth;
const budgetPercent = (s: UserStore) => worldbookConfig(s).budgetPercent;
const budgetCap = (s: UserStore) => worldbookConfig(s).budgetCap;
const minActivations = (s: UserStore) => worldbookConfig(s).minActivations;
const minActivationsDepthMax = (s: UserStore) => worldbookConfig(s).minActivationsDepthMax;
const useGroupScoring = (s: UserStore) => worldbookConfig(s).useGroupScoring;
const recursiveEnabled = (s: UserStore) => worldbookConfig(s).recursiveEnabled;
const maxRecursionDepth = (s: UserStore) => worldbookConfig(s).maxRecursionDepth;
const caseSensitive = (s: UserStore) => worldbookConfig(s).caseSensitive;
const matchWholeWords = (s: UserStore) => worldbookConfig(s).matchWholeWords;
const includeNames = (s: UserStore) => worldbookConfig(s).includeNames;
const overflowAlert = (s: UserStore) => worldbookConfig(s).overflowAlert;
const characterStrategy = (s: UserStore) => worldbookConfig(s).characterStrategy;

export const worldbookSettingsSelectors = {
  budgetCap,
  budgetPercent,
  caseSensitive,
  characterStrategy,
  config: worldbookConfig,
  enabled,
  includeNames,
  matchWholeWords,
  maxRecursionDepth,
  minActivations,
  minActivationsDepthMax,
  overflowAlert,
  recursiveEnabled,
  scanDepth,
  useGroupScoring,
};
