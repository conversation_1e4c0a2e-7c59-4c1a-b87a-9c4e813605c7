import { Icon, Modal } from '@lobehub/ui';
import { Settings } from 'lucide-react';
import { memo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import SettingsForm from './SettingsForm';

interface SettingsModalProps {
  onSuccess?: () => void;
  open: boolean;
  onClose: () => void;
}

const Title = () => {
  const { t } = useTranslation('worldbook');
  return (
    <Flexbox gap={8} horizontal>
      <Icon icon={Settings} />
      {t('settings.title')}
    </Flexbox>
  );
};

const SettingsModal = memo<SettingsModalProps>(({ onSuccess, open, onClose }) => {
  return (
    <Modal
      centered
      footer={null}
      onCancel={onClose}
      open={open}
      styles={{
        body: { padding: 0 },
        content: {
          maxHeight: '85vh',
          overflow: 'hidden',
        },
      }}
      title={<Title />}
      width={700}
    >
      <SettingsForm
        onClose={onClose}
        onSuccess={() => {
          onSuccess?.();
          onClose();
        }}
      />
    </Modal>
  );
});

// Hook模式已移除，统一使用直接的Modal组件管理

export default SettingsModal;
