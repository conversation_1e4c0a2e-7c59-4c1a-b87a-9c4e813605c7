import { z } from 'zod';

import { WorldbookModel } from '@/database/models/worldbook';
import { authedProcedure, router } from '@/libs/trpc/lambda';
import { serverDatabase } from '@/libs/trpc/lambda/middleware';
import type {
  BulkOperationResult,
  CreateWorldbookData,
  ImportResultDetail,
  UpdateWorldbookData,
  Worldbook,
} from '@/types/worldbook';
import { createEntryBaseSchema } from '@/types/worldbook/schemas';

const worldbookProcedure = authedProcedure.use(serverDatabase).use(async (opts) => {
  const { ctx } = opts;

  return opts.next({
    ctx: {
      worldbookModel: new WorldbookModel(ctx.serverDB, ctx.userId),
    },
  });
});

// Zod schemas for validation
const createWorldbookSchema = z.object({
  name: z.string().min(1, '世界书名称不能为空'),
  description: z.string().optional(),
}) satisfies z.ZodType<CreateWorldbookData>;

const updateWorldbookSchema = z.object({
  name: z.string().min(1, '世界书名称不能为空').optional(),
  description: z.string().optional(),
  enabled: z.boolean().optional(),
}) satisfies z.ZodType<UpdateWorldbookData>;

const importWorldbookSchema = z.object({
  worldbook: createWorldbookSchema,
  entries: z.array(createEntryBaseSchema),
});

export const worldbookRouter = router({
  // ====== Query Operations ======

  /**
   * 获取用户的所有世界书
   */
  getWorldbooks: worldbookProcedure.query(async ({ ctx }): Promise<Worldbook[]> => {
    console.log('🔍 [WorldbookRouter] getWorldbooks called for userId:', ctx.userId);
    const result = await ctx.worldbookModel.query();
    console.log('🔍 [WorldbookRouter] getWorldbooks result count:', result.length);
    return result;
  }),

  /**
   * 根据ID获取世界书
   */
  getWorldbookById: worldbookProcedure
    .input(z.object({ id: z.string().min(1, 'ID不能为空') }))
    .query(async ({ ctx, input }): Promise<Worldbook | null> => {
      console.log('🔍 [WorldbookRouter] getWorldbookById called:', input.id);
      const result = await ctx.worldbookModel.findById(input.id);
      console.log('🔍 [WorldbookRouter] getWorldbookById result:', result ? 'found' : 'not found');
      return result;
    }),

  // ====== Mutation Operations ======

  /**
   * 创建世界书
   */
  createWorldbook: worldbookProcedure
    .input(createWorldbookSchema)
    .mutation(async ({ input, ctx }): Promise<Worldbook> => {
      console.log('📚 [WorldbookRouter] createWorldbook called:', input.name);
      const result = await ctx.worldbookModel.create(input);
      console.log('✅ [WorldbookRouter] createWorldbook success:', result.id);
      return result;
    }),

  /**
   * 更新世界书
   */
  updateWorldbook: worldbookProcedure
    .input(
      z.object({
        id: z.string().min(1, 'ID不能为空'),
        data: updateWorldbookSchema,
      }),
    )
    .mutation(async ({ input, ctx }): Promise<Worldbook | null> => {
      console.log('📝 [WorldbookRouter] updateWorldbook called:', input.id);
      const result = await ctx.worldbookModel.update(input.id, input.data);
      console.log('✅ [WorldbookRouter] updateWorldbook result:', result ? 'updated' : 'not found');
      return result;
    }),

  /**
   * 删除世界书及其所有相关数据
   */
  deleteWorldbook: worldbookProcedure
    .input(z.object({ id: z.string().min(1, 'ID不能为空') }))
    .mutation(async ({ input, ctx }): Promise<void> => {
      console.log('🗑️ [WorldbookRouter] deleteWorldbook called:', input.id);
      await ctx.worldbookModel.delete(input.id);
      console.log('✅ [WorldbookRouter] deleteWorldbook completed:', input.id);
    }),

  /**
   * 删除用户的所有世界书
   */
  deleteAllWorldbooks: worldbookProcedure
    .mutation(async ({ ctx }): Promise<void> => {
      console.log('🗑️ [WorldbookRouter] deleteAllWorldbooks called for userId:', ctx.userId);
      await ctx.worldbookModel.deleteAll();
      console.log('✅ [WorldbookRouter] deleteAllWorldbooks completed');
    }),

  // ====== Import/Export Operations ======

  /**
   * 导入世界书（创建世界书和条目）
   */
  importWorldbook: worldbookProcedure
    .input(importWorldbookSchema)
    .mutation(async ({ input, ctx }): Promise<ImportResultDetail> => {
      console.log('📥 [WorldbookRouter] importWorldbook called:', {
        worldbookName: input.worldbook.name,
        entriesCount: input.entries.length,
        userId: ctx.userId,
      });

      const result = await ctx.worldbookModel.importWorldbook(input);

      console.log('✅ [WorldbookRouter] importWorldbook completed:', result);
      return result;
    }),

  // ====== Batch Operations ======

  /**
   * 批量删除世界书
   */
  bulkDeleteWorldbooks: worldbookProcedure
    .input(z.object({ ids: z.array(z.string().min(1)).min(1, '至少需要选择一个世界书') }))
    .mutation(async ({ input, ctx }): Promise<BulkOperationResult> => {
      console.log('🗑️ [WorldbookRouter] bulkDeleteWorldbooks called:', input.ids);

      const errors: Array<{ id: string; error: string }> = [];
      let successCount = 0;

      for (const id of input.ids) {
        try {
          await ctx.worldbookModel.delete(id);
          successCount++;
        } catch (error) {
          console.error(`❌ [WorldbookRouter] Failed to delete worldbook ${id}:`, error);
          errors.push({
            id,
            error: error instanceof Error ? error.message : '删除失败',
          });
        }
      }

      const result = {
        successCount,
        failedCount: errors.length,
        errors,
      };

      console.log('✅ [WorldbookRouter] bulkDeleteWorldbooks completed:', result);
      return result;
    }),

  // ====== Statistics Operations ======

  /**
   * 获取世界书统计信息
   */
  getWorldbookStats: worldbookProcedure.query(async ({ ctx }) => {
    console.log('📊 [WorldbookRouter] getWorldbookStats called for userId:', ctx.userId);

    const worldbooks = await ctx.worldbookModel.query();

    const stats = {
      totalWorldbooks: worldbooks.length,
      enabledWorldbooks: worldbooks.filter(wb => wb.enabled).length,
      totalEntries: worldbooks.reduce((sum, wb) => sum + (wb.entryCount || 0), 0),
      worldbooksWithAgent: worldbooks.filter(wb => wb.primaryAgent).length,
    };

    console.log('📊 [WorldbookRouter] getWorldbookStats result:', stats);
    return stats;
  }),
});