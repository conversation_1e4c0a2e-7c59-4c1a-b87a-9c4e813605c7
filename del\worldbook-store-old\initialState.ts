import type { Worldbook, WorldbookEntry } from '@/types/worldbook';

export interface WorldbookStoreState {
  currentEntry: WorldbookEntry | null;
  currentWorldbook: Worldbook | null;
  // 条目管理
  entries: WorldbookEntry[];
  entriesTotal: number;
  entriesPage: number;
  entriesPageSize: number;
  entriesHasMore: boolean;

  // 加载状态管理
  entryLoadingIds: string[];
  entryUpdatingIds: string[];
  entriesLoading: boolean;

  filterEnabled: boolean | null;
  importing: boolean;

  // UI状态
  loading: boolean;
  // 搜索和筛选
  searchQuery: string;
  selectedEntryIds: string[];
  selectedWorldbookId: string | null;

  sidebarCollapsed: boolean;
  // null表示显示全部，true显示启用，false显示禁用
  sortBy: 'name' | 'date' | 'order';
  sortOrder: 'asc' | 'desc';
  // 世界书管理
  worldbooks: Worldbook[];
}

export const initialState: WorldbookStoreState = {
  currentEntry: null,
  currentWorldbook: null,
  entries: [],
  entriesTotal: 0,
  entriesPage: 1,
  entriesPageSize: 20,
  entriesHasMore: false,
  entryLoadingIds: [],
  entryUpdatingIds: [],
  entriesLoading: false,
  filterEnabled: null,
  importing: false,
  loading: false,
  searchQuery: '',
  selectedEntryIds: [],
  selectedWorldbookId: null,
  worldbooks: [],
  sidebarCollapsed: false,
  sortBy: 'order',
  sortOrder: 'asc',
};
