[{"filename": "settings.json", "type": "settings"}, {"filename": "themes/Dark Lite.json", "type": "theme"}, {"filename": "themes/Cappuccino.json", "type": "theme"}, {"filename": "themes/Celestial Macaron.json", "type": "theme"}, {"filename": "themes/Dark V 1.0.json", "type": "theme"}, {"filename": "themes/Azure.json", "type": "theme"}, {"filename": "backgrounds/__transparent.png", "type": "background"}, {"filename": "backgrounds/_black.jpg", "type": "background"}, {"filename": "backgrounds/_white.jpg", "type": "background"}, {"filename": "backgrounds/bedroom clean.jpg", "type": "background"}, {"filename": "backgrounds/bedroom cyberpunk.jpg", "type": "background"}, {"filename": "backgrounds/bedroom red.jpg", "type": "background"}, {"filename": "backgrounds/bedroom tatami.jpg", "type": "background"}, {"filename": "backgrounds/cityscape medieval market.jpg", "type": "background"}, {"filename": "backgrounds/cityscape medieval night.jpg", "type": "background"}, {"filename": "backgrounds/cityscape postapoc.jpg", "type": "background"}, {"filename": "backgrounds/forest treehouse fireworks air baloons (by kallmeflocc).jpg", "type": "background"}, {"filename": "backgrounds/japan classroom side.jpg", "type": "background"}, {"filename": "backgrounds/japan classroom.jpg", "type": "background"}, {"filename": "backgrounds/japan path cherry blossom.jpg", "type": "background"}, {"filename": "backgrounds/japan university.jpg", "type": "background"}, {"filename": "backgrounds/landscape autumn great tree.jpg", "type": "background"}, {"filename": "backgrounds/landscape beach day.png", "type": "background"}, {"filename": "backgrounds/landscape beach night.jpg", "type": "background"}, {"filename": "backgrounds/landscape mountain lake.jpg", "type": "background"}, {"filename": "backgrounds/landscape postapoc.jpg", "type": "background"}, {"filename": "backgrounds/landscape winter lake house.jpg", "type": "background"}, {"filename": "backgrounds/royal.jpg", "type": "background"}, {"filename": "backgrounds/tavern day.jpg", "type": "background"}, {"filename": "default_Seraphina.png", "type": "character"}, {"filename": "<PERSON><PERSON><PERSON>", "type": "sprites"}, {"filename": "Eldoria.json", "type": "world"}, {"filename": "user-default.png", "type": "avatar"}, {"filename": "Default_Comfy_Workflow.json", "type": "workflow"}, {"filename": "Char_Avatar_Comfy_Workflow.json", "type": "workflow"}, {"filename": "presets/kobold/Ace of Spades.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Basic Coherence.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Best Guess.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Coherent Creativity.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Deterministic.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Genesis.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Godlike.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Good Winds.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Liminal Drift.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Low Rider.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Luna <PERSON>.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Mayday.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Miro Bronze.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Miro Gold.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Miro Silver.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Ouroboros.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Pleasing Results.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Pro Writer.json", "type": "kobold_preset"}, {"filename": "presets/kobold/RecoveredRuins.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Space Alien.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Storywriter.json", "type": "kobold_preset"}, {"filename": "presets/kobold/TFS-with-Top-A.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Titanic.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Universal-Creative.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Universal-Light.json", "type": "kobold_preset"}, {"filename": "presets/kobold/Universal-Super-Creative.json", "type": "kobold_preset"}, {"filename": "presets/kobold/simple-proxy-for-tavern.json", "type": "kobold_preset"}, {"filename": "presets/novel/Asper-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/Blended-Coffee-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/Blook-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/Carefree-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/CosmicCube-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/Edgewise-Clio.json", "type": "novel_preset"}, {"filename": "presets/novel/Fresh-Coffee-Clio.json", "type": "novel_preset"}, {"filename": "presets/novel/Fresh-Coffee-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/Green-Active-Writer-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/Keelback-Clio.json", "type": "novel_preset"}, {"filename": "presets/novel/Long-Press-Clio.json", "type": "novel_preset"}, {"filename": "presets/novel/Pilotfish-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/Pro_Writer-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/Stelenes-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/Talker-<PERSON><PERSON>-Clio.json", "type": "novel_preset"}, {"filename": "presets/novel/Tea_Time-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/Tesseract-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/Vingt-Un-Clio.json", "type": "novel_preset"}, {"filename": "presets/novel/Writers-Daemon-Kayra.json", "type": "novel_preset"}, {"filename": "presets/novel/Erato-Dragonfruit.json", "type": "novel_preset"}, {"filename": "presets/novel/Erato-Golden Arrow.json", "type": "novel_preset"}, {"filename": "presets/novel/Erato-Shosetsu.json", "type": "novel_preset"}, {"filename": "presets/novel/Erato-Wilder.json", "type": "novel_preset"}, {"filename": "presets/novel/Erato-Zany Scribe.json", "type": "novel_preset"}, {"filename": "presets/textgen/Asterism.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Beam Search.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Big O.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Contrastive Search.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Default.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Deterministic.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Divine Intellect.json", "type": "textgen_preset"}, {"filename": "presets/textgen/<PERSON>d (Godlike).json", "type": "textgen_preset"}, {"filename": "presets/textgen/<PERSON>d (Liminal Drift).json", "type": "textgen_preset"}, {"filename": "presets/textgen/LLaMa-Precise.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Midnight Enigma.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Miro Bronze.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Miro Gold.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Miro Silver.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Mirostat.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Naive.json", "type": "textgen_preset"}, {"filename": "presets/textgen/NovelAI (Best Guess).json", "type": "textgen_preset"}, {"filename": "presets/textgen/NovelAI (Decadence).json", "type": "textgen_preset"}, {"filename": "presets/textgen/NovelAI (Genesis).json", "type": "textgen_preset"}, {"filename": "presets/textgen/NovelAI (Lycaenidae).json", "type": "textgen_preset"}, {"filename": "presets/textgen/NovelAI (Ouroboros).json", "type": "textgen_preset"}, {"filename": "presets/textgen/NovelAI (Pleasing Results).json", "type": "textgen_preset"}, {"filename": "presets/textgen/NovelAI (Sphinx Moth).json", "type": "textgen_preset"}, {"filename": "presets/textgen/NovelAI (Storywriter).json", "type": "textgen_preset"}, {"filename": "presets/textgen/Shortwave.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Simple-1.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Space Alien.json", "type": "textgen_preset"}, {"filename": "presets/textgen/StarChat.json", "type": "textgen_preset"}, {"filename": "presets/textgen/TFS-with-Top-A.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Titanic.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Universal-Creative.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Universal-Light.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Universal-Super-Creative.json", "type": "textgen_preset"}, {"filename": "presets/textgen/Yara.json", "type": "textgen_preset"}, {"filename": "presets/textgen/simple-proxy-for-tavern.json", "type": "textgen_preset"}, {"filename": "presets/openai/Default.json", "type": "openai_preset"}, {"filename": "presets/context/Adventure.json", "type": "context"}, {"filename": "presets/context/Alpaca-Single-Turn.json", "type": "context"}, {"filename": "presets/context/Alpaca.json", "type": "context"}, {"filename": "presets/context/ChatML.json", "type": "context"}, {"filename": "presets/context/Default.json", "type": "context"}, {"filename": "presets/context/DreamGen Role-Play V1 ChatML.json", "type": "context"}, {"filename": "presets/context/DreamGen Role-Play V1 Llama3.json", "type": "context"}, {"filename": "presets/context/Libra-32B.json", "type": "context"}, {"filename": "presets/context/Lightning 1.1.json", "type": "context"}, {"filename": "presets/context/Llama 2 Chat.json", "type": "context"}, {"filename": "presets/context/Minimalist.json", "type": "context"}, {"filename": "presets/context/NovelAI.json", "type": "context"}, {"filename": "presets/context/OldDefault.json", "type": "context"}, {"filename": "presets/context/Metharme.json", "type": "context"}, {"filename": "presets/context/Story.json", "type": "context"}, {"filename": "presets/context/Synthia.json", "type": "context"}, {"filename": "presets/context/simple-proxy-for-tavern.json", "type": "context"}, {"filename": "presets/context/Command R.json", "type": "context"}, {"filename": "presets/context/Llama 3 Instruct.json", "type": "context"}, {"filename": "presets/context/Llama 4 Instruct.json", "type": "context"}, {"filename": "presets/context/Phi.json", "type": "context"}, {"filename": "presets/instruct/Adventure.json", "type": "instruct"}, {"filename": "presets/instruct/Alpaca-Single-Turn.json", "type": "instruct"}, {"filename": "presets/instruct/Alpaca.json", "type": "instruct"}, {"filename": "presets/instruct/ChatML.json", "type": "instruct"}, {"filename": "presets/instruct/DreamGen Role-Play V1 ChatML.json", "type": "instruct"}, {"filename": "presets/instruct/DreamGen Role-Play V1 Llama3.json", "type": "instruct"}, {"filename": "presets/instruct/Koala.json", "type": "instruct"}, {"filename": "presets/instruct/Libra-32B.json", "type": "instruct"}, {"filename": "presets/instruct/Lightning 1.1.json", "type": "instruct"}, {"filename": "presets/instruct/Llama 2 Chat.json", "type": "instruct"}, {"filename": "presets/instruct/Metharme.json", "type": "instruct"}, {"filename": "presets/instruct/OpenOrca-OpenChat.json", "type": "instruct"}, {"filename": "presets/instruct/Story.json", "type": "instruct"}, {"filename": "presets/instruct/Synthia.json", "type": "instruct"}, {"filename": "presets/instruct/Tulu.json", "type": "instruct"}, {"filename": "presets/context/Tulu.json", "type": "context"}, {"filename": "presets/instruct/Vicuna 1.0.json", "type": "instruct"}, {"filename": "presets/instruct/Vicuna 1.1.json", "type": "instruct"}, {"filename": "presets/instruct/WizardLM-13B.json", "type": "instruct"}, {"filename": "presets/instruct/WizardLM.json", "type": "instruct"}, {"filename": "presets/instruct/simple-proxy-for-tavern.json", "type": "instruct"}, {"filename": "presets/instruct/Command R.json", "type": "instruct"}, {"filename": "presets/instruct/Llama 3 Instruct.json", "type": "instruct"}, {"filename": "presets/instruct/Llama 4 Instruct.json", "type": "instruct"}, {"filename": "presets/instruct/Phi.json", "type": "instruct"}, {"filename": "presets/moving-ui/Default.json", "type": "moving_ui"}, {"filename": "presets/quick-replies/Default.json", "type": "quick_replies"}, {"filename": "presets/instruct/Llama-3-Instruct-Names.json", "type": "instruct"}, {"filename": "presets/instruct/ChatML-Names.json", "type": "instruct"}, {"filename": "presets/context/Llama-3-Instruct-Names.json", "type": "context"}, {"filename": "presets/context/ChatML-Names.json", "type": "context"}, {"filename": "presets/context/Gemma 2.json", "type": "context"}, {"filename": "presets/instruct/Gemma 2.json", "type": "instruct"}, {"filename": "presets/sysprompt/Actor.json", "type": "sysprompt"}, {"filename": "presets/sysprompt/Assistant - Expert.json", "type": "sysprompt"}, {"filename": "presets/sysprompt/Assistant - Simple.json", "type": "sysprompt"}, {"filename": "presets/sysprompt/Chain of Thought.json", "type": "sysprompt"}, {"filename": "presets/sysprompt/Roleplay - Detailed.json", "type": "sysprompt"}, {"filename": "presets/sysprompt/Roleplay - Immersive.json", "type": "sysprompt"}, {"filename": "presets/sysprompt/Roleplay - Simple.json", "type": "sysprompt"}, {"filename": "presets/sysprompt/Text Adventure.json", "type": "sysprompt"}, {"filename": "presets/sysprompt/Writer - Creative.json", "type": "sysprompt"}, {"filename": "presets/sysprompt/Writer - Realistic.json", "type": "sysprompt"}, {"filename": "presets/sysprompt/Blank.json", "type": "sysprompt"}, {"filename": "presets/sysprompt/Neutral - Chat.json", "type": "sysprompt"}, {"filename": "presets/sysprompt/Lightning 1.1.json", "type": "sysprompt"}, {"filename": "presets/instruct/Mistral V1.json", "type": "instruct"}, {"filename": "presets/context/Mistral V1.json", "type": "context"}, {"filename": "presets/instruct/Mistral V2 & V3.json", "type": "instruct"}, {"filename": "presets/context/Mistral V2 & V3.json", "type": "context"}, {"filename": "presets/instruct/Mistral V3-Tekken.json", "type": "instruct"}, {"filename": "presets/context/Mistral V3-Tekken.json", "type": "context"}, {"filename": "presets/instruct/Mistral V7.json", "type": "instruct"}, {"filename": "presets/context/Mistral V7.json", "type": "context"}, {"filename": "presets/instruct/DeepSeek-V2.5.json", "type": "instruct"}, {"filename": "presets/context/DeepSeek-V2.5.json", "type": "context"}, {"filename": "presets/instruct/GLM-4.json", "type": "instruct"}, {"filename": "presets/context/GLM-4.json", "type": "context"}, {"filename": "presets/reasoning/DeepSeek.json", "type": "reasoning"}, {"filename": "presets/reasoning/Blank.json", "type": "reasoning"}, {"filename": "presets/instruct/Dots1.json", "type": "instruct"}, {"filename": "presets/context/Dots1.json", "type": "context"}]