import type { MessageRoleType } from '@/types/message';

/**
 * 次关键词逻辑枚举
 * 定义主关键词与次关键词的组合逻辑
 */
export enum SelectiveLogic {
  /** 主关键词 + 任一次关键词 */
  AND_ANY = 'and_any',
  /** 主关键词 + 非全部次关键词 */
  NOT_ALL = 'not_all',
  /** 主关键词 + 无任何次关键词 */
  NOT_ANY = 'not_any',
  /** 主关键词 + 全部次关键词 */
  AND_ALL = 'and_all',
}

/**
 * 激活模式枚举
 * 定义世界书条目的激活方式
 */
export enum ActivationMode {
  /** 关键字激活 (默认模式) */
  Keyword = 'keyword',
  /** 始终激活 */
  Constant = 'constant',
  /** 向量化激活 */
  Vectorized = 'vectorized',
}

/**
 * 世界书插入位置枚举
 * 定义条目内容在对话中的插入位置
 */
export enum WorldbookPosition {
  /** 角色定义前 */
  Before = 'before',
  /** 角色定义后 */
  After = 'after',
  /** 作者注释顶部 */
  AuthorNoteTop = 'author_note_top',
  /** 作者注释底部 */
  AuthorNoteBottom = 'author_note_bottom',
  /** 指定深度插入 */
  AtDepth = 'at_depth',
  /** 示例消息顶部 */
  ExampleMessageTop = 'example_message_top',
  /** 示例消息底部 */
  ExampleMessageBottom = 'example_message_bottom',
}

/**
 * 扫描状态枚举
 * 定义世界书扫描的状态
 */
export enum ScanState {
  /** 停止扫描 */
  None = 'none',
  /** 初始状态 */
  Initial = 'initial',
  /** 递归扫描触发 */
  Recursion = 'recursion',
  /** 最小激活深度偏移触发 */
  MinActivations = 'min_activations',
}

/**
 * 扩展提示类型枚举
 * 定义扩展提示的位置类型
 */
export enum ExtensionPromptType {
  /** 无位置 */
  None = 'none',
  /** 提示中 */
  InPrompt = 'in_prompt',
  /** 聊天中 */
  InChat = 'in_chat',
  /** 提示前 */
  BeforePrompt = 'before_prompt',
}

// 重新导出MessageRoleType，避免重复定义
export type { MessageRoleType };
