'use client';

import { Icon } from '@lobehub/ui';
import { createStyles } from 'antd-style';
import { BookPlus } from 'lucide-react';
import { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { useWorldbookStore } from '@/store/worldbook';

import EntryManager from './EntryManager';
import EntryModal from './EntryModal';
// 世界书相关组件已移至@menu布局，主页面只显示条目管理

const useStyles = createStyles(({ css, token }) => ({
  container: css`
    height: 100%;
    background: ${token.colorBgContainer};
    display: flex;
    flex-direction: column;
  `,
  emptyState: css`
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: ${token.colorTextTertiary};
    font-size: 16px;
    flex-direction: column;
    gap: 16px;
  `,
  emptyIcon: css`
    font-size: 48px;
    color: ${token.colorTextQuaternary};
  `,
}));

interface WorldbookManagerProps {
  title?: string;
  worldbookId?: string;
}

const WorldbookManager = memo<WorldbookManagerProps>(({ title, worldbookId }) => {
  const { t } = useTranslation('worldbook');
  const { styles } = useStyles();

  // Store状态
  const {
    currentWorldbook,
    worldbooks,
    loading,
    fetchWorldbooks,
    setCurrentWorldbook,
  } = useWorldbookStore();

  // 条目模态框状态 (世界书相关模态框已移至@menu布局)
  const [entryModalOpen, setEntryModalOpen] = useState(false);
  const [entryModalData, setEntryModalData] = useState<{
    worldbookId: string;
    entry?: any;
  } | null>(null);

  // 初始化数据
  useEffect(() => {
    fetchWorldbooks();
  }, [fetchWorldbooks]);

  // 处理URL中的worldbookId
  useEffect(() => {
    if (worldbookId && worldbooks.length > 0) {
      const worldbook = worldbooks.find(wb => wb.id === worldbookId);
      if (worldbook && currentWorldbook?.id !== worldbookId) {
        setCurrentWorldbook(worldbookId);
      }
    }
  }, [worldbookId, worldbooks, currentWorldbook, setCurrentWorldbook]);

  const selectedWorldbook = worldbookId
    ? worldbooks.find(wb => wb.id === worldbookId)
    : currentWorldbook;

  // 世界书相关操作已移至@menu布局

  const handleCreateEntry = (worldbookId: string) => {
    setEntryModalData({
      worldbookId,
    });
    setEntryModalOpen(true);
  };

  const handleEditEntry = (worldbookId: string, entry: any) => {
    setEntryModalData({
      worldbookId,
      entry,
    });
    setEntryModalOpen(true);
  };

  return (
    <div className={styles.container}>
      {/* 主内容 - 条目管理 (侧边栏已移至@menu布局) */}
      {selectedWorldbook ? (
        <EntryManager
          worldbookId={selectedWorldbook.id}
          onCreateEntry={handleCreateEntry}
          onEditEntry={handleEditEntry}
        />
      ) : (
        <div className={styles.emptyState}>
          <Icon icon={BookPlus} className={styles.emptyIcon} />
          <div>{t('selectWorldbook')}</div>
        </div>
      )}

      {/* 世界书相关模态框已移至@menu布局，只保留条目模态框 */}

      {entryModalData && (
        <EntryModal
          open={entryModalOpen}
          worldbookId={entryModalData.worldbookId}
          entry={entryModalData.entry}
          onClose={() => {
            setEntryModalOpen(false);
            setEntryModalData(null);
          }}
          onSuccess={() => {
            setEntryModalOpen(false);
            setEntryModalData(null);
          }}
        />
      )}
    </div>
  );
});

WorldbookManager.displayName = 'WorldbookManager';

export default WorldbookManager;
