import { lambdaClient } from '@/libs/trpc/client';
import type {
  ActivationStatus,
  CreateEntryData,
  CreateWorldbookData,
  ImportResultDetail,
  UpdateEntryData,
  UpdateWorldbookData,
  Worldbook,
  WorldbookEntry,
  WorldbookImportData,
} from '@/types/worldbook';

import type { IWorldbookService, PaginatedResponse, SearchParams } from './type';

export class ClientService implements IWorldbookService {
  // 世界书管理
  async getWorldbooks(): Promise<Worldbook[]> {
    return lambdaClient.worldbook.getWorldbooks.query();
  }

  async getWorldbook(id: string): Promise<Worldbook> {
    const result = await lambdaClient.worldbook.getWorldbookById.query({ id });
    if (!result) {
      throw new Error('Worldbook not found');
    }
    return result;
  }

  async createWorldbook(data: CreateWorldbookData): Promise<Worldbook> {
    const id = await lambdaClient.worldbook.createWorldbook.mutate(data);
    if (!id) {
      throw new Error('Failed to create worldbook');
    }
    return this.getWorldbook(id);
  }

  async updateWorldbook(id: string, data: UpdateWorldbookData): Promise<Worldbook> {
    await lambdaClient.worldbook.updateWorldbook.mutate({ id, value: data });
    return this.getWorldbook(id);
  }

  async deleteWorldbook(id: string): Promise<void> {
    await lambdaClient.worldbook.removeWorldbook.mutate({ id });
  }

  // 条目管理
  async getEntries(worldbookId: string, params?: SearchParams): Promise<PaginatedResponse<WorldbookEntry>> {
    // 调用支持搜索和分页的接口
    return lambdaClient.worldbookChunk.getChunksByWorldbookId.query({
      worldbookId,
      params: params || {}
    });
  }

  async getEntry(id: string): Promise<WorldbookEntry> {
    const result = await lambdaClient.worldbookChunk.getChunkById.query({ id });
    if (!result) {
      throw new Error('Entry not found');
    }
    return result;
  }

  async createEntry(worldbookId: string, data: CreateEntryData): Promise<WorldbookEntry> {
    return lambdaClient.worldbookChunk.createChunk.mutate({
      worldbookId,
      title: data.title,
      content: data.content,
      keys: data.keys,
      keysSecondary: data.keysSecondary,
      selectiveLogic: data.selectiveLogic,
      order: data.order,
      probability: data.probability,
      constant: data.constant,
      scanDepth: data.scanDepth,
      position: data.position,
      groupName: data.groupName,
      sticky: data.sticky,
      cooldown: data.cooldown,
      delay: data.delay,
      excludeRecursion: data.excludeRecursion,
      delayUntilRecursion: data.delayUntilRecursion,
      groupWeight: data.groupWeight,
      caseSensitive: data.caseSensitive,
      matchWholeWords: data.matchWholeWords,
      useRegex: data.useRegex,
    });
  }

  async updateEntry(id: string, data: UpdateEntryData): Promise<WorldbookEntry> {
    console.log('🌐 [Service] updateEntry called with:', { id, data });
    const result = await lambdaClient.worldbookChunk.updateChunk.mutate({ id, value: data });
    console.log('🌐 [Service] updateChunk API returned:', result);
    console.log('📝 [Service] Returned content:', result?.content);
    return result;
  }

  async deleteEntry(id: string): Promise<void> {
    await lambdaClient.worldbookChunk.removeChunk.mutate({ id });
  }

  // 批量操作
  async bulkUpdateEntries(ids: string[], data: Partial<WorldbookEntry>): Promise<void> {
    await lambdaClient.worldbookChunk.bulkUpdateChunks.mutate({ ids, value: data });
  }

  async bulkDeleteEntries(ids: string[]): Promise<void> {
    await lambdaClient.worldbookChunk.bulkDeleteChunks.mutate({ ids });
  }

  // 导入导出
  async importWorldbook(data: WorldbookImportData): Promise<ImportResultDetail> {
    return lambdaClient.worldbook.importWorldbook.mutate(data);
  }



  // 激活状态查询
  async getActivationStatus(worldbookId: string): Promise<ActivationStatus> {
    // 注意：当前tRPC实现中没有激活状态查询，这里返回默认值
    return {
      activeEntries: [],
      lastActivated: new Date().toISOString(),
      worldbookId,
    };
  }
}

// 导出单例实例
export const worldbookService = new ClientService();
