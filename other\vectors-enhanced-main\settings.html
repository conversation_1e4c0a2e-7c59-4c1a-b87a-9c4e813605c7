<div id="vectors_enhanced_container" class="inline-drawer">
  <div class="inline-drawer-toggle inline-drawer-header">
    <b>聊天记录超级管理器</b>
    <div class="inline-drawer-icon fa-solid fa-circle-chevron-down down"></div>
  </div>
  <div class="inline-drawer-content">
    <!-- 主开关 -->
    <div
      class="vectors-enhanced-section"
      style="padding-bottom: 0.5rem; border-bottom: 2px solid var(--SmartThemeQuoteColor)"
    >
      <label
        class="checkbox_label"
        for="vectors_master_enabled"
        title="启用/禁用整个插件的所有功能。禁用时，所有向量化和查询功能都将停止工作。"
      >
        <input id="vectors_enhanced_master_enabled" type="checkbox" />
        <span style="font-weight: bold; color: var(--SmartThemeQuoteColor)">启用聊天记录超级管理器</span>
      </label>
      <small style="display: block; margin-top: 0.25rem; color: var(--SmartThemeEmColor)">
        关闭此开关将禁用插件的所有功能，包括向量化、查询和任务管理
      </small>
    </div>

    <!-- 向量化设置部分 -->
    <div id="vectors_enhanced_main_settings" class="vectors-enhanced-section">
      <h3>向量化设置</h3>

      <details class="flex-container flexFlowColumn m-t-0-5">
        <summary><strong>向量化参数</strong></summary>

        <div class="flex-container flexFlowColumn m-t-0-5">
          <label for="vectors_source"> 向量化源 </label>
          <select id="vectors_enhanced_source" class="text_pole">
            <option value="transformers">本地 (Transformers)</option>
            <option value="vllm">vLLM</option>
            <option value="ollama">Ollama</option>
          </select>
        </div>

      <!-- vLLM 设置 -->
      <div id="vectors_enhanced_vllm_settings" class="flex-container flexFlowColumn" style="display: none">
        <label for="vectors_vllm_model"> 模型名称 </label>
        <input
          id="vectors_enhanced_vllm_model"
          class="text_pole"
          type="text"
          placeholder="例如：intfloat/e5-mistral-7b-instruct"
        />

        <label for="vectors_vllm_url"> API URL（可选） </label>
        <input id="vectors_enhanced_vllm_url" class="text_pole" type="text" placeholder="留空使用默认vLLM设置" />
        <small> 如果未设置，将使用API连接设置中的URL。 </small>
      </div>

      <!-- Ollama 设置 -->
      <div id="vectors_enhanced_ollama_settings" class="flex-container flexFlowColumn" style="display: none">
        <label for="vectors_enhanced_ollama_model">
          <span data-i18n="Ollama 模型">Ollama 模型</span>
        </label>
        <input
          id="vectors_enhanced_ollama_model"
          class="text_pole"
          placeholder="rjmalagon/gte-qwen2-1.5b-instruct-embed-f16"
        />

        <label for="vectors_enhanced_ollama_url">
          <span data-i18n="Ollama URL">Ollama URL</span>
        </label>
        <input
          id="vectors_enhanced_ollama_url"
          class="text_pole"
          placeholder="留空将使用默认地址 http://localhost:11434"
        />

        <label class="checkbox_label" for="vectors_enhanced_ollama_keep">
          <input id="vectors_enhanced_ollama_keep" type="checkbox" />
          <span data-i18n="保持模型在内存中">保持模型在内存中</span>
        </label>

        <small>
          模型必须先下载！使用 <code>ollama pull</code> 命令下载模型。<br />
          提示：在API连接设置中设置Ollama的URL。
        </small>
      </div>

      <!-- 本地设置 -->
      <div id="vectors_enhanced_local_settings" class="flex-container flexFlowColumn">
        <small> 使用本地Transformers模型进行嵌入 </small>
      </div>

      <!-- Transformers 设置 -->
      <div id="vectors_enhanced_transformers_settings" class="flex-container flexFlowColumn">
        <label for="vectors_enhanced_local_model">
          <span data-i18n="本地模型">本地模型</span>
        </label>
        <input id="vectors_enhanced_local_model" class="text_pole" placeholder="留空使用默认模型" />
      </div>

      <hr />

      <!-- 通用设置 -->
      <div class="flex-container">
        <div class="flex1" title="向量化的文本块大小">
          <label for="vectors_chunk_size">
            <small>块大小</small>
          </label>
          <input id="vectors_enhanced_chunk_size" type="number" class="text_pole" min="100" max="10000" />
        </div>
        <div class="flex1" title="块之间的重叠百分比">
          <label for="vectors_overlap_percent">
            <small>重叠 %</small>
          </label>
          <input id="vectors_enhanced_overlap_percent" type="number" class="text_pole" min="0" max="50" />
        </div>
        <div class="flex1" title="检索的最小相似度分数">
          <label for="vectors_score_threshold">
            <small>分数阈值</small>
          </label>
          <input id="vectors_enhanced_score_threshold" type="number" class="text_pole" min="0" max="1" step="0.05" />
        </div>
      </div>

      <div class="flex-container flexFlowColumn">
        <label for="vectors_force_chunk_delimiter" title="块边界的自定义分隔符">
          <small>自定义块分隔符</small>
        </label>
        <input id="vectors_enhanced_force_chunk_delimiter" class="text_pole" type="text" placeholder="（可选）" />
      </div>

      <!-- 查询设置 -->
      <div class="flex-container m-t-0-5">
        <div class="flex1" title="查询时使用最近几条消息的内容">
          <label for="vectors_query_messages">
            <small>查询消息数</small>
          </label>
          <input id="vectors_enhanced_query_messages" type="number" class="text_pole" min="1" max="20" />
        </div>
        <div class="flex1" title="返回的最大相关块数量（仍受分数阈值限制）">
          <label for="vectors_max_results">
            <small>最大结果数</small>
          </label>
          <input id="vectors_enhanced_max_results" type="number" class="text_pole" min="1" max="100" />
        </div>
      </div>

      <!-- 启用向量查询 -->
      <label class="checkbox_label" for="vectors_enabled" title="启用/禁用向量查询功能（已向量化的内容不会受影响）">
        <input id="vectors_enhanced_enabled" type="checkbox" />
        <span>启用向量查询</span>
      </label>

      <!-- 显示查询结果通知 -->
      <label class="checkbox_label" for="vectors_show_query_notification" title="每次向量查询后显示结果统计">
        <input id="vectors_enhanced_show_query_notification" type="checkbox" />
        <span>显示查询结果通知</span>
      </label>

      <!-- 详细通知选项 -->
      <div id="vectors_enhanced_notification_details" style="margin-left: 1.5rem; display: none;">
        <label class="checkbox_label" for="vectors_detailed_notification" title="显示各来源的详细分布（聊天记录、文件、世界信息）">
          <input id="vectors_enhanced_detailed_notification" type="checkbox" />
          <span>详细模式（显示来源分布）</span>
        </label>
      </div>
      </details>

      <hr />
<!-- Rerank 设置 -->
      <details>
        <summary><strong>Rerank 设置</strong></summary>
        <label class="checkbox_label" for="vectors_enhanced_rerank_enabled" title="启用/禁用Rerank模型对搜索结果进行重排序">
          <input id="vectors_enhanced_rerank_enabled" type="checkbox" />
          <span>启用 Rerank</span>
        </label>
        <label class="checkbox_label" for="vectors_enhanced_rerank_success_notify" title="在Rerank成功时显示通知">
          <input id="vectors_enhanced_rerank_success_notify" type="checkbox" />
          <span>显示 Rerank 成功通知</span>
        </label>
        <div class="flex-container flexFlowColumn m-t-0-5">
            <label for="vectors_enhanced_rerank_url">Rerank API URL</label>
            <input id="vectors_enhanced_rerank_url" class="text_pole" type="text" placeholder="例如：https://api.siliconflow.cn/v1/rerank">

            <label for="vectors_enhanced_rerank_apiKey">Rerank API Key</label>
            <input id="vectors_enhanced_rerank_apiKey" class="text_pole" type="password" placeholder="输入您的API Key">

            <label for="vectors_enhanced_rerank_model">Rerank 模型</label>
            <input id="vectors_enhanced_rerank_model" class="text_pole" type="text" placeholder="例如：Pro/BAAI/bge-reranker-v2-m3">

            <div class="flex-container">
              <div class="flex1" title="在Rerank前，从向量搜索中获取的候选项数量">
                  <label for="vectors_enhanced_rerank_top_n"><small>Rerank Top N</small></label>
                  <input id="vectors_enhanced_rerank_top_n" type="number" class="text_pole" min="1" max="100">
              </div>
              <div class="flex1" title="混合分数中Rerank分数的权重 (0-1)">
                  <label for="vectors_enhanced_rerank_hybrid_alpha"><small>混合权重</small></label>
                  <input id="vectors_enhanced_rerank_hybrid_alpha" type="number" class="text_pole" min="0" max="1" step="0.1">
              </div>
            </div>
        </div>
      </details>

      <hr />

      <!-- 注入设置 -->
      <details>
        <summary><strong>注入设置</strong></summary>

      <div class="flex-container flexFlowColumn">
        <label for="vectors_template"> 注入模板 </label>
        <textarea
          id="vectors_enhanced_template"
          class="text_pole textarea_compact"
          rows="3"
          placeholder="使用 {{text}} 指定检索内容的位置"
        ></textarea>
      </div>

      <!-- 内容标签设置 -->
      <div class="flex-container flexFlowColumn m-t-0-5">
        <label>
          <small>内容标签（用于区分不同来源的内容）</small>
        </label>
        <div class="flex-container">
          <div class="flex1">
            <label for="vectors_tag_chat">
              <small>聊天记录</small>
            </label>
            <input id="vectors_enhanced_tag_chat" class="text_pole" type="text" placeholder="past_chat" />
          </div>
          <div class="flex1">
            <label for="vectors_tag_wi">
              <small>世界信息</small>
            </label>
            <input id="vectors_enhanced_tag_wi" class="text_pole" type="text" placeholder="world_part" />
          </div>
          <div class="flex1">
            <label for="vectors_tag_file">
              <small>文件</small>
            </label>
            <input id="vectors_enhanced_tag_file" class="text_pole" type="text" placeholder="databank" />
          </div>
        </div>
      </div>

      <label>注入位置</label>
      <div class="radio_group">
        <label>
          <input type="radio" name="vectors_position" value="2" />
          <span>主提示前</span>
        </label>
        <label>
          <input type="radio" name="vectors_position" value="0" />
          <span>主提示后</span>
        </label>
        <label>
          <input type="radio" name="vectors_position" value="1" />
          <span>聊天内@深度</span>
          <input
            id="vectors_enhanced_depth"
            class="text_pole widthUnset"
            type="number"
            min="0"
            max="999"
            style="width: 60px"
          />
          <span>作为</span>
          <select id="vectors_enhanced_depth_role" class="text_pole widthNatural">
            <option value="0">系统</option>
            <option value="1">用户</option>
            <option value="2">助手</option>
          </select>
        </label>
      </div>

      <label class="checkbox_label" for="vectors_include_wi">
        <input id="vectors_enhanced_include_wi" type="checkbox" />
        <span>包含在世界信息扫描中</span>
      </label>

      <label
        class="checkbox_label"
        for="vectors_auto_vectorize"
        title="当聊天内容发生变化时（如发送/接收消息、编辑消息等），自动重新向量化选中的内容。关闭此选项需要手动点击'向量化'按钮。"
      >
        <input id="vectors_enhanced_auto_vectorize" type="checkbox" />
        <span>更改时自动向量化</span>
      </label>
      </details>
    </div>

    <hr class="m-t-1 m-b-1" />

    <!-- 内容选择部分 -->
    <div id="vectors_enhanced_content_settings" class="vectors-enhanced-section">
      <h3>内容选择</h3>

      <!-- 聊天消息 -->
      <div class="content-type-section">
        <label class="checkbox_label" for="vectors_chat_enabled">
          <input id="vectors_enhanced_chat_enabled" type="checkbox" />
          <span>聊天消息</span>
        </label>

        <div id="vectors_enhanced_chat_settings" class="content-settings" style="display: none">
          <div class="flex-container">
            <div class="flex1">
              <label for="vectors_chat_start">
                <small>从消息 #</small>
              </label>
              <input id="vectors_enhanced_chat_start" type="number" class="text_pole" min="0" value="0" />
            </div>
            <div class="flex1">
              <label for="vectors_chat_end">
                <small>到消息 # (-1 表示最后)</small>
              </label>
              <input id="vectors_enhanced_chat_end" type="number" class="text_pole" value="-1" />
            </div>
          </div>

          <div class="flex-container flexFlowColumn m-t-0-5">
            <label>
              <small>消息类型</small>
            </label>
            <label class="checkbox_label">
              <input id="vectors_enhanced_chat_user" type="checkbox" checked />
              <span>用户消息</span>
            </label>
            <label class="checkbox_label">
              <input id="vectors_enhanced_chat_assistant" type="checkbox" checked />
              <span>AI消息</span>
            </label>
            <label class="checkbox_label">
              <input id="vectors_enhanced_chat_include_hidden" type="checkbox" />
              <span>包含隐藏消息</span>
            </label>
          </div>

          <!-- 隐藏消息管理 -->
          <div
            class="flex-container flexFlowColumn m-t-1"
            style="border-top: 1px dashed var(--SmartThemeQuoteColor); padding-top: 0.5rem"
          >
            <label class="vectors-enhanced-subsection-title">
              <small>隐藏消息管理</small>
            </label>

            <small class="text-muted margin-bottom-10px"> 勾选"包含隐藏消息"后，隐藏的消息也会被包含在向量化中 </small>

            <!-- 隐藏/显示按钮 -->
            <div class="flex-container m-t-0-5">
              <button id="vectors_enhanced_hide_range" class="menu_button menu_button_icon" title="隐藏选定范围的消息">
                <i class="fa-solid fa-eye-slash"></i>
                <span>隐藏消息</span>
              </button>
              <button
                id="vectors_enhanced_unhide_range"
                class="menu_button menu_button_icon"
                title="显示选定范围的消息"
              >
                <i class="fa-solid fa-eye"></i>
                <span>显示消息</span>
              </button>
              <button id="vectors_enhanced_show_hidden" class="menu_button menu_button_icon" title="查看当前隐藏的消息">
                <i class="fa-solid fa-list"></i>
                <span>查看隐藏</span>
              </button>
            </div>

            <!-- 隐藏消息信息显示 -->
            <div id="vectors_enhanced_hidden_info" class="m-t-0-5" style="display: none">
              <div class="text-muted">
                <strong>当前隐藏消息: <span id="vectors_enhanced_hidden_count">0</span></strong>
              </div>
            </div>
          </div>

          <div class="flex-container flexFlowColumn m-t-0-5">
            <div class="flex-container alignItemsCenter">
              <label class="flex1 vectors-enhanced-subsection-title">
                <small>标签提取规则</small>
              </label>
              <button id="vectors_enhanced_tag_examples" class="menu_button menu_button_icon" title="查看标签提取功能的使用方法和示例" style="padding: 0.2rem 0.4rem; font-size: 0.8em;">
                  <i class="fa-solid fa-lightbulb"></i>
                  <span>查看示例</span>
              </button>
            </div>
            <div id="vectors_enhanced_rules_editor">
              <!-- 规则编辑器将在这里动态生成 -->
            </div>
            <div class="flex-container m-t-0-5" style="gap: 0.5rem;">
                <button id="vectors_enhanced_add_rule" class="menu_button menu_button_icon">
                    <i class="fa-solid fa-plus"></i>
                    <span>添加新规则</span>
                </button>
                <button id="vectors_enhanced_tag_scanner" class="menu_button menu_button_icon" title="扫描当前选择的内容中存在的标签以作为规则建议">
                    <i class="fa-solid fa-search"></i>
                    <span>扫描标签</span>
                </button>
                <button id="vectors_enhanced_exclude_cot" class="menu_button menu_button_icon" title="快速添加一条正则规则，用于排除HTML注释格式的思维链（CoT）">
                    <i class="fa-solid fa-comment-slash"></i>
                    <span>排除小CoT</span>
                </button>
            </div>
            <!-- 标签建议区域 -->
            <div id="vectors_enhanced_tag_suggestions" class="m-t-0-5" style="display: none;">
              <div class="flex-container alignItemsCenter m-b-0-5">
                <small class="text-muted flex1">
                  <strong>发现的标签 (<span id="vectors_tag_scan_stats"></span>):</strong>
                </small>
                <button id="vectors_enhanced_clear_suggestions" class="menu_button menu_button_icon fa-solid fa-times" title="清除建议"></button>
              </div>
              <div id="vectors_enhanced_tag_list">
                <!-- 标签建议将在这里动态生成 -->
              </div>
            </div>
          </div>

          <!-- 内容过滤黑名单 -->
          <div class="flex-container flexFlowColumn m-t-0-5">
            <label for="vectors_content_blacklist" class="vectors-enhanced-subsection-title">
              <small>内容过滤黑名单（一行一个关键词或短语）</small>
            </label>
            <textarea
              id="vectors_enhanced_content_blacklist"
              class="text_pole textarea_compact"
              rows="3"
              placeholder="Step&#10;思考过程&#10;推理步骤&#10;让我想想"
            ></textarea>
            <details class="text-muted margin-bottom-10px">
              <summary><small><strong>黑名单功能</strong></small></summary>
              <div><small>• 包含这些关键词的提取内容将被跳过</small></div>
              <div><small>• 支持中英文关键词</small></div>
              <div><small>• 与排除语法配合使用：先排除子标签，再进行黑名单过滤</small></div>
              <div><small>处理顺序：提取标签 → 排除子标签 → 黑名单过滤</small></div>
            </details>
          </div>

        </div>
      </div>

      <hr class="m-t-0-5 m-b-0-5" />

      <!-- 文件 -->
      <div class="content-type-section">
        <label class="checkbox_label" for="vectors_files_enabled">
          <input id="vectors_enhanced_files_enabled" type="checkbox" />
          <span>文件</span>
        </label>

        <div id="vectors_enhanced_files_settings" class="content-settings" style="display: none">
          <div id="vectors_enhanced_files_list" class="file-list">
            <!-- 文件列表将动态填充 -->
          </div>
          <button id="vectors_enhanced_files_refresh" class="menu_button">
            <i class="fa-solid fa-refresh"></i>
            <span>刷新文件列表</span>
          </button>
        </div>
      </div>

      <hr class="m-t-0-5 m-b-0-5" />

      <!-- 世界信息 -->
      <div class="content-type-section">
        <label class="checkbox_label" for="vectors_wi_enabled">
          <input id="vectors_enhanced_wi_enabled" type="checkbox" />
          <span>世界信息</span>
        </label>

        <div id="vectors_enhanced_wi_settings" class="content-settings" style="display: none">
          <div id="vectors_enhanced_wi_list" class="wi-list">
            <!-- 世界信息条目将动态填充 -->
          </div>
          <button id="vectors_enhanced_wi_refresh" class="menu_button">
            <i class="fa-solid fa-refresh"></i>
            <span>刷新世界信息</span>
          </button>
        </div>
      </div>

      <hr class="m-t-1 m-b-1" />

      <!-- 向量化任务列表 -->
      <div id="vectors_enhanced_tasks_settings" class="vectors-enhanced-section">
        <h3>向量化任务</h3>
        <div id="vectors_enhanced_task_list" class="task-list">
          <!-- 任务列表将动态填充 -->
        </div>
      </div>

      <hr class="m-t-1 m-b-1" />

      <!-- 操作按钮 -->
      <div id="vectors_enhanced_actions_settings" class="vectors-enhanced-section">
        <div class="vectors-enhanced-actions flex-container">
          <button id="vectors_enhanced_preview" class="menu_button menu_button_icon">
            <i class="fa-solid fa-eye"></i>
            <span>预览</span>
          </button>
          <button id="vectors_enhanced_export" class="menu_button menu_button_icon">
            <i class="fa-solid fa-download"></i>
            <span>导出</span>
          </button>
          <button id="vectors_enhanced_vectorize" class="menu_button menu_button_icon">
            <i class="fa-solid fa-vector-square"></i>
            <span>向量化</span>
          </button>
          <button id="vectors_enhanced_abort" class="menu_button menu_button_icon" title="中断向量化" style="display: none;">
            <i class="fa-solid fa-stop"></i>
            <span>中断</span>
          </button>
        </div>

        <div id="vectors_enhanced_progress" class="vectors-enhanced-progress m-t-1" style="display: none">
          <div class="progress-bar">
            <div class="progress-bar-inner" style="width: 0%"></div>
          </div>
          <div class="progress-text">准备中...</div>
        </div>

        <div id="vectors_enhanced_status" class="vectors-enhanced-status m-t-1" style="display: none">
          <!-- 状态消息将在此显示 -->
        </div>

        <!-- 调试按钮区域 -->
        <!-- <div class="vectors-enhanced-debug m-t-1">
          <h4>调试工具</h4>
          <div class="flex-container">
            <button id="vectors_debug_world_info" class="menu_button menu_button_icon" title="深度分析世界信息状态">
              <i class="fa-solid fa-magnifying-glass"></i>
              <span>世界信息调试</span>
            </button>
            <button id="vectors_debug_ui_sync" class="menu_button menu_button_icon" title="检查UI与设置同步状态">
              <i class="fa-solid fa-sync"></i>
              <span>UI同步检查</span>
            </button>
            <button id="vectors_cleanup_selections" class="menu_button menu_button_icon" title="清理无效选择">
              <i class="fa-solid fa-broom"></i>
              <span>清理选择</span>
            </button>
          </div>
          <div class="flex-container m-t-0-5">
            <button id="vectors_debug_content" class="menu_button menu_button_icon" title="显示当前内容选择状态">
              <i class="fa-solid fa-list"></i>
              <span>内容选择状态</span>
            </button>
            <button id="vectors_debug_file_overlap" class="menu_button menu_button_icon" title="分析文件重复检测">
              <i class="fa-solid fa-files"></i>
              <span>文件重复分析</span>
            </button>
            <button id="vectors_clear_world_info" class="menu_button menu_button_icon" title="清空所有世界信息选择">
              <i class="fa-solid fa-trash"></i>
              <span>清空世界信息</span>
            </button>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</div>
