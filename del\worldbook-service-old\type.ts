import type {
  ActivationStatus,
  CreateEntryData,
  CreateWorldbookData,
  ExportOptions,
  ImportResult,
  UpdateEntryData,
  UpdateWorldbookData,
  Worldbook,
  WorldbookEntry,
} from '@/types/worldbook';

/**
 * 世界书服务接口定义
 */
export interface IWorldbookService {
  // 世界书管理
  getWorldbooks(): Promise<Worldbook[]>;
  getWorldbook(id: string): Promise<Worldbook>;
  createWorldbook(data: CreateWorldbookData): Promise<Worldbook>;
  updateWorldbook(id: string, data: UpdateWorldbookData): Promise<Worldbook>;
  deleteWorldbook(id: string): Promise<void>;

  // 条目管理
  getEntries(worldbookId: string, params?: SearchParams): Promise<PaginatedResponse<WorldbookEntry>>;
  getEntry(id: string): Promise<WorldbookEntry>;
  createEntry(worldbookId: string, data: CreateEntryData): Promise<WorldbookEntry>;
  updateEntry(id: string, data: UpdateEntryData): Promise<WorldbookEntry>;
  deleteEntry(id: string): Promise<void>;

  // 批量操作
  bulkUpdateEntries(ids: string[], data: Partial<WorldbookEntry>): Promise<void>;
  bulkDeleteEntries(ids: string[]): Promise<void>;

  // 导入导出
  importWorldbook(worldbookId: string, data: any): Promise<ImportResult>;
  exportWorldbook(id: string, options: ExportOptions): Promise<any>;

  // 激活状态查询
  getActivationStatus(worldbookId: string): Promise<ActivationStatus>;
}

/**
 * API 响应类型
 */
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
}

/**
 * 分页响应类型
 */
export interface PaginatedResponse<T> {
  hasMore: boolean;
  items: T[];
  page: number;
  pageSize: number;
  total: number;
}

/**
 * 搜索参数类型
 */
export interface SearchParams {
  // 基础分页
  page?: number;
  pageSize?: number;

  // 搜索条件
  query?: string;

  // 过滤条件
  enabled?: boolean;
  constant?: boolean;
  hasKeys?: boolean;
  hasSecondaryKeys?: boolean;
  groupName?: string;
  selectiveLogic?: string;

  // 范围过滤
  orderMin?: number;
  orderMax?: number;
  probabilityMin?: number;
  probabilityMax?: number;

  // 排序
  sortBy?: 'name' | 'date' | 'order' | 'probability';
  sortOrder?: 'asc' | 'desc';
}
