@startuml SillyTavern世界书类图和数据结构
!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontSize 10

title SillyTavern 世界书类图和数据结构关系

package "Core Classes" {
    
    class WorldInfoBuffer {
        - depthBuffer: string[]
        - recurseBuffer: string[]
        - injectBuffer: string[]
        - globalScanData: WIGlobalScanData
        - skew: number
        - startDepth: number
        + {static} externalActivations: Map<string, object>
        --
        + constructor(messages: string[], globalScanData: WIGlobalScanData)
        + get(entry: WIScanEntry, scanState: number): string
        + matchKeys(haystack: string, needle: string, entry: WIScanEntry): boolean
        + addRecurse(message: string): void
        + addInject(message: string): void
        + getExternallyActivated(entry: WIScanEntry): object|undefined
        + resetExternalEffects(): void
        + advanceScan(): void
        + getDepth(): number
        + hasRecurse(): boolean
        - initDepthBuffer(messages: string[]): void
        - transformString(str: string, entry: WIScanEntry): string
    }
    
    class WorldInfoTimedEffects {
        - chat: string[]
        - entries: WIScanEntry[]
        - isDryRun: boolean
        - buffer: Record<TimedEffectType, WIScanEntry[]>
        - onEnded: Record<TimedEffectType, Function>
        --
        + constructor(chat: string[], entries: WIScanEntry[], isDryRun: boolean)
        + checkTimedEffects(): void
        + isEffectActive(effectType: TimedEffectType, entry: WIScanEntry): boolean
        + setTimedEffects(activatedEntries: WIScanEntry[]): void
        + cleanUp(): void
        - ensureChatMetadata(): void
        - getEntryKey(entry: WIScanEntry): string
        - getEntryTimedEffect(effectType: TimedEffectType, entry: WIScanEntry, force: boolean): TimedEffect
        - isEntryTimedEffectActive(effectType: TimedEffectType, entry: WIScanEntry): boolean
    }
    
    class VectorExtension {
        - settings: VectorSettings
        --
        + activateWorldInfo(chat: string[]): Promise<void>
        + getSortedEntries(): Promise<WIScanEntry[]>
        + queryMultipleCollections(collectionIds: string[], queryText: string, topK: number, threshold: number): Promise<QueryResult>
        + insertVectorItems(collectionId: string, items: VectorItem[]): Promise<void>
        + deleteVectorItems(collectionId: string, hashes: number[]): Promise<void>
        + getQueryText(chat: string[], initiator: string): Promise<string>
        - groupEntriesByWorld(entries: WIScanEntry[]): Record<string, WIScanEntry[]>
        - synchronizeCollections(groupedEntries: Record<string, WIScanEntry[]>): Promise<string[]>
    }
}

package "Data Structures" {
    
    class WIScanEntry {
        + uid: string
        + world: string
        + key: string[]
        + keysecondary: string[]
        + comment: string
        + content: string
        + constant: boolean
        + vectorized: boolean
        + selective: boolean
        + selectiveLogic: number
        + position: number
        + order: number
        + depth: number
        + role: number
        + disable: boolean
        + caseSensitive: boolean
        + matchWholeWords: boolean
        + useGroupScoring: boolean
        + scanDepth: number
        + matchPersonaDescription: boolean
        + matchCharacterDescription: boolean
        + matchCharacterPersonality: boolean
        + matchCharacterDepthPrompt: boolean
        + matchScenario: boolean
        + matchCreatorNotes: boolean
        + probability: number
        + useProbability: boolean
        + sticky: number
        + cooldown: number
        + delay: number
        + excludeRecursion: boolean
        + preventRecursion: boolean
        + delayUntilRecursion: number|boolean
        + group: string
        + groupOverride: boolean
        + groupWeight: number
        + characterFilter: CharacterFilter
        + decorators: string[]
        + addMemo: boolean
        + automationId: string
    }
    
    class WIGlobalScanData {
        + personaDescription: string
        + characterDescription: string
        + characterPersonality: string
        + characterDepthPrompt: string
        + scenario: string
        + creatorNotes: string
    }
    
    class CharacterFilter {
        + names: string[]
        + tags: string[]
        + isExclude: boolean
    }
    
    class TimedEffect {
        + start: number
        + end: number
        + protected: boolean
    }
    
    class VectorSettings {
        + enabled_world_info: boolean
        + enabled_for_all: boolean
        + max_entries: number
        + score_threshold: number
        + source: string
        + include_wi: boolean
    }
    
    class VectorItem {
        + hash: number
        + text: string
        + index: string|number
    }
    
    class QueryResult {
        + hashes: number[]
        + metadata: object[]
    }
}

package "Enums" {
    
    enum ScanState {
        INITIAL = 0
        RECURSION = 1
        MIN_ACTIVATIONS = 2
        NONE = 3
    }
    
    enum WorldInfoPosition {
        before = 0
        after = 1
        ANTop = 2
        ANBottom = 3
        atDepth = 4
        EMTop = 5
        EMBottom = 6
    }
    
    enum SelectiveLogic {
        AND_ANY = 0
        NOT_ALL = 1
        NOT_ANY = 2
        AND_ALL = 3
    }
    
    enum ExtensionPromptRoles {
        SYSTEM = 0
        USER = 1
        ASSISTANT = 2
    }
    
    enum TimedEffectType {
        sticky
        cooldown
        delay
    }
}

package "Global Configuration" {
    
    class WorldInfoConfig {
        + world_info_depth: number
        + world_info_recursive: boolean
        + world_info_max_recursion_steps: number
        + world_info_include_names: boolean
        + world_info_min_activations: number
        + world_info_min_activations_depth_max: number
        + world_info_budget: number
        + world_info_budget_cap: number
        + world_info_overflow_alert: boolean
        + world_info_case_sensitive: boolean
        + world_info_match_whole_words: boolean
        + world_info_use_group_scoring: boolean
        + world_info_character_strategy: number
    }
}

package "Result Types" {
    
    class WIActivated {
        + worldInfoBefore: string
        + worldInfoAfter: string
        + WIDepthEntries: DepthEntry[]
        + EMEntries: ExampleEntry[]
        + ANBeforeEntries: string[]
        + ANAfterEntries: string[]
        + allActivatedEntries: Set<WIScanEntry>
    }
    
    class DepthEntry {
        + depth: number
        + entries: string[]
        + role: number
    }
    
    class ExampleEntry {
        + position: string
        + content: string
    }
    
    class WIPromptResult {
        + worldInfoString: string
        + worldInfoBefore: string
        + worldInfoAfter: string
        + worldInfoExamples: ExampleEntry[]
        + worldInfoDepth: DepthEntry[]
        + anBefore: string[]
        + anAfter: string[]
    }
}

' Relationships
WorldInfoBuffer ||--|| WIGlobalScanData : contains
WorldInfoTimedEffects ||--o{ WIScanEntry : manages
WIScanEntry ||--|| CharacterFilter : has
WorldInfoTimedEffects ||--o{ TimedEffect : creates
VectorExtension ||--|| VectorSettings : uses
VectorExtension ||--o{ VectorItem : processes
VectorExtension ||--o{ QueryResult : returns

WIScanEntry ||--|| WorldInfoPosition : uses
WIScanEntry ||--|| SelectiveLogic : uses
WIScanEntry ||--|| ExtensionPromptRoles : uses
WorldInfoTimedEffects ||--|| TimedEffectType : uses

WIActivated ||--o{ DepthEntry : contains
WIActivated ||--o{ ExampleEntry : contains
WIActivated ||--o{ WIScanEntry : references

WIPromptResult ||--o{ DepthEntry : contains
WIPromptResult ||--o{ ExampleEntry : contains

@enduml
