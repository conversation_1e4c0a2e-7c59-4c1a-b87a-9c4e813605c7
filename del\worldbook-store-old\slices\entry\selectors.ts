import type { WorldbookStore } from '../../store';

// Entry Manager selectors
export const entryManagerSelectors = {
  // 基础状态
  entries: (s: WorldbookStore) => s.entries,
  entriesLoading: (s: WorldbookStore) => s.entriesLoading,
  
  // 当前选中的条目
  currentEntry: (s: WorldbookStore) => s.currentEntry,
  selectedEntryIds: (s: WorldbookStore) => s.selectedEntryIds,
  
  // 计算选择器
  entriesByWorldbookId: (worldbookId: string) => (s: WorldbookStore) =>
    s.entries.filter(entry => entry.worldbookId === worldbookId),
  
  enabledEntries: (s: WorldbookStore) => 
    s.entries.filter(entry => entry.enabled),
  
  enabledEntriesByWorldbookId: (worldbookId: string) => (s: WorldbookStore) =>
    s.entries.filter(entry => entry.worldbookId === worldbookId && entry.enabled),
  
  entryById: (id: string) => (s: WorldbookStore) =>
    s.entries.find(entry => entry.id === id),
  
  selectedEntries: (s: WorldbookStore) =>
    s.entries.filter(entry => s.selectedEntryIds.includes(entry.id)),
  
  // 统计信息
  entryCount: (s: WorldbookStore) => s.entries.length,
  
  entryCountByWorldbookId: (worldbookId: string) => (s: WorldbookStore) =>
    s.entries.filter(entry => entry.worldbookId === worldbookId).length,
  
  enabledEntryCount: (s: WorldbookStore) => 
    s.entries.filter(entry => entry.enabled).length,
  
  enabledEntryCountByWorldbookId: (worldbookId: string) => (s: WorldbookStore) =>
    s.entries.filter(entry => entry.worldbookId === worldbookId && entry.enabled).length,
  
  selectedEntryCount: (s: WorldbookStore) => s.selectedEntryIds.length,
  
  // 状态检查
  hasEntries: (s: WorldbookStore) => s.entries.length > 0,
  
  hasEntriesInWorldbook: (worldbookId: string) => (s: WorldbookStore) =>
    s.entries.some(entry => entry.worldbookId === worldbookId),
  
  hasEnabledEntries: (s: WorldbookStore) => 
    s.entries.some(entry => entry.enabled),
  
  hasEnabledEntriesInWorldbook: (worldbookId: string) => (s: WorldbookStore) =>
    s.entries.some(entry => entry.worldbookId === worldbookId && entry.enabled),
  
  hasSelectedEntries: (s: WorldbookStore) => s.selectedEntryIds.length > 0,
  
  isAllEntriesSelected: (worldbookId?: string) => (s: WorldbookStore) => {
    const relevantEntries = worldbookId 
      ? s.entries.filter(entry => entry.worldbookId === worldbookId)
      : s.entries;
    
    return relevantEntries.length > 0 && 
           relevantEntries.every(entry => s.selectedEntryIds.includes(entry.id));
  },
  
  isSomeEntriesSelected: (worldbookId?: string) => (s: WorldbookStore) => {
    const relevantEntries = worldbookId 
      ? s.entries.filter(entry => entry.worldbookId === worldbookId)
      : s.entries;
    
    return relevantEntries.some(entry => s.selectedEntryIds.includes(entry.id));
  },
  
  // 搜索和过滤
  entriesByKeys: (keys: string[]) => (s: WorldbookStore) =>
    s.entries.filter(entry => 
      entry.keys?.some(key => 
        keys.some(searchKey => 
          key.toLowerCase().includes(searchKey.toLowerCase())
        )
      )
    ),
  
  entriesByContent: (searchTerm: string) => (s: WorldbookStore) => {
    const term = searchTerm.toLowerCase();
    return s.entries.filter(entry => 
      entry.title?.toLowerCase().includes(term) ||
      entry.content?.toLowerCase().includes(term) ||
      entry.keys?.some(key => key.toLowerCase().includes(term)) ||
      entry.keysSecondary?.some(key => key.toLowerCase().includes(term))
    );
  },
  
  constantEntries: (s: WorldbookStore) => 
    s.entries.filter(entry => entry.constant),
  
  constantEntriesByWorldbookId: (worldbookId: string) => (s: WorldbookStore) =>
    s.entries.filter(entry => entry.worldbookId === worldbookId && entry.constant),
};
