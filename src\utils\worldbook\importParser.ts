import type { 
  CreateEntryData, 
  CreateWorldbookData, 
  SelectiveLogic, 
  SillyTavernEntry, 
  SillyTavernWorldbook, 
  WorldbookImportData 
} from '@/types/worldbook';

/**
 * 世界书导入格式解析器
 * 支持项目标准格式和SillyTavern格式的兼容导入
 */

// 格式检测结果
export interface FormatDetectionResult {
  error?: string;
  format: 'project' | 'sillytavern' | 'unknown';
  isValid: boolean;
}

/**
 * 检测文件格式类型
 */
/**
 * 检测是否为项目标准格式
 */
function isProjectFormat(data: any): boolean {
  return (
    data.worldbook &&
    typeof data.worldbook === 'object' &&
    typeof data.worldbook.name === 'string' &&
    Array.isArray(data.entries) &&
    data.entries.every((entry: any) =>
      typeof entry === 'object' &&
      typeof entry.title === 'string' &&
      typeof entry.content === 'string' &&
      Array.isArray(entry.keys)
    )
  );
}

/**
 * 检测是否为SillyTavern格式
 */
function isSillyTavernFormat(data: any): boolean {
  // SillyTavern格式特征：
  // 1. 有entries对象（不是数组）
  // 2. entries的值有key和content字段
  // 3. 可能有name、description等世界书字段
  if (!data.entries || typeof data.entries !== 'object' || Array.isArray(data.entries)) {
    return false;
  }

  const entryKeys = Object.keys(data.entries);
  if (entryKeys.length === 0) {
    return false;
  }

  // 检查前几个条目的格式
  const sampleEntries = entryKeys.slice(0, 3).map(key => data.entries[key]);
  return sampleEntries.every((entry: any) =>
    typeof entry === 'object' &&
    (Array.isArray(entry.key) || typeof entry.key === 'string') &&
    typeof entry.content === 'string'
  );
}



export function detectFormat(data: any): FormatDetectionResult {
  if (!data || typeof data !== 'object') {
    return {
      error: '文件格式无效，请确保上传的是有效的JSON文件',
      format: 'unknown',
      isValid: false
    };
  }

  // 检测项目标准格式
  if (isProjectFormat(data)) {
    return { format: 'project', isValid: true };
  }

  // 检测SillyTavern格式
  if (isSillyTavernFormat(data)) {
    return { format: 'sillytavern', isValid: true };
  }

  return {
    error: '未解析出可导入的世界书信息。支持的格式：项目标准格式、SillyTavern格式',
    format: 'unknown',
    isValid: false
  };
}



/**
 * 解析导入数据，统一转换为项目标准格式
 */
export function parseImportData(
  data: any, 
  fileName?: string
): WorldbookImportData {
  const detection = detectFormat(data);
  
  if (!detection.isValid) {
    throw new Error(detection.error || 'Invalid format');
  }

  switch (detection.format) {
    case 'project': {
      return parseProjectFormat(data, fileName);
    }
    case 'sillytavern': {
      return parseSillyTavernFormat(data, fileName);
    }
    default: {
      throw new Error('未解析出可导入的世界书信息');
    }
  }
}

/**
 * 解析项目标准格式
 */
function parseProjectFormat(data: any, fileName?: string): WorldbookImportData {
  // 验证数据完整性
  if (!data.worldbook || !Array.isArray(data.entries)) {
    throw new Error('项目格式无效：缺少世界书信息或条目数据');
  }

  // 验证世界书数据
  const worldbook: CreateWorldbookData = {
    description: data.worldbook.description || '',
    name: data.worldbook.name || fileName?.replace(/\.(json|worldbook)$/i, '') || 'Imported Worldbook',
  };

  // 验证和转换条目数据
  const entries: CreateEntryData[] = data.entries.map((entry: any, index: number) => {
    try {
      return validateAndConvertEntry(entry);
    } catch (error) {
      throw new Error(`第 ${index + 1} 个条目格式无效: ${(error as Error).message}`);
    }
  });

  return {
    entries,
    version: data.version || '1.0',
    worldbook,
  };
}



/**
 * 解析SillyTavern格式并转换为项目格式
 */
function parseSillyTavernFormat(data: SillyTavernWorldbook, fileName?: string): WorldbookImportData {
  const entries = data.entries || {};
  
  // 创建世界书信息
  const worldbook: CreateWorldbookData = {
    description: data.description || '',
    name: data.name || fileName?.replace(/\.(json|worldbook)$/i, '') || 'Imported Worldbook',
  };

  // 转换条目
  const convertedEntries: CreateEntryData[] = [];
  
  for (const [entryId, entry] of Object.entries(entries)) {
    try {
      const convertedEntry = convertSillyTavernEntry(entry, entryId);
      convertedEntries.push(convertedEntry);
    } catch (error) {
      console.warn(`Failed to convert SillyTavern entry ${entryId}:`, error);
      // 继续处理其他条目，不中断整个导入过程
    }
  }

  if (convertedEntries.length === 0) {
    throw new Error('No valid entries found in SillyTavern format');
  }

  return {
    entries: convertedEntries,
    version: '1.0',
    worldbook,
  };
}

/**
 * 转换SillyTavern条目为项目格式
 */
function convertSillyTavernEntry(entry: SillyTavernEntry, entryId: string): CreateEntryData {
  // 处理selective字段的类型转换
  let selectiveLogic: string = 'and_any';
  if (entry.selectiveLogic !== undefined) {
    // 数字转字符串
    const logicMap = ['and_any', 'not_all', 'not_any', 'and_all'];
    selectiveLogic = logicMap[entry.selectiveLogic] || 'and_any';
  } else if (entry.selective !== undefined) {
    if (typeof entry.selective === 'boolean') {
      selectiveLogic = entry.selective ? 'and_any' : 'not_any';
    } else {
      const logicMap = ['and_any', 'not_all', 'not_any', 'and_all'];
      selectiveLogic = logicMap[entry.selective] || 'and_any';
    }
  }

  // 生成标题：优先使用comment，然后使用第一个关键词，最后使用Entry ID
  let title = entry.comment || '';
  if (!title && entry.key && entry.key.length > 0) {
    title = Array.isArray(entry.key) ? entry.key[0] : String(entry.key);
  }
  if (!title) {
    title = `Entry ${entryId}`;
  }

  return {
    // 基础字段
    title,
    content: entry.content || '',
    keys: Array.isArray(entry.key) ? entry.key : [entry.key].filter(Boolean),
    keysSecondary: Array.isArray(entry.keysecondary) ? entry.keysecondary : [],
    selectiveLogic: selectiveLogic as any,

    // 激活模式 (兼容SillyTavern的constant和vectorized字段)
    activationMode: mapSillyTavernActivationMode(entry),

    // 位置控制 (SillyTavern使用数字，需要转换)
    position: mapSillyTavernPosition(entry.position),
    depth: entry.depth,
    role: mapSillyTavernRole(entry.role),
    order: entry.order || 100,

    // 匹配配置 (智能映射SillyTavern的matchSources字段)
    matchSources: mapSillyTavernMatchSources(entry),
    caseSensitive: entry.caseSensitive,
    matchWholeWords: entry.matchWholeWords,
    useRegex: entry.useRegex || false,

    // 概率控制
    probability: entry.probability || 100,
    useProbability: entry.probability !== undefined && entry.probability < 100,

    // 时间效果
    sticky: entry.sticky,
    cooldown: entry.cooldown,
    delay: entry.delay,

    // 递归控制
    excludeRecursion: entry.excludeRecursion || false,
    preventRecursion: false, // SillyTavern没有这个字段
    delayUntilRecursion: entry.delayUntilRecursion || 0,

    // 分组功能
    groupName: entry.group,
    groupWeight: entry.groupWeight || 100,
    groupOverride: false, // SillyTavern没有这个字段
    useGroupScoring: false, // SillyTavern没有这个字段

    // 扫描控制
    scanDepth: entry.depth,

    // 显示控制
    displayIndex: entry.displayIndex || 0,
    addMemo: entry.addMemo || false,

    // 装饰器 (SillyTavern没有这个字段)
    decorators: [],

    // 角色过滤 (SillyTavern没有这些字段)
    characterFilterNames: [],
    characterFilterTags: [],
    characterFilterExclude: false,
  };
}

/**
 * 验证和转换条目数据
 */
function validateAndConvertEntry(entry: any): CreateEntryData {
  if (!entry || typeof entry !== 'object') {
    throw new Error('Entry must be an object');
  }

  if (!entry.title || typeof entry.title !== 'string') {
    throw new Error('Entry title is required and must be a string');
  }

  if (!entry.content || typeof entry.content !== 'string') {
    throw new Error('Entry content is required and must be a string');
  }

  if (!Array.isArray(entry.keys)) {
    throw new Error('Entry keys must be an array');
  }

  return {
    // 基础字段
    title: entry.title,
    content: entry.content,
    keys: entry.keys,
    keysSecondary: entry.keysSecondary || [],

    // 激活模式 (兼容旧的constant字段)
    activationMode: entry.constant ? 'constant' : (entry.activationMode || 'keyword'),

    // 位置和顺序
    position: entry.position || 'after',
    depth: entry.depth,
    role: entry.role,
    order: entry.order || 100,

    // 匹配配置
    matchSources: entry.matchSources || {
      personaDescription: false,
      characterDescription: false,
      characterPersonality: false,
      characterDepthPrompt: false,
      scenario: false,
      creatorNotes: false,
    },
    caseSensitive: entry.caseSensitive,
    matchWholeWords: entry.matchWholeWords,
    useRegex: entry.useRegex || false,

    // 概率控制
    probability: entry.probability || 100,
    useProbability: entry.useProbability !== false,

    // 时间效果
    sticky: entry.sticky,
    cooldown: entry.cooldown,
    delay: entry.delay,

    // 递归控制
    excludeRecursion: entry.excludeRecursion || false,
    preventRecursion: entry.preventRecursion || false,
    delayUntilRecursion: entry.delayUntilRecursion || 0,

    // 分组功能
    groupName: entry.groupName,
    groupWeight: entry.groupWeight || 100,
    groupOverride: entry.groupOverride || false,
    useGroupScoring: entry.useGroupScoring || false,

    // 扫描控制
    scanDepth: entry.scanDepth,

    // 显示控制
    displayIndex: entry.displayIndex || 0,
    addMemo: entry.addMemo || false,

    // 装饰器
    decorators: entry.decorators || [],

    // 角色过滤
    characterFilterNames: entry.characterFilterNames || [],
    characterFilterTags: entry.characterFilterTags || [],
    characterFilterExclude: entry.characterFilterExclude || false,

    // 选择逻辑 (兼容数字和字符串)
    selectiveLogic: typeof entry.selectiveLogic === 'number'
      ? ['and_any', 'not_all', 'not_any', 'and_all'][entry.selectiveLogic] || 'and_any'
      : entry.selectiveLogic || 'and_any',
  };
}

/**
 * 从文件内容解析导入数据
 */
export function parseImportFromFile(
  fileContent: string,
  fileName?: string
): WorldbookImportData {
  let data: any;
  
  try {
    data = JSON.parse(fileContent);
  } catch {
    throw new Error('JSON格式无效，请确保文件是有效的JSON格式');
  }

  return parseImportData(data, fileName);
}

/**
 * 映射SillyTavern位置到项目格式
 */
function mapSillyTavernPosition(position?: number): string {
  switch (position) {
    case 0: return 'after';
    case 1: return 'before';
    case 2: return 'author_note_top';
    case 3: return 'author_note_bottom';
    case 4: return 'at_depth';
    case 5: return 'example_message_top';
    case 6: return 'example_message_bottom';
    default: return 'after';
  }
}

/**
 * 映射SillyTavern角色到项目格式
 */
function mapSillyTavernRole(role?: number): string {
  switch (role) {
    case 0: return 'system';
    case 1: return 'user';
    case 2: return 'assistant';
    case 3: return 'tool';
    default: return 'system';
  }
}

/**
 * 映射SillyTavern激活模式到项目格式
 */
function mapSillyTavernActivationMode(entry: any): string {
  if (entry.constant) return 'constant';
  if (entry.vectorized) return 'vectorized';
  return 'keyword';
}

/**
 * 智能映射SillyTavern的matchSources字段
 */
function mapSillyTavernMatchSources(entry: any): any {
  return {
    personaDescription: entry.matchPersonaDescription || false,
    characterDescription: entry.matchCharacterDescription || false,
    characterPersonality: entry.matchCharacterPersonality || false,
    characterDepthPrompt: entry.matchCharacterDepthPrompt || false,
    scenario: entry.matchScenario || false,
    creatorNotes: entry.matchCreatorNotes || false,
  };
}

/**
 * 获取导入预览信息
 */
export function getImportPreview(data: WorldbookImportData) {
  const { worldbook, entries } = data;
  
  // 统计信息
  const stats = {
    totalEntries: entries.length,
    enabledEntries: entries.length, // 导入时默认都启用
    constantEntries: entries.filter(e => e.constant).length,
    averageContentLength: entries.length > 0 
      ? Math.round(entries.reduce((sum, e) => sum + e.content.length, 0) / entries.length)
      : 0,
    keywordCount: entries.reduce((sum, e) => sum + e.keys.length, 0),
  };

  return {
    worldbookName: worldbook.name,
    worldbookDescription: worldbook.description,
    stats,
    sampleEntries: entries.slice(0, 3).map(entry => ({
      title: entry.title,
      contentPreview: entry.content.substring(0, 100) + (entry.content.length > 100 ? '...' : ''),
      keywords: entry.keys,
      selectiveLogic: entry.selectiveLogic,
    })),
  };
}
