{"Favorite": "<PERSON><PERSON><PERSON><PERSON>", "Tag": "Etikett", "Duplicate": "Duplikat", "Persona": "<PERSON>a", "Delete": "Löschen", "AI Response Configuration": "KI-Antwortkonfiguration", "AI Configuration panel will stay open": "Das KI-Konfigurationsfeld bleibt geöffnet", "clickslidertips": "<PERSON><PERSON> ein<PERSON>ch drauf, um die Zahlen selber einzugeben.", "MAD LAB MODE ON": "MAD LAB-MODUS EIN", "Documentation on sampling parameters": "Dokumentation zu Abtastparametern", "kobldpresets": "Kobold-Einstellungen von vorher", "guikoboldaisettings": "KoboldAI-Einstellungen für das Menü", "Update current preset": "Aktuelles Voreinstellung aktualisieren", "Save preset as": "Voreinstellung speichern als", "Import preset": "Voreinstellung importieren", "Export preset": "Voreinstellung exportieren", "Restore current preset": "Aktuelle Voreinstellung wiederherstellen", "Delete the preset": "Die Voreinstellung löschen", "novelaipresets": "NovelAI-Einstellungen von früher", "Default": "Standard", "openaipresets": "OpenAI-Einstellungen von vorher", "Text Completion presets": "Voreinstellungen für Textvervollständigung", "AI Module": "KI-Modul", "Changes the style of the generated text.": "Ändert den Stil des generierten Textes.", "No Module": "<PERSON><PERSON>", "Instruct": "<PERSON><PERSON><PERSON>", "Prose Augmenter": "Prosa-Erweiterer", "Text Adventure": "<PERSON><PERSON><PERSON><PERSON>", "response legth(tokens)": "Länge der Antwort (Tokens)", "Streaming": "Streamen", "Streaming_desc": "Zeige die Antwort Stück für Stück an, während sie generiert wird.", "context size(tokens)": "Größe des Zusammenhangs (Tokens)", "unlocked": "Freigeschaltet", "Only enable this if your model supports context sizes greater than 8192 tokens": "Aktiviere dies nur, wenn dein Modell Kontextgrößen von mehr als 8192 To<PERSON>s unterstützt.", "Max prompt cost:": "Maximale Sofortkosten:", "Display the response bit by bit as it is generated.": "Zeige die Antwort Stück für Stück, während sie generiert wird.", "When this is off, responses will be displayed all at once when they are complete.": "<PERSON><PERSON> dies ausgeschaltet ist, werden Antworten angezeigt, sobald sie vollständig sind.", "Temperature": "Temperatur", "rep.pen": "Wiederholungsstrafe", "Rep. Pen. Range.": "Wiederh. Strafe <PERSON>.", "Rep. Pen. Slope": "Steigung der Wiederholungsstrafe", "Rep. Pen. Freq.": "Wiederh. Strafe <PERSON>igkeit.", "Rep. Pen. Presence": "Wiederh. Strafe <PERSON>heit", "TFS": "TFS", "Phrase Repetition Penalty": "Strafe für wiederholte Phrasen", "Off": "Aus", "Very light": "<PERSON><PERSON> leicht", "Light": "<PERSON><PERSON>t", "Medium": "<PERSON><PERSON><PERSON>", "Aggressive": "Aggressiv", "Very aggressive": "Sehr aggressiv", "Unlocked Context Size": "Entsperrte Kontextgröße", "Unrestricted maximum value for the context slider": "Uneingeschränkter maximaler Wert für den Kontext-Schieberegler", "Context Size (tokens)": "Größe des Zusammenhangs (Tokens)", "Max Response Length (tokens)": "Maximale Antwortlänge (Tokens)", "Multiple swipes per generation": "<PERSON><PERSON><PERSON>wipes pro Generation", "Enable OpenAI completion streaming": "OpenAI-Vervollständigungsstreaming aktivieren", "Frequency Penalty": "Frequenzstrafe", "Presence Penalty": "Präsenzstrafe", "Count Penalty": "<PERSON><PERSON><PERSON>", "Top K": "Top K", "Top P": "Top P", "Repetition Penalty": "Wiederholungsstrafe", "Min P": "Mindestens P", "Top A": "Top A", "Quick Prompts Edit": "Schnelle Aufforderungen bearbeiten", "Main": "<PERSON><PERSON><PERSON>", "NSFW": "Nicht für die Arbeit geeignet", "Jailbreak": "Ausbruch", "Utility Prompts": "<PERSON><PERSON><PERSON>-Prompts", "Impersonation prompt": "Aufforderung zur Personifikation", "Restore default prompt": "<PERSON>prom<PERSON> wied<PERSON>hers<PERSON>len", "Prompt that is used for Impersonation function": "Aufforderung, die für die Funktion der Personifikation verwendet wird.", "World Info Format Template": "Vorlage für das World Info-Format", "Restore default format": "Standardformat wiederherstellen", "Wraps activated World Info entries before inserting into the prompt.": "Umschließt aktivierte World Info-Einträge vor dem Einfügen in die Eingabeaufforderung.", "scenario_format_template_part_1": "Verwenden", "scenario_format_template_part_2": "um eine Stelle zu markieren, an der der Inhalt eingefügt wird.", "Scenario Format Template": "Szenarioformatvorlage", "Personality Format Template": "Vorlage für das Persönlichkeitsformat", "Group Nudge Prompt Template": "Vorlage für die Gruppenanstoß-Eingabeaufforderung", "Sent at the end of the group chat history to force reply from a specific character.": "Wird am Ende des Gruppenchatverlaufs gesendet, um eine Antwort von einem bestimmten Charakter zu erzwingen.", "New Chat": "<PERSON><PERSON><PERSON>", "Restore new chat prompt": "Neue Chat-Eingabeaufforderung wiederherstellen", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "Wird am Anfang des Chatverlaufs gesetzt, um anzuzeigen, dass gerade ein neuer Chat beginnt.", "New Group Chat": "Neuer Gruppenchat", "Restore new group chat prompt": "Standardeingabeaufforderung wiederherstellen", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "Wird am Anfang des Chatverlaufs gesetzt, um anzuzeigen, dass gerade ein neuer Gruppenchat beginnt.", "New Example Chat": "<PERSON><PERSON>er Be<PERSON>piel-Chat", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "Wird zu Be<PERSON>n von Dialogbeispielen gesetzt, um anzuzeigen, dass ein neuer Beispiel-Chat gestartet wird.", "Continue nudge": "<PERSON><PERSON>", "Set at the end of the chat history when the continue button is pressed.": "Wird am Ende des Chatverlaufs gesetzt, wenn die Schaltfläche „Weiter“ gedrückt wird.", "Replace empty message": "<PERSON><PERSON>", "Send this text instead of nothing when the text box is empty.": "Sende diesen Text, wenn das Textfeld leer ist, anstelle von nichts.", "Seed": "Seed", "Set to get deterministic results. Use -1 for random seed.": "Eingestellt, um deterministische Ergebnisse zu erhalten. Verwenden Sie -1 für den Zufallsstartwert.", "Temperature controls the randomness in token selection": "Die Temperatur steuert die Zufälligkeit bei der Tokenauswahl:\n- Eine niedrige Temperatur (<1,0) führt zu intelligenterem Text, wobei häufig auftretende Tokens (Wörter oder Zeichen) priorisiert werden.\n- Eine hohe Temperatur (>1,0) erhöht die Kreativität und die Vielfalt der Ausgabe, wobei seltenere Tokens (Wörter oder Zeichen) eine größere Chance haben.\nStelle den Wert auf 1,0 für die Standardwahrscheinlichkeiten ein.", "Top_K_desc": "Top K legt ein Limit für die obersten Tokens fest, die ausgewählt werden können.", "Top_P_desc": "Top P (auch bekannt als Kernsampling) kombiniert alle erforderlichen obersten Tokens, um einen bestimmten Prozentsatz zu erreichen.\n<PERSON>, wenn die obersten 2 Tokens 25% ausmachen und Top P 0,50 beträgt, werden nur diese beiden obersten Tokens berücksichtigt.\nStelle den Wert auf 1,0, um dies zu deaktivieren.", "Typical P": "Typisch P", "Typical_P_desc": "Bei der typischen P-Stichprobe werden Tokens priorisiert, basierend auf ihrer Abweichung von der durchschnittlichen Entropie des Satzes.\nTokens mit einer kumulierten Wahrscheinlichkeit nahe am definierten Schwellenwert (z. B. 0,5) werden beibehalten, was <PERSON><PERSON><PERSON>, dass sie einen mittleren Informationsgehalt haben.\n<PERSON>elle den Wert auf 1,0, um dies zu deaktivieren.", "Min_P_desc": "Min P legt eine Basismindestwahrscheinlichkeit fest. Diese wird basierend auf der Wahrscheinlichkeit des obersten Tokens optimiert.\nWenn die Wahrscheinlichkeit des obersten Tokens 80% beträgt und Min P 0,1 beträgt, werden nur Tokens mit einer Wahrscheinlichkeit von mehr als 8% berücksichtigt.\nStelle den Wert auf 0,0, um dies zu deaktivieren.", "Top_A_desc": "Top A legt einen Schwellenwert für die Tokenauswahl basierend auf dem Quadrat der höchsten Tokenwahrscheinlichkeit fest.\nWenn Top A 0,2 beträgt und die Wahrscheinlichkeit des obersten Tokens 50% beträgt, werden Tokens mit einer Wahrscheinlichkeit von weniger als 5% ausgeschlossen (0,2 * 0,5^2).\n<PERSON><PERSON> den Wert auf 0,0, um dies zu deaktivieren.", "Tail_Free_Sampling_desc": "Schwanzfreie Stichprobe (TFS) sucht nach schwach wahrscheinlichen Tokens in der Verteilung,\n indem sie die Änderungsrate der Tokenwahrscheinlichkeiten mithil<PERSON> von Derivaten analysiert. Tokens werden bis zu einer bestimmten Schwelle (z. B. 0,3) beibehalten, basierend auf der zweiten einheitlichen Ableitung.\nJe näher 0, desto mehr Tokens werden abgelehnt. Stelle den Wert auf 1,0, um dies zu deaktivieren.", "rep.pen range": "Bereich der Wiederholungsstrafe", "Mirostat": "Mirostat", "Mode": "Modus", "Mirostat_Mode_desc": "Ein Wert von 0 deaktiviert Mirostat vollständig. 1 steht für Mirostat 1.0 und 2 für Mirostat 2.0", "Tau": "Tau", "Mirostat_Tau_desc": "Steuert die Variabilität der Mirostat-Ausgaben", "Eta": "Eta", "Mirostat_Eta_desc": "<PERSON><PERSON><PERSON> die Lernrate von Mirostat", "Ban EOS Token": "Verbiet das EOS-Token", "Ban_EOS_Token_desc": "Verbot des End-of-Sequence (EOS)-Tokens mit KoboldCpp (und möglicherweise auch anderer Token mit KoboldAI). Gut zum Schreiben von Geschichten, sollte aber nicht für den Chat- und Anweisungsmodus verwendet werden.", "GBNF Grammar": "GBNF-Grammatik", "Type in the desired custom grammar": "Gib die gewünschte individuelle Grammatik ein.", "Samplers Order": "Sampler-Reihenfolge", "Samplers will be applied in a top-down order. Use with caution.": "Sampler werden in einer Top-Down-Reihenfolge angewendet. Benutzen Sie es mit Vorsicht.", "Tail Free Sampling": "Schwanzfreie Stichprobe", "Load koboldcpp order": "Laden Sie die Reihenfolge von koboldcpp", "Preamble": "Vorwort", "Use style tags to modify the writing style of the output.": "Verwende Stil-Tags, um den Schreibstil der Ausgabe zu ändern.", "Banned Tokens": "Verbotene Tokens", "Sequences you don't want to appear in the output. One per line.": "<PERSON><PERSON><PERSON><PERSON>, die du nicht in der Ausgabe sehen möchtest. Eine pro Zeile.", "Logit Bias": "Logit-Bias", "Add": "Hinzufügen", "Helps to ban or reenforce the usage of certain words": "<PERSON><PERSON><PERSON> da<PERSON>, die Verwendung bestimmter Wörter zu verbieten oder zu verstärken.", "CFG Scale": "CFG-Skala", "Negative Prompt": "Negatives Prompt", "Add text here that would make the AI generate things you don't want in your outputs.": "Füge hier Text hinzu, der die KI dazu bringen würde, <PERSON><PERSON> zu generieren, die du nicht in deinen Ausgaben haben möchtest.", "Used if CFG Scale is unset globally, per chat or character": "<PERSON>ird ver<PERSON><PERSON>, wenn die CFG-Skalierung global, pro Chat oder pro Zeichen nicht festgelegt ist.", "Mirostat Tau": "Mirostat Tau", "Mirostat LR": "Mirostat LR", "Min Length": "Minimale Länge", "Top K Sampling": "Top K-Beprobung", "Nucleus Sampling": "Nukleus-Beprobung", "Top A Sampling": "Top A-Beprobung", "CFG": "CFG", "Neutralize Samplers": "Sampler neutralisieren", "Set all samplers to their neutral/disabled state.": "Setze alle Sampler auf ihren neutralen/deaktivierten Zustand.", "Sampler Select": "Sampler-Auswahl", "Customize displayed samplers or add custom samplers.": "Passen Sie angezeigte Sampler an oder fügen Sie benutzerdefinierte Sampler hinzu.", "Epsilon Cutoff": "Epsilon-Abschaltung", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "Epsilon-Cutoff legt einen Wahrscheinlichkeitsboden fest, unter dem Tokens vom Abtasten ausgeschlossen werden.\nIn Einheiten von 1e-4; der geeignete Wert ist 3.\n<PERSON><PERSON> 0,0 ein, um dies zu deaktivieren.", "Eta Cutoff": "Eta-Abschaltung", "Eta_Cutoff_desc": "Eta-Cutoff ist der Hauptparameter der speziellen Eta-Beprobungstechnik.&#13;In Einheiten von 1e-4; ein vernünftiger Wert ist 3.&#13;Auf 0 setzen, um zu deaktivieren.&#13;<PERSON><PERSON>e das Paper Truncation Sampling as Language Model Desmoothing <PERSON> et al. (2022) für Details.", "rep.pen decay": "Rep Pen-Verfall", "Encoder Rep. Pen.": "Encoder Wiederholungsstrafe.", "No Repeat Ngram Size": "<PERSON>ine Wiederholung Ngram-Größe", "Skew": "Schief<PERSON>", "Max Tokens Second": "Maximale Tokens pro Sekunde", "Smooth Sampling": "Reibungs<PERSON><PERSON> <PERSON>", "Smooth_Sampling_desc": "Ermöglicht Ihnen, quadratische/kubische Transformationen zu verwenden, um die Verteilung anzupassen. Niedrigere Glättungsfaktorwerte sind kreativer, normalerweise liegt der Sweetspot zwischen 0,2 und 0,3 (vorausgesetzt, die Kurve ist = 1). <PERSON><PERSON><PERSON> Glättungskurvenwerte machen die Kurve steiler, was Entscheidungen mit geringer Wahrscheinlichkeit aggressiver bestraft. Eine Kurve von 1,0 entspricht der alleinigen Verwendung des Glättungsfaktors.", "Smoothing Factor": "Glättungsfaktor", "Smoothing Curve": "Glättungskurve", "DRY_Repetition_Penalty_desc": "DRY bestraft Token, die das Ende der Eingabe in eine Sequenz verlängern würden, die zuvor in der Eingabe aufgetreten ist. Setzen Sie den Multiplikator auf 0, um dies zu deaktivieren.", "DRY Repetition Penalty": "DRY Wiederholungsstrafe", "DRY_Multiplier_desc": "Auf einen Wert > 0 setzen, um DRY zu aktivieren. Steuert die Stärke der Strafe für die kürzesten bestraften Sequenzen.", "Multiplier": "Multiplikator", "DRY_Base_desc": "<PERSON><PERSON><PERSON>, wie schnell die Strafe mit zunehmender Sequenzlänge wächst.", "Base": "Base", "DRY_Allowed_Length_desc": "<PERSON>ängste Sequenz, die ohne Strafe wiederholt werden kann.", "Allowed Length": "Erlaubte Länge", "Penalty Range": "Strafbereich", "DRY_Sequence_Breakers_desc": "<PERSON><PERSON>, bei denen die Sequenzübereinstimmung nicht fortgesetzt wird. Angegeben als durch Kommas getrennte Liste zitierter Zeichenfolgen.", "Sequence Breakers": "Sequenzunterbrecher", "JSON-serialized array of strings.": "JSON-serialisiertes Array von Zei<PERSON>folgen.", "Dynamic Temperature": "Dynamische Temperatur", "Scale Temperature dynamically per token, based on the variation of probabilities": "Die Skalierung der Temperatur wird dynamisch pro Token festgelegt, basierend auf der Variation der Wahrscheinlichkeiten.", "Minimum Temp": "Minimale Temperatur", "Maximum Temp": "Maximale Temperatur", "Exponent": "Exponent", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat (Modus=1 gilt nur für llama.cpp)", "Mirostat_desc": "Mirostat ist ein Thermostat für die Ausgangsperplexität. Es ist ein Mechanismus zur Anpassung der Ausgangsschwierigkeit, um Konsistenz zwischen Eingabe und Ausgabe zu erreichen.", "Mirostat Mode": "Mirostat-Modus", "Variability parameter for Mirostat outputs": "Variabilitätsparameter für Mirostat-Ausgaben.", "Mirostat Eta": "Mirostat Eta", "Learning rate of Mirostat": "<PERSON><PERSON><PERSON> Mir<PERSON>.", "Beam search": "Strahlensuche", "Helpful tip coming soon.": "Hilfreicher Tipp folgt in Kürze.", "Number of Beams": "Anzahl der Strahlen", "Length Penalty": "Längenstrafe", "Early Stopping": "<PERSON><PERSON><PERSON>", "Contrastive search": "Kontrastive Suche", "Penalty Alpha": "Strafe Alpha", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "Stärke des Regularisierungsterms für den kontrastiven Suchvorgang. Setze ihn auf 0, um CS zu deaktivieren.", "Do Sample": "<PERSON><PERSON><PERSON>'s aus", "Add BOS Token": "Füge ein BOS-Token hinzu", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "Füg das bos_token am Anfang der Aufforderungen hinzu. Wenn du das deaktivierst, können die Antworten kreativer werden.", "Ban the eos_token. This forces the model to never end the generation prematurely": "Verbiet das eos_token. So wird das Modell g<PERSON>wungen, die Generierung niemals vorzeitig zu beenden.", "Ignore EOS Token": "EOS-Token ignorieren", "Ignore the EOS Token even if it generates.": "Ignorieren Sie das EOS-Token, auch wenn es generiert wird.", "Skip Special Tokens": "Spezielle Tokens überspringen", "Temperature Last": "Letzte Temperatur", "Temperature_Last_desc": "Benutz den Temperaturmuster zuletzt. Ist normalerweise sinnvoll.\nWen<PERSON> aktiv<PERSON>, werden zuerst eine Gruppe potenzieller Tokens ausgewählt und dann wird die Temperatur angewendet, um ihre relativen Wahrscheinlichkeiten (technisch: Logits) zu korrigieren.\n<PERSON><PERSON>, wird die Temperatur zuerst angewendet, um die relativen Wahrscheinlichkeiten für alle Tokens zu korrigieren, und dann wird eine Gruppe potenzieller Tokens daraus ausgewählt.\nDas Deaktivieren der Temperatur am Ende erhöht die Wahrscheinlichkeit von Tokens im Schwanz der Verteilung und erhöht die Wahrscheinlichkeit inkonsistenter Antworten.", "Speculative Ngram": "Speku<PERSON><PERSON>", "Use a different speculative decoding method without a draft model": "Verwenden Sie eine andere spekulative Dekodierungsmethode ohne Entwurfsmodell. Die Verwendung eines Entwurfsmodells ist vorzuziehen. Spekulatives N-Gramm ist nicht so effektiv.", "Spaces Between Special Tokens": "Leerzeichen zwischen Sonderzeichen", "LLaMA / Mistral / Yi models only": "Nur für LLaMA / Mistral / Yi-<PERSON>le. <PERSON><PERSON>, dass du zuerst den richtigen Analyzer auswählst.\nStrings sollten nicht in den Ergebnissen erscheinen.\nEine Zeichenfolge pro Zeile. Text oder [Zeichenkennungen].\nViele Zeichen beginnen mit einem Leerzeichen. Verwende den Zeichenzähler, wenn du unsicher bist.", "Example: some text [42, 69, 1337]": "Beispiel:\nEin bisschen Text\n[42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "Anleitung ohne Klassifizierer. Bald kommen weitere hilfreiche Tipps.", "Scale": "Maßstab", "JSON Schema": "JSON-Schema", "Type in the desired JSON schema": "Geben Sie das gewünschte JSON-Schema ein", "Grammar String": "Grammatikzeichenfolge", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF oder EBNF, hängt vom verwendeten Backend ab. <PERSON><PERSON> dies<PERSON> verwenden, soll<PERSON> wissen, welches.", "Top P & Min P": "Top P und Min P", "Load default order": "Standardreihenfolge laden", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "Nur llama.cpp. Bestimmt die Reihenfolge der Sampler. Wenn der Mirostat-Modus nicht 0 ist, wird die Sampler-Reihenfolge ignoriert.", "Sampler Priority": "Sampler-Priorität", "Ooba only. Determines the order of samplers.": "Nur Ooba. Bestimmt die Reihenfolge der Sampler.", "Character Names Behavior": "Charakternamen Verhalten", "Helps the model to associate messages with characters.": "<PERSON><PERSON><PERSON> dem <PERSON>l, <PERSON><PERSON><PERSON><PERSON> mit Zeichen zu verknüpfen.", "None": "<PERSON><PERSON>", "character_names_default": "Außer für Gruppen und frühere Personas. Andernfalls stellen Si<PERSON> sicher, dass Sie in der Eingabeaufforderung Namen angeben.", "Don't add character names.": "Fügen Sie keine Charakternamen hinzu.", "Completion": "Vervollständigungsobjekt", "character_names_completion": "Es gelten Einschränkungen: nur lateinische alphanumerische Zeichen und Unterstriche. Funktioniert nicht für alle Quellen, insbesondere: Claude, MistralAI, Google.", "Add character names to completion objects.": "Fügen Sie den Vervollständigungsobjekten Charakternamen hinzu.", "Message Content": "Nachrichteninhalt", "Prepend character names to message contents.": "Stellen Sie dem Nachrichteninhalt die Zeichennamen voran.", "Continue Postfix": "<PERSON><PERSON> mit Postfix", "The next chunk of the continued message will be appended using this as a separator.": "Der nächste Teil der Fortsetzungsnachricht wird mit diesem als Trennzeichen angehängt.", "Space": "<PERSON><PERSON>", "Newline": "Neue Zeile", "Double Newline": "Doppelte Neue Zeile", "Wrap user messages in quotes before sending": "Umschließt Benutzerbotschaften vor dem Senden mit Anführungszeichen", "Wrap in Quotes": "In Anführungszeichen setzen", "Wrap entire user message in quotes before sending.": "Umschließe die gesamte Benutzernachricht vor dem Senden in Anführungszeichen.", "Leave off if you use quotes manually for speech.": "Lass es weg, wenn du Anführungszeichen manuell für die Sprache verwendest.", "Continue prefill": "<PERSON>t dem Vorausfüllen fortfahren", "Continue sends the last message as assistant role instead of system message with instruction.": "Mit dem Fortfahren wird die letzte Nachricht als Assistenzrolle gesendet, anstatt als Systemnachricht mit Anweisungen.", "Squash system messages": "Systemnachrichten zusammenfassen", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "Kombiniert aufeinanderfolgende Systemnachrichten zu einer (ausschließlich Beispiel-Dialoge ausgeschlossen). Kann die Kohärenz für einige Modelle verbessern.", "Enable function calling": "Funktionsaufruf aktivieren", "Send inline images": "Inline-Bilder senden", "image_inlining_hint_1": "Sendet Bilder in Eingabeaufforderungen, wenn das Modell dies unterstützt.\nVerwenden Sie die", "image_inlining_hint_2": "Aktion auf eine Nachricht oder die", "image_inlining_hint_3": "<PERSON><PERSON>, um eine Bilddatei an den Chat anzuhängen.", "Inline Image Quality": "Inline-Bildqualität", "openai_inline_image_quality_auto": "Auto", "openai_inline_image_quality_low": "<PERSON><PERSON><PERSON>", "openai_inline_image_quality_high": "Hoch", "Use AI21 Tokenizer": "Verwenden Sie den AI21 Tokenizer", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "Verwenden Sie den entsprechenden Tokenizer für Jurassic-Modelle, der effizienter ist als der von GPT.", "Use Google Tokenizer": "Google-Tokenizer verwenden", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "Verwenden Sie den geeigneten Tokenizer für Google-Modelle über deren API. Langsamere Prompt-Verarbeitung, bietet jedoch eine viel genauere Token-Zählung.", "Use system prompt": "Systemaufforderung verwenden", "(Gemini 1.5 Pro/Flash only)": "(nur Gemini 1.5 Pro/Flash)", "Merges_all_system_messages_desc_1": "Führt alle Systemnachrichten bis zur ersten Nachricht mit einer Nicht-Systemrolle zusammen und sendet sie in einer", "Merges_all_system_messages_desc_2": "Feld.", "Assistant Prefill": "Assistenten-Vorausfüllung", "Start Claude's answer with...": "<PERSON><PERSON><PERSON> Claudes Antwort mit...", "Assistant Impersonation Prefill": "Identitätswechsel des Assistenten vorab ausfüllen", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "Senden Sie die Systemaufforderung für unterstützte Modelle. <PERSON><PERSON>, wird die Benutzernachricht am Anfang der Aufforderung hinzugefügt.", "User first message": "Erste Nachricht des Benutzers", "Restore User first message": "Erste Nachricht des Benutzers wiederherstellen", "Human message": "Mensch<PERSON> Nachricht, Anweisung usw.\n<PERSON><PERSON><PERSON> nichts hinzu, wenn es leer ist, d. h. er<PERSON>ert eine neue Eingabeaufforderung mit der Rolle „Benutzer“.", "New preset": "Neue Voreinstellung", "Delete preset": "Voreinstellung löschen", "View / Edit bias preset": "Voreinstellung für Bias anzeigen / bearbeiten", "Add bias entry": "Bias-Ein<PERSON>g hinzufügen", "Most tokens have a leading space.": "Die meisten Token haben ein führendes Leerzeichen.", "API Connections": "API-Verbindungen", "Text Completion": "Textvervollständigung", "Chat Completion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "Vermeide das Senden sensibler Informationen an die Horde.", "Review the Privacy statement": "Überprüfe die Datenschutzerklärung", "Register a Horde account for faster queue times": "Registriere einen Horde-Account für kürzere Wartezeiten in der Warteschlange", "Learn how to contribute your idle GPU cycles to the Horde": "<PERSON><PERSON><PERSON><PERSON>, wie Sie Ihre ungenutzten GPU-Zyklen zum Horde beitragen können", "Adjust context size to worker capabilities": "Passe die Kontextgröße an die Fähigkeiten des Arbeiters an", "Adjust response length to worker capabilities": "Passe die Länge der Antwort an die Fähigkeiten des Arbeiters an", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "Kann bei schlechten Antworten helfen, indem nur die genehmigten Arbeiter in die Warteschlange gestellt werden. Kann die Reaktionszeit verlangsamen.", "Trusted workers only": "Nur vertrauenswürdige Arbeiter", "API key": "API-Schlüssel", "Get it here:": "Hol es dir hier:", "Register": "Registrieren", "View my Kudos": "<PERSON><PERSON> anzeigen", "Enter": "Eingeben", "to use anonymous mode.": "um den anonymen Modus zu verwenden.", "Clear your API key": "Lösche deinen API-Schlüssel", "For privacy reasons, your API key will be hidden after you reload the page.": "Aus Datenschutzgründen wird Ihr API-Schlüssel nach dem Neuladen der Seite verborgen.", "Models": "<PERSON><PERSON>", "Refresh models": "Modelle aktualisieren", "-- Horde models not loaded --": "-- Horde-<PERSON><PERSON> nicht geladen --", "Not connected...": "Nicht verbunden...", "API url": "API-URL", "Example: http://127.0.0.1:5000/api ": "Beispiel: http://127.0.0.1:5000/api", "Connect": "Verbinden", "Cancel": "Abbrechen", "Novel API key": "NovelAPI-Schlüssel", "Get your NovelAI API Key": "Hol dir deinen NovelAI API-Schlüssel", "Enter it in the box below": "Gib ihn im untenstehenden Feld ein", "Novel AI Model": "Novel AI-Modell", "No connection...": "<PERSON><PERSON>...", "API Type": "API-Typ", "Default (completions compatible)": "Standard [OpenAI /completions-kompatibel: oobabooga, LM Studio usw.]", "TogetherAI API Key": "TogetherAI API-Schlüssel", "TogetherAI Model": "TogetherAI-Modell", "-- Connect to the API --": "-- <PERSON><PERSON> der API verbinden --", "OpenRouter API Key": "OpenRouter API-Schlüssel", "Click Authorize below or get the key from": "<PERSON>licke unten auf Autorisieren oder hol dir den Schlüssel von", "View Remaining Credits": "Verbleibende Gutschriften anzeigen", "OpenRouter Model": "OpenRouter-Modell", "Model Providers": "<PERSON><PERSON><PERSON><PERSON>", "InfermaticAI API Key": "InfermaticAI API-Schlüssel", "InfermaticAI Model": "InfermaticAI-Modell", "DreamGen API key": "DreamGen API-Schlüssel", "DreamGen Model": "DreamGen-Modell", "Mancer API key": "Mancer API-Schlüssel", "Mancer Model": "<PERSON><PERSON>-<PERSON><PERSON>", "Make sure you run it with": "<PERSON><PERSON> sicher, dass du es ausführst mit", "flag": "Flagge", "API key (optional)": "API-<PERSON><PERSON><PERSON><PERSON> (optional)", "Server url": "Server-URL", "Example: http://127.0.0.1:5000": "Beispiel: http://127.0.0.1:5000", "Custom model (optional)": "Benutzerdefiniertes Modell (optional)", "vllm-project/vllm": "vllm-project/vllm (OpenAI API-Wrappermodus)", "vLLM API key": "vLLM-API-Schlüssel", "Example: http://127.0.0.1:8000": "Beispiel: http://127.0.0.1:8000", "vLLM Model": "vLLM-Modell", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine (Wrappermodus für OpenAI API)", "Aphrodite API key": "Aphrodite API-Schlüssel", "Aphrodite Model": "Aphrodite-Modell", "ggerganov/llama.cpp": "ggerganov/llama.cpp (Output-Server)", "Example: http://127.0.0.1:8080": "Beispiel: http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "Beispiel: http://127.0.0.1:11434", "Ollama Model": "Ollama-Modell", "Download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tabby API key": "Tabby API-Schlüssel", "koboldcpp API key (optional)": "koboldcpp API-Schlüssel (optional)", "Example: http://127.0.0.1:5001": "Beispiel: http://127.0.0.1:5001", "Authorize": "Autorisieren", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "Hole dein OpenRouter-API-Token mit OAuth-Fluss. Du wirst zu openrouter.ai weitergeleitet", "Bypass status check": "Umgehe Statusüberprüfung", "Chat Completion Source": "Quelle für Chat-Vervollständigung", "Reverse Proxy": "Reverse-Proxy", "Proxy Presets": "Proxy-Voreinstellungen", "Saved addresses and passwords.": "Gespeicherte Adressen und Passwörter.", "Save Proxy": "Proxy speichern", "Delete Proxy": "Proxy löschen", "Proxy Name": "Proxy-Name", "This will show up as your saved preset.": "Dies wird als Ihre gespeicherte Voreinstellung angezeigt.", "Proxy Server URL": "Proxy-Server-URL", "Alternative server URL (leave empty to use the default value).": "Alternative Server-URL (lassen <PERSON> le<PERSON>, um den Standardwert zu verwenden).", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "Entferne deinen echten OAI-API-Schlüssel aus dem API-Panel, BEVOR du etwas in dieses Feld eingibst.", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "Wir können keine Unterstützung für Probleme bieten, die bei der Verwendung eines inoffiziellen OpenAI-Proxys auftreten.", "Doesn't work? Try adding": "Funktioniert nicht? Versuchen Sie hinzuzufügen", "at the end!": "Am Ende!", "Proxy Password": "Proxy-Passwort", "Will be used as a password for the proxy instead of API key.": "Wird anstelle des API-Schlüssels als Kennwort für den Proxy verwendet.", "Peek a password": "Einsehen eines Passworts", "OpenAI API key": "OpenAI API-Schlüssel", "View API Usage Metrics": "API-Nutzungsmetriken anzeigen", "Follow": "Folgen", "these directions": "diesen Anweisungen", "to get your OpenAI API key.": "um deinen OpenAI-API-Schlüssel zu erhalten.", "Use Proxy password field instead. This input will be ignored.": "Verwenden Sie stattdessen das Feld „Proxy-Passwort“. Diese Eingabe wird ignoriert.", "OpenAI Model": "OpenAI-Modell", "Bypass API status check": "Umgehe API-Statusüberprüfung", "Show External models (provided by API)": "Externe Modelle anzeigen (bereitgestellt von API)", "Get your key from": "Hol dir deinen <PERSON> von", "Anthropic's developer console": "Anthropics Entwicklerkonsole", "Claude Model": "<PERSON><PERSON><PERSON><PERSON>", "Window AI Model": "Fenster AI-Modell", "Model Order": "Sortierung des OpenRouter-Modells", "Alphabetically": "Alphabetisch", "Price": "Preis (am günstigsten)", "Context Size": "Kontextgröße", "Group by vendors": "Gruppieren nach Anbietern", "Group by vendors Description": "Platzieren Sie OpenAI-Modelle in einer Gruppe, anthropogene Modelle in einer anderen Gruppe usw. Kann mit Sortierung kombiniert werden.", "Allow fallback routes": "Fallback-<PERSON><PERSON>", "Allow fallback routes Description": "Das alternative Modell wird automatisch ausgewählt, wenn das ausgewählte Modell Ihre Anfrage nicht erfüllen kann.", "Scale API Key": "Scale API-Schlüssel", "Clear your cookie": "Löschen Sie Ihre Cookies", "Alt Method": "Alternative Methode", "AI21 API Key": "AI21 API-Schlüssel", "AI21 Model": "AI21-Modell", "Google AI Studio API Key": "Google AI Studio API-Schlüssel", "Google Model": "Google-Modell", "MistralAI API Key": "MistralAI API-Schlüssel", "MistralAI Model": "MistralAI-Modell", "Groq API Key": "Groq API-Schlüssel", "Groq Model": "Groq-Modell", "Perplexity API Key": "Perplexity API-Schlüssel", "Perplexity Model": "Perplexitätsmodell", "Cohere API Key": "Cohere API-Schlüssel", "Cohere Model": "Cohere-Modell", "Custom Endpoint (Base URL)": "Benutzerdefinierter Endpunkt (Basis-URL)", "Custom API Key": "Benutzerdefinierter API-Schlüssel", "Available Models": "Verfügbare Modelle", "Prompt Post-Processing": "Zeitnahe Nachbearbeitung", "Applies additional processing to the prompt before sending it to the API.": "Wendet zusätzliche Verarbeitungsvorgänge auf die Eingabeaufforderung an, bevor sie an die API gesendet wird.", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "Überprüft deine API-Verbindung durch Senden einer kurzen Testnachricht. Sei dir bewusst, dass du dafür gutgeschrieben wirst!", "Test Message": "Testnachricht", "Auto-connect to Last Server": "Automatisch mit dem letzten Server verbinden", "Missing key": "❌ <PERSON><PERSON><PERSON>", "Key saved": "✔️ Schlüssel gespeichert", "View hidden API keys": "Versteckte API-Schlüssel anzeigen", "AI Response Formatting": "KI-Antwortformatierung", "Advanced Formatting": "Erweiterte Formatierung", "Context Template": "Kontextvorlage", "Auto-select this preset for Instruct Mode": "Diese Voreinstellung automatisch für den Anweisungsmodus auswählen", "Story String": "Geschichtszeichenfolge", "Example Separator": "Beispiel-Trennzeichen", "Chat Start": "Chat-Start", "Add Chat Start and Example Separator to a list of stopping strings.": "Fügen Sie einer Liste von Stoppzeichenfolgen „Chat-Start“ und „Beispieltrennzeichen“ hinzu.", "Use as Stop Strings": "Verwende als Stoppzeichenfolgen", "Allow Jailbreak": "<PERSON>l<PERSON> zu<PERSON>en", "Context Order": "Kontextreihenfolge", "Summary": "Zusammenfassung", "Author's Note": "Hinweis des Autors", "Example Dialogues": "Beispieldialoge", "Hint": "<PERSON><PERSON><PERSON><PERSON>:", "In-Chat Position not affected": "Die Bestellungen für Zusammenfassungen und Autorennotizen sind nur dann betroffen, wenn für sie keine In-Chat-Position festgelegt ist.", "Instruct Mode": "Anweisungsmodus", "Enabled": "Aktiviert", "instruct_bind_to_context": "<PERSON><PERSON> aktiv<PERSON>, werden Kontextvorlagen automatisch basierend auf dem ausgewählten Anweisungsvorlagennamen oder nach Präferenz ausgewählt.", "Bind to Context": "An Kontext binden", "Presets": "Voreinstellungen", "Auto-select this preset on API connection": "Diese Voreinstellung automatisch bei API-Verbindung auswählen", "Activation Regex": "Aktivierungsregex", "Wrap Sequences with Newline": "Sequenzen mit Zeilenumbruch umschließen", "Replace Macro in Sequences": "Makro in Sequenzen ersetzen", "Skip Example Dialogues Formatting": "Formatierung der Beispiel-Dialoge überspringen", "Include Names": "Namen e<PERSON>", "Force for Groups and Personas": "Erzwingen für Gruppen und Personen", "System Prompt": "System-Prompt", "Instruct Mode Sequences": "Anweisungsmodus-Sequenzen", "System Prompt Wrapping": "Umbruch der Systemeingabeaufforderung", "Inserted before a System prompt.": "Wird vor einer Systemaufforderung eingefügt.", "System Prompt Prefix": "Präfix der Systemaufforderung", "Inserted after a System prompt.": "Wird nach einer Systemaufforderung eingefügt.", "System Prompt Suffix": "Systemaufforderungssuffix", "Chat Messages Wrapping": "<PERSON><PERSON><PERSON><PERSON> von Chat-Nachrichten", "Inserted before a User message and as a last prompt line when impersonating.": "Wird vor einer Benutzernachricht und als letzte Eingabeaufforderungszeile beim Identitätswechsel eingefügt.", "User Message Prefix": "Benutzernachrichtenpräfix", "Inserted after a User message.": "Wird nach einer Benutzernachricht eingefügt.", "User Message Suffix": "Benutzernachrichtensuffix", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "Wird vor einer Assistentennachricht und als letzte Eingabeaufforderungszeile beim Generieren einer KI-Antwort eingefügt.", "Assistant Message Prefix": "Assistenten-Nachrichtenpräfix", "Inserted after an Assistant message.": "Wird nach einer Assistentennachricht eingefügt.", "Assistant Message Suffix": "Assistentennachrichtensuffix", "Inserted before a System (added by slash commands or extensions) message.": "Wird vor einer Systemnachricht (hinzugefügt durch Schrägstrichbefehle oder Erweiterungen) eingefügt.", "System Message Prefix": "Systemnachrichtenpräfix", "Inserted after a System message.": "Wird nach einer Systemnachricht eingefügt.", "System Message Suffix": "Systemnachrichtensuffix", "If enabled, System Sequences will be the same as User Sequences.": "<PERSON><PERSON> akt<PERSON><PERSON>, sind die System-Sequenzen dieselben wie die Benutzer-Sequenzen.", "System same as User": "<PERSON> gleich <PERSON>", "Misc. Sequences": "Verschiedene Sequenzen", "Inserted before the first Assistant's message.": "Wird vor der ersten Nachricht des Assistenten eingefügt.", "First Assistant Prefix": "Präfix des ersten Assistenten", "instruct_last_output_sequence": "Wird vor der letzten Nachricht des Assistenten oder als letzte Eingabeaufforderungszeile beim Generieren einer KI-Antwort eingefügt (außer bei einer neutralen/Systemrolle).", "Last Assistant Prefix": "Präfix des letzten Assistenten", "Will be inserted as a last prompt line when using system/neutral generation.": "Wird bei Verwendung der System-/Neutralgenerierung als letzte Eingabeaufforderungszeile eingefügt.", "System Instruction Prefix": "Systemanweisungspräfix", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "Wenn eine Stoppsequenz generiert wird, wird alles danach (einschließlich) aus der Ausgabe entfernt.", "Stop Sequence": "Stoppsequenz", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "Wird am Anfang des Chatverlaufs eingefügt, wenn dieser nicht mit einer Benutzernachricht beginnt.", "User Filler Message": "Benutzer-Füllnachricht", "Context Formatting": "Kontextformatierung", "(Saved to Context Template)": "(Gespeichert in Kontextvorlage)", "Always add character's name to prompt": "Füge immer den Namen des Charakters zur Eingabe hinzu", "Generate only one line per request": "Generiere nur eine Zeile pro Anfrage", "Trim Incomplete Sentences": "Unvollständige Sätze entfernen", "Include Newline": "Zeilenumbruch einbeziehen", "Misc. Settings": "Sonstige Einstellungen", "Collapse Consecutive Newlines": "Aufeinanderfolgende neue Zeilen zusammenfalten", "Trim spaces": "Leerzei<PERSON> entfernen", "Tokenizer": "Tokenizer", "Token Padding": "Token-Auffüllung", "Start Reply With": "Antwort mit starten", "AI reply prefix": "KI-Antwortpräfix", "Show reply prefix in chat": "Antwortpräfix im Chat anzeigen", "Non-markdown strings": "Nicht-Markdown-Strings", "separate with commas w/o space between": "getrennt durch Kommas ohne Leerzeichen dazwischen", "Custom Stopping Strings": "Benutzerdefinierte Stoppzeichenfolgen", "JSON serialized array of strings": "JSON serialisierte Reihe von Zeichenfolgen", "Replace Macro in Stop Strings": "Makro in benutzerdefinierten Stoppzeichenfolgen ersetzen", "Auto-Continue": "Automatisch fortsetzen", "Allow for Chat Completion APIs": "Erlaube Chat-Vervollständigungs-APIs", "Target length (tokens)": "Ziel-Länge (Tokens)", "World Info": "Weltinformation", "Locked = World Editor will stay open": "Verriegelt = Welt-Editor ble<PERSON><PERSON> geöffnet", "Worlds/Lorebooks": "Welten/Lorebooks", "Active World(s) for all chats": "Aktive Welt(en) für alle Chats", "-- World Info not found --": "-- Weltinformation nicht gefunden --", "Global World Info/Lorebook activation settings": "Global World Info/Lorebook Aktivierungseinstellungen", "Click to expand": "Zum erweitern klicken", "Scan Depth": "Scan-T<PERSON>e", "Context %": "Kontext %", "Budget Cap": "Budgetgrenze", "(0 = disabled)": "(0 = deaktiviert)", "Scan chronologically until reached min entries or token budget.": "Chronologisch scannen, bis die Mindesteinträge oder das Token-Budget erreicht sind.", "Min Activations": "Mindestanzahl Aktivierungen", "Max Depth": "Maximale Tiefe", "(0 = unlimited, use budget)": "(0 = un<PERSON><PERSON><PERSON><PERSON>, <PERSON> nutzen)", "Insertion Strategy": "Einfügestrategie", "Sorted Evenly": "Gleichm<PERSON><PERSON><PERSON> sortiert", "Character Lore First": "Charakter<PERSON><PERSON><PERSON>", "Global Lore First": "Globale Lore <PERSON>", "Entries can activate other entries by mentioning their keywords": "Einträge können andere Einträge aktivieren, indem sie ihre Schlüsselwörter erwähnen", "Recursive Scan": "Rekursive Suche", "Lookup for the entry keys in the context will respect the case": "Die Suche nach den Eintragsschlüsseln im Kontext wird die Groß- und Kleinschreibung berücksichtigen", "Case Sensitive": "Groß-/Kleinschreibung beachten", "If the entry key consists of only one word, it would not be matched as part of other words": "<PERSON><PERSON> der Eintragsschlüssel nur aus einem Wort besteht, wird er nicht als Teil anderer Wörter abgeglichen", "Match Whole Words": "Ganze Wörter abgleichen", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "Für die Einschlussgruppenfilterung werden nur die Einträge mit den meisten Schlüsselübereinstimmungen ausgewählt.", "Use Group Scoring": "Gruppenwertung verwenden", "Alert if your world info is greater than the allocated budget.": "<PERSON><PERSON>, wenn Ihre Weltinformationen das zugewiesene Budget überschreiten.", "Alert On Overflow": "Warnung bei Überlauf", "New": "<PERSON>eu", "or": "oder", "--- Pick to Edit ---": "--- <PERSON><PERSON> Bearbeiten auswählen ---", "Rename World Info": "Weltinfo umbenennen", "Open all Entries": "Alle Einträge öffnen", "Close all Entries": "Alle Einträge schließen", "New Entry": "<PERSON><PERSON><PERSON> Eintrag", "Fill empty Memo/Titles with Keywords": "<PERSON><PERSON>/Titel mit Schlüsselwörtern füllen", "Import World Info": "Weltinfo importieren", "Export World Info": "Weltinfo exportieren", "Duplicate World Info": "Weltinfo duplizieren", "Delete World Info": "Weltinfo löschen", "Search...": "Suchen...", "Search": "<PERSON><PERSON>", "Priority": "Priorität", "Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Title A-Z": "Titel A-Z", "Title Z-A": "Titel Z-A", "Tokens ↗": "Token ↗", "Tokens ↘": "Token ↘", "Depth ↗": "Tiefe ↗", "Depth ↘": "Tiefe ↘", "Order ↗": "Reihenfolge ↗", "Order ↘": "Reihenfolge ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "Auslöser% ↗", "Trigger% ↘": "Auslöser% ↘", "Refresh": "Aktualisieren", "User Settings": "Benutzereinstellungen", "Simple": "<PERSON><PERSON><PERSON>", "Advanced": "Fortgeschritten", "UI Language": "UI-Sprache", "Account": "Ko<PERSON>", "Admin Panel": "Administrationsmenü", "Logout": "Ausloggen", "Search Settings": "Sucheinstellungen", "UI Theme": "UI-Thema", "Import a theme file": "Ein Design-Datei importieren", "Export a theme file": "Ein Design-Datei exportieren", "Delete a theme": "Löschen eines Designs", "Update a theme file": "Ein Theme-Datei aktualisieren", "Save as a new theme": "Als neues Theme speichern", "Avatar Style:": "Avatar-Stil", "Circle": "Kreis", "Square": "Quadrat", "Rectangle": "<PERSON><PERSON><PERSON>", "Chat Style:": "Chat-Stil:", "Flat": "Flache\nBlasen\nDokument", "Bubbles": "Blasen", "Document": "Dokument", "Specify colors for your theme.": "Geben Sie Farben für Ihr Design an.", "Theme Colors": "Themenfarben", "Main Text": "Haupttext", "Italics Text": "Kursiver Text", "Underlined Text": "Unterstrichener Text", "Quote Text": "Zitattext", "Shadow Color": "Sc<PERSON>tenfarbe", "Chat Background": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "UI Background": "UI-Hintergrund", "UI Border": "UI-Rand", "User Message Blur Tint": "Benutzer-Nachrichten-Blur-Tönung", "AI Message Blur Tint": "KI-Nachrichten-Blur-Tönung", "Chat Width": "Chat-Breite", "Width of the main chat window in % of screen width": "Breite des Hauptchatfensters in % der Bildschirmbreite", "Font Scale": "Schriftskalierung", "Font size": "Schriftgröße", "Blur Strength": "Unschärfestärke", "Blur strength on UI panels.": "Unschärfestärke auf UI-Panels.", "Text Shadow Width": "Textschattenbreite", "Strength of the text shadows": "Stärke der Textschatten", "Disables animations and transitions": "Deaktiviert Animationen und Übergänge", "Reduced Motion": "Verringerte Bewegung", "removes blur from window backgrounds": "Entfernt Unschä<PERSON>e von Fensterhintergründen", "No Blur Effect": "<PERSON><PERSON>", "Remove text shadow effect": "Entferne den Textschatten-Effekt", "No Text Shadows": "<PERSON><PERSON>", "Reduce chat height, and put a static sprite behind the chat window": "Verringere die Chat-Höhe und setze einen statischen Sprite hinter das Chat-Fenster", "Waifu Mode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "Zeige immer die vollständige Liste der Nachrichtenaktionen-Kontextelemente für Chat-Nachrichten an, anstatt sie hinter '...' zu verstecken", "Auto-Expand Message Actions": "Automatische Erweiterung von Nachrichtenaktionen", "Alternative UI for numeric sampling parameters with fewer steps": "Alternative UI für numerische Probenparameter mit weniger Schritten", "Zen Sliders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Entirely unrestrict all numeric sampling parameters": "Völlig alle numerischen Probenparameter freigeben", "Mad Lab Mode": "Verrückter Labor-Modus", "Time the AI's message generation, and show the duration in the chat log": "Zeit die Nachrichtengenerierung des KI und zeige die Dauer im Chat-Log", "Message Timer": "Nachrichten-Timer", "Show a timestamp for each message in the chat log": "Zeige einen Zeitstempel für jede Nachricht im Chat-Log", "Chat Timestamps": "Chat-Zeitstempel", "Show an icon for the API that generated the message": "Zeige ein Symbol für die API, die die Nachricht generiert hat", "Model Icon": "Modell-Icon", "Show sequential message numbers in the chat log": "Zeige aufeinanderfolgende Nachrichtennummern im Chat-Log", "Message IDs": "Nachrichten-IDs", "Hide avatars in chat messages.": "Avatare in Chatnachrichten verbergen.", "Hide Chat Avatars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>", "Show the number of tokens in each message in the chat log": "Zeige die Anzahl der Tokens in jeder Nachricht im Chat-Log", "Show Message Token Count": "Anzahl der Nachrichten-Token anzeigen", "Single-row message input area. Mobile only, no effect on PC": "Einzeln Zeile Nachrichteneingabebereich. Nur auf Mobilgeräten, keine Auswirkungen auf PC", "Compact Input Area (Mobile)": "Kompakter Eingabebereich (Mobil)", "In the Character Management panel, show quick selection buttons for favorited characters": "Zeige im Charakter-Management-Panel Schnellauswahlknöpfe für favorisierte Charaktere", "Characters Hotswap": "Charaktere Hotswap", "Enable magnification for zoomed avatar display.": "Aktivieren Sie die Vergrößerung für die vergrößerte Avatar-Anzeige.", "Avatar Hover Magnification": "Avatar-Hover-Vergrößerung", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "Aktiviert einen Vergrößerungseffekt beim <PERSON>, wenn Sie den gezoomten Avatar anzeigen, nachdem Sie im Chat auf das Bild eines Avatars geklickt haben.", "Show tagged character folders in the character list": "Zeige markierte Charakterordner in der Charakterliste", "Tags as Folders": "Tags als Ordner", "Tags_as_Folders_desc": "Letzte Änderung: Tags müssen im Tag-Management-Menü als Ordner markiert sein, um als solche angezeigt zu werden. Klicken Si<PERSON> hier, um es aufzurufen.", "Character Handling": "Charakterbehandlung", "If set in the advanced character definitions, this field will be displayed in the characters list.": "<PERSON><PERSON> in den erweiterten Charakterdefinitionen festgelegt, wird dieses <PERSON> in der Charakterliste angezeigt.", "Char List Subheader": "Zeichenliste - Unterüberschrift", "Character Version": "Charakterversion", "Created by": "<PERSON><PERSON><PERSON><PERSON> von", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "Verwende Fuzzy-Matching und suche Charaktere in der Liste nach allen Datenfeldern, nicht nur nach einem Namens-Substring", "Advanced Character Search": "Erweiterte Charakter-Suche", "If checked and the character card contains a prompt override (System Prompt), use that instead": "<PERSON>n aktiviert und die Charakterkarte eine Prompt-Überschreibung enthält (System-Prompt), verwende stattdessen diese", "Prefer Character Card Prompt": "Bevorzuge Charakterkarten-Prompt", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "<PERSON>n aktiviert und die Charakterkarte eine Jailbreak-Überschreibung enthält (Post-History-Instruction), verwende stattdessen diese", "Prefer Character Card Jailbreak": "Bevorzuge Charakterkarten-Jailbreak", "never_resize_avatars_tooltip": "Vermeiden Sie das Zuschneiden und Ändern der Größe importierter Zeichenbilder. <PERSON><PERSON>, wird die Größe auf 512 x 768 zugeschnitten/angepasst.", "Never resize avatars": "<PERSON><PERSON>e ni<PERSON> verklein<PERSON>", "Show actual file names on the disk, in the characters list display only": "Zeige tatsächliche Dateinamen auf der Festplatte, nur in der Anzeige der Charakterliste", "Show avatar filenames": "Avatar-Dateinamen anzeigen", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "Aufforderung zum Importieren eingebetteter Karten-Tags beim Importieren von Charakteren. Andernfalls werden eingebettete Tags ignoriert", "Import Card Tags": "Karten-Tags importieren", "Hide character definitions from the editor panel behind a spoiler button": "Verstecke Charakterdefinitionen im Editor-Panel hinter einem Spoiler-<PERSON><PERSON>", "Spoiler Free Mode": "Spoilerf<PERSON>ier Modus", "Miscellaneous": "Verschiedenes", "Reload and redraw the currently open chat": "Lade den aktuell geöffneten Chat neu und zeichne ihn neu", "Reload Chat": "Chat neu laden", "Debug Menu": "Debug-Menü", "Smooth Streaming": "Reibungsloses Streaming", "Experimental feature. May not work for all backends.": "Experimentelle Funktion. Funktioniert möglicherweise nicht für alle Backends.", "Slow": "Langsam", "Fast": "<PERSON><PERSON><PERSON>", "Play a sound when a message generation finishes": "Spiele einen Ton, wenn die Nachrichtengenerierung abgeschlossen ist", "Message Sound": "Nachrichtenklang", "Only play a sound when ST's browser tab is unfocused": "Spiele nur einen Ton, wenn der Browser-Tab von ST nicht im Fokus ist", "Background Sound Only": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reduce the formatting requirements on API URLs": "Reduziere die Formatierungsanforderungen für API-URLs", "Relaxed API URLS": "Entspannte API-URLs", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "Frage nach dem Importieren von Weltinfos/Lorebooks für jeden neuen Charakter mit eingebettetem Lorebook. Wenn nicht ausgewählt, wird stattdessen eine kurze Nachricht angezeigt", "Lorebook Import Dialog": "Lorebook-Import-Dialog", "Restore unsaved user input on page refresh": "<PERSON>elle nicht gespeicherte Benutzereingaben beim Aktualisieren der Seite wieder her", "Restore User Input": "Benutzereingabe wiederherstellen", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "Ermögliche das Neupositionieren bestimmter UI-Elemente durch Ziehen. Nur auf dem PC, keine Auswirkungen auf Mobilgeräte", "Movable UI Panels": "Verschiebbare UI-Panel", "MovingUI preset. Predefined/saved draggable positions": "MovingUI-Voreinstellung. Vordefinierte/gespeicherte verschiebbare Positionen", "MUI Preset": "MUI-Voreinstellung", "Save movingUI changes to a new file": "Speichere MovingUI-Änderungen in einer neuen Datei", "Reset MovingUI panel sizes/locations.": "Setzen Sie die Größen/Positionen der MovingUI-Bedienfelder zurück.", "Apply a custom CSS style to all of the ST GUI": "Wende einen benutzerdefinierten CSS-Stil auf die gesamte ST-Benutzeroberfläche an", "Custom CSS": "Benutzerdefiniertes CSS", "Expand the editor": "<PERSON>rweitern Sie den Editor", "Chat/Message Handling": "Chat-/Nachrichtenbehandlung", "# Messages to Load": "# <PERSON><PERSON> ladende Nachricht", "The number of chat history messages to load before pagination.": "Die Anzahl der Chatverlaufsnachrichten, die vor der Paginierung geladen werden sollen.", "(0 = All)": "(0 = Alle)", "Streaming FPS": "Streaming FPS", "Update speed of streamed text.": "Aktualisierungsgeschwindigkeit des gestreamten Textes.", "Example Messages Behavior": "<PERSON><PERSON><PERSON><PERSON> von Beispielnachrichten", "Gradual push-out": "Allmähliches Herausschieben", "Always include examples": "Immer Beispiele einbeziehen", "Never include examples": "<PERSON><PERSON> e<PERSON>", "Send on Enter": "<PERSON><PERSON> <PERSON><PERSON> senden", "Disabled": "Deaktiviert", "Automatic (PC)": "<PERSON>mat<PERSON> (PC)", "Press Send to continue": "<PERSON><PERSON><PERSON>, um fortzufahren", "Show a button in the input area to ask the AI to continue (extend) its last message": "<PERSON><PERSON>ge einen Button im Eingabebereich, um die KI zu bitten, ihre letzte Nachricht fortzusetzen (zu erweitern)", "Quick 'Continue' button": "<PERSON><PERSON><PERSON> '<PERSON>'-Schaltfläche", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "Zeige Pfeilbuttons auf der letzten In-Chat-Nachricht, um alternative KI-Antworten zu generieren. Sowohl auf PC als auch auf Mobilgeräten", "Swipes": "Wischgesten", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "Ermögliche das Verwenden von Wischgesten auf der letzten In-Chat-Nachricht, um die Wischgenerierung auszulösen. Nur auf Mobilgeräten, keine Auswirkungen auf PCs", "Gestures": "Gesten", "Auto-load Last Chat": "Letzten Chat automatisch laden", "Auto-scroll Chat": "Chat automatisch scrollen", "Save edits to messages without confirmation as you type": "Speichere Änderungen an Nachrichten ohne Bestätigung während du tippst", "Auto-save Message Edits": "Nachrichtenänderungen automatisch speichern", "Confirm message deletion": "Löschung der Nachricht bestätigen", "Auto-fix Markdown": "Markdown automatisch reparieren", "Disallow embedded media from other domains in chat messages": "Eingebettete Medien von anderen Domänen in Chat-Nachrichten nicht zu<PERSON>en.", "Forbid External Media": "Externe Medien verbieten", "Allow {{char}}: in bot messages": "Erlaube {{char}}: in Bot-Nachrichten", "Allow {{user}}: in bot messages": "Erlaube {{user}}: in Bot-Nachrichten", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "Überspringe die Codierung von  und  Zeichen im Nachrichtentext, was e<PERSON> <PERSON><PERSON><PERSON><PERSON> von HTML-Markup sowie <PERSON> zu<PERSON>t", "Show tags in responses": "Tags in Antworten anzeigen", "Allow AI messages in groups to contain lines spoken by other group members": "Ermögliche es AI-Nachrichten in Gruppen, <PERSON><PERSON><PERSON> zu enthalten, die von anderen Gruppenmitgliedern gesprochen wurden", "Relax message trim in Groups": "Nachrichtenbeschnitt in Gruppen entspannen", "Log prompts to console": "Protokolliere Aufforderungen in die Konsole", "Requests logprobs from the API for the Token Probabilities feature": "<PERSON><PERSON> von der API für die Funktion Token-Wahrscheinlichkeiten an", "Request token probabilities": "Token-Wahrscheinlichkeiten anfordern", "Automatically reject and re-generate AI message based on configurable criteria": "Automatisch AI-Nachricht ablehnen und basierend auf konfigurierbaren Kriterien erneut generieren", "Auto-swipe": "Automatisches Wischen", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "Aktiviere die Auto-Wisch-Funktion. Einstellungen in diesem Abschnitt haben nur dann Auswirkungen, wenn das automatische Wischen aktiviert ist", "Minimum generated message length": "Minimale generierte Nachrichtenlänge", "If the generated message is shorter than these many characters, trigger an auto-swipe": "Wenn die generierte Nachricht kürzer ist als diese, löse automatisches Wischen aus", "Blacklisted words": "Verbotene Wörter", "words you dont want generated separated by comma ','": "<PERSON><PERSON><PERSON>, die du nicht generiert haben möchtest, durch Komma ',' getrennt", "Blacklisted word count to swipe": "An<PERSON><PERSON> der verbotenen Wörter, um zu wischen", "Minimum number of blacklisted words detected to trigger an auto-swipe": "Mindestanzahl von erkannten verbotenen Wörtern, um eine automatische Wischbewegung auszulösen", "AutoComplete Settings": "AutoVervollständigen-Einstellungen", "Automatically hide details": "Details automatisch ausblenden", "Determines how entries are found for autocomplete.": "Best<PERSON><PERSON><PERSON>, wie Einträge für die Autovervollständigung gefunden werden.", "Autocomplete Matching": "Da<PERSON> passend", "Starts with": "Beginnt mit", "Includes": "<PERSON><PERSON><PERSON><PERSON>", "Fuzzy": "<PERSON><PERSON><PERSON><PERSON>", "Sets the style of the autocomplete.": "Legt den Stil der automatischen Vervollständigung fest.", "Autocomplete Style": "Stil", "Follow Theme": "Thema folgen", "Dark": "<PERSON><PERSON><PERSON>", "Sets the font size of the autocomplete.": "Legt die Schriftgröße der Autovervollständigung fest.", "Sets the width of the autocomplete.": "Legt die Breite der Autovervollständigung fest.", "Autocomplete Width": "Breite", "chat input box": "Chat-Eingabefeld", "entire chat width": "gesamte Chatbreite", "full window width": "volle Fensterbreite", "STscript Settings": "STscript-Einstellungen", "Sets default flags for the STscript parser.": "Legt Standardflags für den STscript-Parser fest.", "Parser Flags": "Parser-Flags", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "Wechseln Sie zu einer strengeren Escape-Methode, bei der alle Trennzeichen mit einem Backslash und auch Backslashs mit einem Escape-Zeichen versehen werden können.", "STRICT_ESCAPING": "STRENGES_ESCAPING", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "Ersetzen Sie alle {{getvar::}}- und {{getglobalvar::}}-<PERSON><PERSON><PERSON> durch gültige Variablen, um eine doppelte Makroersetzung zu vermeiden.", "REPLACE_GETVAR": "REPLACE_GETVAR", "Change Background Image": "Hintergrundbild ändern", "Filter": "Filter", "Automatically select a background based on the chat context": "Automatisch einen Hintergrund basierend auf dem Chatkontext auswählen", "Auto-select": "Automatische Auswahl", "System Backgrounds": "Systemhintergründe", "Chat Backgrounds": "Chat-Hintergründe", "bg_chat_hint_1": "Chat-Hintergründe generiert mit dem", "bg_chat_hint_2": "Die Erweiterung wird hier angezeigt.", "Extensions": "Erweiterungen", "Notify on extension updates": "Benachrichtigen bei Erweiterungsaktualisierungen", "Manage extensions": "Erweiterungen verwalten", "Import Extension From Git Repo": "Erweiterung aus Git-Repository importieren", "Install extension": "Erweiterung installieren", "Extras API:": "Extras API:", "Auto-connect": "Automatisch verbinden", "Extras API URL": "Extras API URL", "Extras API key (optional)": "Zusätzlicher API-Schlüssel (optional)", "Persona Management": "Persönlichkeitsverwaltung", "How do I use this?": "Wie benutze ich das?", "Click for stats!": "Klick für Statistiken!", "Usage Stats": "Nutzungsstatistiken", "Backup your personas to a file": "Speicher deine Persönlichkeiten in einer Datei.", "Backup": "Backup", "Restore your personas from a file": "Stell deine Per<PERSON>önlichkeiten aus einer Datei wieder her.", "Restore": "Wiederherstellen", "Create a dummy persona": "<PERSON><PERSON><PERSON> e<PERSON> Dummy-<PERSON>a", "Create": "<PERSON><PERSON><PERSON><PERSON>", "Toggle grid view": "Rasteransicht umschalten", "No persona description": "[<PERSON><PERSON>]", "Name": "Name", "Enter your name": "Gib deinen <PERSON>n ein", "Click to set a new User Name": "<PERSON><PERSON><PERSON>, um einen neuen Benutzernamen festzulegen", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "<PERSON><PERSON><PERSON>, um deine ausgewählte Persona für den aktuellen Chat zu sperren. <PERSON>lick<PERSON> erneut, um die Sperre zu entfernen.", "Click to set user name for all messages": "<PERSON><PERSON><PERSON>, um für alle Nachrichten einen Benutzernamen festzulegen", "Persona Description": "Persönlichkeitsbeschreibung", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "Beispiel: [{{user}} ist ein 28-jähriges rumänisches Katzenmädchen.]", "Tokens persona description": "Beschreibung der Tokens-Persona", "Position:": "Position:", "In Story String / Prompt Manager": "In Story-String / Prompt-Manager", "Top of Author's Note": "Oberteil der Autorennotiz", "Bottom of Author's Note": "Unterteil der Autorennotiz", "In-chat @ Depth": "<PERSON><PERSON> @ Tiefe", "Depth:": "Tiefe:", "Role:": "Rolle:", "System": "System", "User": "<PERSON><PERSON><PERSON>", "Assistant": "Assistent", "Show notifications on switching personas": "Benachrichtigungen beim Wechseln von Persönlichkeiten anzeigen", "Character Management": "Charakterverwaltung", "Locked = Character Management panel will stay open": "Verriegelt = Charakterverwaltungsfeld bleibt geöffnet", "Select/Create Characters": "Charaktere auswählen/erstellen", "Favorite characters to add them to HotSwaps": "Favoritencharaktere hinzufügen, um sie zu HotSwaps hinzuzufügen", "Token counts may be inaccurate and provided just for reference.": "Token-Zählungen können ungenau sein und dienen nur zur Referenz.", "Total tokens": "Token insgesamt", "Calculating...": "<PERSON><PERSON><PERSON><PERSON>ng…", "Tokens": "Tokens", "Permanent tokens": "Permanente Token", "Permanent": "<PERSON><PERSON><PERSON>", "About Token 'Limits'": "Über Token-Limits", "Toggle character info panel": "Zeicheninformationsfeld umschalten", "Name this character": "<PERSON><PERSON> <PERSON><PERSON> Charakter einen Namen", "extension_token_counter": "Token:", "Click to select a new avatar for this character": "<PERSON><PERSON><PERSON>, um einen neuen Avatar für diesen Charakter auszuwählen", "Add to Favorites": "<PERSON><PERSON> <PERSON><PERSON> hinzufügen", "Advanced Definition": "Erweiterte Definition", "Character Lore": "Charakterhin<PERSON>grund", "Chat Lore": "Chat-Überlieferung", "Export and Download": "Exportieren und Herunterladen", "Duplicate Character": "<PERSON><PERSON><PERSON>", "Create Character": "<PERSON><PERSON><PERSON>", "Delete Character": "Charakter löschen", "More...": "Mehr...", "Link to World Info": "Link zur Weltinfo", "Import Card Lore": "Karten-Lore importieren", "Scenario Override": "Szenarioüberschreibung", "Convert to Persona": "In Persona konvertieren", "Rename": "Umbenennen", "Link to Source": "<PERSON> Quelle", "Replace / Update": "Ersetzen / Aktualisieren", "Import Tags": "Tags importieren", "Search / Create Tags": "Tags suchen/erstellen", "View all tags": "Alle Tags anzeigen", "Creator's Notes": "Schöpfernotizen", "Show / Hide Description and First Message": "Beschreibung und erste Nachricht anzeigen/verbergen", "Character Description": "Charakterbeschreibung", "Click to allow/forbid the use of external media for this character.": "<PERSON><PERSON><PERSON>, um die Verwendung externer Medien für diesen Charakter zuzulassen/zu verbieten.", "Ext. Media": "Ext. Medien", "Describe your character's physical and mental traits here.": "Beschreibe hier die physischen und mentalen Eigenschaften deines Charakters.", "First message": "<PERSON><PERSON><PERSON>", "Click to set additional greeting messages": "<PERSON><PERSON><PERSON>, um zusätzliche Begrüßungsnachrichten festzulegen", "Alt. Greetings": "Alt. Grüße", "This will be the first message from the character that starts every chat.": "Dies wird die erste Nachricht des Charakters sein, die jeden Chat startet.", "Group Controls": "Gruppensteuerung", "Chat Name (Optional)": "Chatname (optional)", "Click to select a new avatar for this group": "<PERSON><PERSON><PERSON>, um einen neuen Avatar für diese Gruppe auszuwählen", "Group reply strategy": "Strategie für Gruppenantworten", "Natural order": "Natürliche Reihenfolge", "List order": "Listenreihenfolge", "Group generation handling mode": "Gruppengenerierungs-Handhabungsmodus", "Swap character cards": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>chen", "Join character cards (exclude muted)": "Charakterkarten beitreten (Stummschaltung ausschließen)", "Join character cards (include muted)": "Charakterkarten beitreten (auch stummgeschaltet)", "Inserted before each part of the joined fields.": "Wird vor jedem Teil der verbundenen Felder eingefügt.", "Join Prefix": "Präfix beitreten", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "<PERSON>n „Charakterkarten zusammenfügen“ ausgewählt ist, werden alle entsprechenden Felder der Charaktere zusammengefügt. Das bedeutet, dass im Story-String beispielsweise alle Charakterbeschreibungen zu einem großen Text zusammengefügt werden. Wen<PERSON> <PERSON>, dass diese Felder getrennt werden, können Sie hier ein Präfix oder Suffix definieren. Dieser Wert unterstützt normale Makros und ersetzt außerdem {{char}} durch den Namen des entsprechenden Charakters und <FIELDNAME> durch den Namen des Teils (z. B.: Beschreibung, Persönlichkeit, Szenario usw.).", "Inserted after each part of the joined fields.": "Wird nach jedem Teil der verbundenen Felder eingefügt.", "Join Suffix": "Join-<PERSON><PERSON><PERSON>", "Set a group chat scenario": "Setze ein Gruppenchat-Szenario", "Click to allow/forbid the use of external media for this group.": "<PERSON><PERSON><PERSON>, um die Verwendung externer Medien für diese Gruppe zuzulassen/zu verbieten.", "Restore collage avatar": "Collage-<PERSON><PERSON> wied<PERSON>hers<PERSON>len", "Allow self responses": "Selbstantworten erlauben", "Auto Mode": "Automatikmodus", "Auto Mode delay": "Auto-Modus-Verzögerung", "Hide Muted Member Sprites": "Stummgeschaltete Mitglieder-Sprites verbergen", "Current Members": "Aktuelle Mitglieder", "Add Members": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Create New Character": "Neuen Charakter erstellen", "Import Character from File": "Charakter aus Datei importieren", "Import content from external URL": "Inhalt von externer URL importieren", "Create New Chat Group": "Neue Chatgruppe erstellen", "Characters sorting order": "Sortierreihenfolge der Charaktere", "A-Z": "A-Z", "Z-A": "Z-A", "Newest": "Neueste", "Oldest": "Älteste", "Favorites": "<PERSON><PERSON>", "Recent": "<PERSON>eu", "Most chats": "<PERSON><PERSON>", "Least chats": "<PERSON><PERSON><PERSON>", "Most tokens": "Die meisten Tokens", "Least tokens": "Die wenigsten Tokens", "Random": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Toggle character grid view": "Rasteransicht des Charakters umschalten", "Bulk_edit_characters": "Massenbearbei<PERSON><PERSON> von <PERSON>", "Bulk select all characters": "Massenauswahl aller Zeichen", "Bulk delete characters": "Massenlöschung von Charakteren", "popup-button-save": "Speichern", "popup-button-yes": "<PERSON>a", "popup-button-no": "NEIN", "popup-button-cancel": "Stornieren", "popup-button-import": "Importieren", "Advanced Definitions": "Erweiterte Definitionen", "Prompt Overrides": "Eingabeaufforderungsüberschreibungen", "(For Chat Completion and Instruct Mode)": "(<PERSON><PERSON><PERSON>-Abschluss und Anweisungsmodus)", "Insert {{original}} into either box to include the respective default prompt from system settings.": "Füge {{original}} in eines der Felder ein, um den jeweiligen Standardprompt aus den Systemeinstellungen einzuschließen.", "Main Prompt": "Haupt-Prompt", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "Alle Inhalte hier ersetzen den Standard-Haupt-Prompt, der für diesen Charakter verwendet wird. (v2 Spezifikation: system_prompt)", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "Alle Inhalte hier ersetzen den standardmäßigen Jailbreak-Prompt, der für diesen Charakter verwendet wird. (v2 Spezifikation: post_history_instructions)", "Creator's Metadata (Not sent with the AI prompt)": "Metadaten des Erstellers (Nicht mit dem KI-Prompt gesendet)", "Creator's Metadata": "Metadaten des Erstellers", "(Not sent with the AI Prompt)": "(Nicht mit der KI-Eingabeaufforderung gesendet)", "Everything here is optional": "Alles hier ist optional", "(Botmaker's name / Contact Info)": "(Name des Bot-Erstellers / Kontaktinformationen)", "(If you want to track character versions)": "(<PERSON><PERSON> du Charakterversionen verfolgen möchtest)", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "(<PERSON><PERSON><PERSON><PERSON> den Bot, g<PERSON> <PERSON><PERSON><PERSON> zur Verwendung oder liste die Chatmodelle auf, auf denen er getestet wurde. Dies wird in der Charakterliste angezeigt.)", "Tags to Embed": "Tags zum Einbetten", "(Write a comma-separated list of tags)": "(Schreibe eine kommagetrennte Liste von Tags)", "Personality summary": "Persönlichkeitszusammenfassung", "(A brief description of the personality)": "(Eine kurze Beschreibung der Persönlichkeit)", "Scenario": "<PERSON><PERSON><PERSON>", "(Circumstances and context of the interaction)": "(Umstände und Kontext der Interaktion)", "Character's Note": "Anmerkung des Charakters", "(Text to be inserted in-chat @ designated depth and role)": "(Text wird im Chat in der angegebenen Tiefe und Rolle eingefügt)", "@ Depth": "@ Tiefe", "Role": "<PERSON><PERSON>", "Talkativeness": "Gesprächigkeit", "How often the character speaks in group chats!": "Wie oft der Charakter in Gruppenchats spricht!", "How often the character speaks in": "Wie oft spricht der Charakter in", "group chats!": "Gruppenchats!", "Shy": "Schüchtern", "Normal": "Normal", "Chatty": "Plaudernd", "Examples of dialogue": "Beispiele für Dialog", "Important to set the character's writing style.": "<PERSON><PERSON><PERSON><PERSON>, den Schreibstil des Charakters festzulegen.", "(Examples of chat dialog. Begin each example with START on a new line.)": "(Beispiele für Chatdialoge. Beginne jedes Beispiel mit START in einer neuen Zeile.)", "Save": "Speichern", "Chat History": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Chat": "Chat importieren", "Copy to system backgrounds": "In Systemhintergründe kopieren", "Rename background": "Hintergrund umbenennen", "Lock": "<PERSON><PERSON><PERSON>", "Unlock": "Freischalten", "Delete background": "Hintergrund löschen", "Chat Scenario Override": "Chat-Szenario-Überschreibung", "Remove": "Entfernen", "Type here...": "T<PERSON>pe hier...", "Chat Lorebook": "Chat Lorebook für", "Chat Lorebook for": "Chat Lorebook für", "chat_world_template_txt": "Eine ausgewählte Weltinformation wird an diesen Chat gebunden. Beim Generieren einer KI-Antwort wird sie mit den Einträgen aus globalen und Charakter-Lorebooks kombiniert.", "Select a World Info file for": "Wähle eine Weltinfo-Datei für", "Primary Lorebook": "Primäres Geschichtenbuch", "A selected World Info will be bound to this character as its own Lorebook.": "Eine ausgewählte Weltinfo wird diesem Charakter als sein eigenes Geschichtenbuch zugeordnet.", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "<PERSON>im Gene<PERSON> einer KI-Antwort wird sie mit den Einträgen aus einem globalen Weltinfo-Selektor kombiniert.", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "Beim Exportieren eines Charakters wird auch die ausgewählte Geschichtenbuchdatei in den JSON-Daten exportiert.", "Additional Lorebooks": "Zusätzliche Geschichtenbücher", "Associate one or more auxillary Lorebooks with this character.": "Verknüpfe ein oder mehrere zusätzliche Geschichtenbücher mit diesem Charakter.", "NOTE: These choices are optional and won't be preserved on character export!": "ACHTUNG: Diese Entscheidungen sind optional und werden beim Export des Charakters nicht beibehalten!", "Rename chat file": "Chatdatei umbenennen", "Export JSONL chat file": "JSONL-Chatdatei exportieren", "Download chat as plain text document": "Chat als einfaches Textdokument herunterladen", "Delete chat file": "Chatdatei löschen", "Use tag as folder": "Als Ordner markieren", "Hide on character card": "<PERSON><PERSON> ausblenden", "Delete tag": "Tag löschen", "Entry Title/Memo": "Eintragstitel/Memo", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled": "WI-Eintragstatus: 🔵 Konstant 🟢 Normal 🔗 Vektorisiert ❌ Deaktiviert", "WI_Entry_Status_Constant": "<PERSON><PERSON>ante", "WI_Entry_Status_Normal": "Normal", "WI_Entry_Status_Vectorized": "<PERSON>ek<PERSON><PERSON><PERSON>", "WI_Entry_Status_Disabled": "Deaktiviert", "T_Position": "↑Char: vor Charakterdefinitionen\n↓Char: nach Charakterdefinitionen\n↑AN: vor Anmerkungen des Autors\n↓AN: nach Anmerkungen des Autors\n@D: bei Tiefe", "Before Char Defs": "Vor Charakterdefinitionen", "After Char Defs": "<PERSON>ch Charakterdefinitionen", "Before EM": "↑EM", "After EM": "↓EM", "Before AN": "Vor AN", "After AN": "Nach <PERSON>", "at Depth System": "@D ⚙️", "at Depth User": "@D 👤", "at Depth AI": "@D 🤖", "Depth": "<PERSON><PERSON><PERSON>", "Order:": "Reihenfolge:", "Order": "Reihenfolge:", "Trigger %:": "Auslösen %:", "Probability": "Wahrscheinlichkeit", "Duplicate world info entry": "Doppelter Weltinfoeintrag", "Delete world info entry": "Weltinfoeintrag löschen", "Comma separated (required)": "Komma getrennt (er<PERSON>er<PERSON>)", "Primary Keywords": "Primäre <PERSON>hlüsselwö<PERSON>", "Keywords or Regexes": "Schlüsselwörter oder reguläre Ausdrücke", "Comma separated list": "Kommagetrennte Liste", "Switch to plaintext mode": "In den Klartextmodus wechseln", "Logic": "Logik", "AND ANY": "UND JEDE", "AND ALL": "UND ALLES", "NOT ALL": "NICHT ALLES", "NOT ANY": "NICHT JEDE", "(ignored if empty)": "(wird ignoriert, wenn leer)", "Optional Filter": "Optionaler Filter", "Keywords or Regexes (ignored if empty)": "Schlüsselwörter oder reguläre Ausdrücke (werden ignoriert, wenn sie leer sind)", "Comma separated list (ignored if empty)": "Durch Komma getrennte Liste (wird ignoriert, wenn sie leer ist)", "Use global setting": "Globale Einstellung verwenden", "Case-Sensitive": "Groß-/Kleinschreibung beachten", "Yes": "<PERSON>a", "No": "<PERSON><PERSON>", "Can be used to automatically activate Quick Replies": "Kann verwendet werden, um Schnellantworten automatisch zu aktivieren", "Automation ID": "Automatisierungs-ID", "( None )": "( <PERSON><PERSON> )", "Content": "Inhalt", "Exclude from recursion": "Aus Rekursion ausschließen", "Prevent further recursion (this entry will not activate others)": "Weitere Rekursion verhindern (dieser Eintrag aktiviert keine anderen)", "Delay until recursion (this entry can only be activated on recursive checking)": "Verzögerung bis zur Rekursion (dieser Eintrag kann nur bei rekursiver Prüfung aktiviert werden)", "What this keyword should mean to the AI, sent verbatim": "Was dieses Schlüsselwort für die KI bedeuten soll, wörtlich gesendet", "Filter to Character(s)": "Filtern auf Charakter(e)", "Character Exclusion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "-- Characters not found --": "-- Charaktere nicht gefunden --", "Inclusion Group": "Einschlussgruppe", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "Inklusionsgruppen stellen sicher, dass immer nur ein Eintrag aus einer Gruppe aktiviert wird, wenn mehrere ausgelöst werden. Unterstützt mehrere durch Kommas getrennte Gruppen. Dokumentation: World Info - Inclusion Group", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "Diesen Eintrag priorisieren: Wenn diese Option aktiviert ist, wird dieser Eintrag aus allen Auswahlen priorisiert. Wenn mehrere priorisiert sind, wird der Eintrag mit der höchsten „Reihenfolge“ ausgewählt.", "Only one entry with the same label will be activated": "Nur ein Eintrag mit demselben Label wird aktiviert", "A relative likelihood of entry activation within the group": "Eine relative Wahrscheinlichkeit der Eintrittsaktivierung innerhalb der Gruppe", "Group Weight": "Gruppengewicht", "Selective": "Selektiv", "Use Probability": "Wahrscheinlichkeit verwenden", "Add Memo": "<PERSON><PERSON>", "Text or token ids": "Text oder [Token-IDs]", "close": "schließen", "prompt_manager_edit": "<PERSON><PERSON><PERSON>", "prompt_manager_name": "Name", "A name for this prompt.": "Ein Name für diese Eingabeaufforderung.", "To whom this message will be attributed.": "Wem diese Nachricht zugeschrieben wird.", "AI Assistant": "KI-Assistent", "prompt_manager_position": "Position", "Next to other prompts (relative) or in-chat (absolute).": "Neben anderen Eingabeaufforderungen (relativ) oder im Chat (absolut).", "prompt_manager_relative": "Relativ", "prompt_manager_depth": "<PERSON><PERSON><PERSON>", "0 = after the last message, 1 = before the last message, etc.": "0 = nach der letzten Nachricht, 1 = vor der letzten Nachricht usw.", "Prompt": "Aufforderung", "The prompt to be sent.": "Die zu sendende Eingabeaufforderung.", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "Diese Eingabeaufforderung kann nicht durch Charakterkarten überschrieben werden, selbst wenn dies bevorzugt wird.", "prompt_manager_forbid_overrides": "Überschreibungen verbieten", "reset": "zurücksetzen", "save": "speichern", "This message is invisible for the AI": "Diese Nachricht ist für die KI unsichtbar", "Message Actions": "Nachrichtenaktionen", "Translate message": "Nachricht übersetzen", "Generate Image": "<PERSON><PERSON><PERSON> gene<PERSON>", "Narrate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Exclude message from prompts": "<PERSON><PERSON><PERSON><PERSON> von Aufforderungen ausschließen", "Include message in prompts": "Nachricht in Aufforderungen einschließen", "Embed file or image": "<PERSON>i oder Bild e<PERSON>", "Create checkpoint": "Checkpoint erstellen", "Create Branch": "<PERSON><PERSON><PERSON> erstellen", "Copy": "<PERSON><PERSON><PERSON>", "Open checkpoint chat": "Checkpoint-<PERSON><PERSON>", "Edit": "<PERSON><PERSON><PERSON>", "Confirm": "Bestätigen", "Copy this message": "Diese Nachricht kopieren", "Delete this message": "Diese Nachricht löschen", "Move message up": "<PERSON><PERSON>richt nach oben verschieben", "Move message down": "<PERSON><PERSON><PERSON>t nach unten verschieben", "Enlarge": "Vergrößern", "Welcome to SillyTavern!": "Will<PERSON>mmen bei SillyTavern!", "welcome_message_part_1": "<PERSON> das", "welcome_message_part_2": "Offizielle Dokumentation", "welcome_message_part_3": null, "welcome_message_part_4": "<PERSON><PERSON>", "welcome_message_part_5": "im Chat für Befehle und Makros.", "welcome_message_part_6": "Werden Sie Mitglied der", "Discord server": "Discord-Server", "welcome_message_part_7": "für Informationen und Ankündigungen.", "SillyTavern is aimed at advanced users.": "Si<PERSON><PERSON><PERSON><PERSON> richtet sich an fortgeschrittene Benutzer.", "If you're new to this, enable the simplified UI mode below.": "<PERSON>n Si<PERSON> damit noch nicht vertraut sind, aktivieren Sie unten den vereinfachten UI-Modus.", "Change it later in the 'User Settings' panel.": "Ändern Sie es später im Bereich „Benutzereinstellungen“.", "Enable simple UI mode": "Aktivieren Sie den einfachen UI-Modus", "Looking for AI characters?": "Suchen Sie nach KI-Charakteren?", "onboarding_import": "Importieren", "from supported sources or view": "aus unterstützten Quellen oder anzeigen", "Sample characters": "Beispielcharaktere", "Your Persona": "<PERSON><PERSON>", "Before you get started, you must select a persona name.": "<PERSON><PERSON>, müssen Si<PERSON> einen Persona-Namen auswählen.", "welcome_message_part_8": "Dies kann jederzeit geändert werden über die", "welcome_message_part_9": "Symbol.", "Persona Name:": "Personenname:", "Temporarily disable automatic replies from this character": "Automatische Antworten dieses Charakters vorübergehend deaktivieren", "Enable automatic replies from this character": "Automatische Antworten dieses Charakters aktivieren", "Trigger a message from this character": "<PERSON><PERSON> Nachricht von diesem Charakter auslösen", "Move up": "Nach oben verschieben", "Move down": "Nach unten verschieben", "View character card": "Charakterkarte anzeigen", "Remove from group": "Aus Gruppe entfernen", "Add to group": "Zur Gruppe hinzufügen", "Alternate Greetings": "Alternative Grüße", "Alternate_Greetings_desc": "Diese werden beim Starten eines neuen Chats als Wischbewegungen in der ersten Nachricht angezeigt.\nGruppenmitglieder können eine davon auswählen, um die Unterhaltung zu beginnen.", "Alternate Greetings Hint": "<PERSON>licken Si<PERSON> auf die Schaltfläche, um zu beginnen!", "(This will be the first message from the character that starts every chat)": "(Dies wird die erste Nachricht des Charakters sein, die jeden Chat startet)", "Forbid Media Override explanation": "Möglichkeit des aktuellen Charakters/der aktuellen Gruppe, externe Medien in Chats zu verwenden.", "Forbid Media Override subtitle": "Medien: <PERSON><PERSON><PERSON>, Videos, Audio. Extern: nicht auf dem lokalen Server gehostet.", "Always forbidden": "Immer verboten", "Always allowed": "<PERSON><PERSON> erlaubt", "View contents": "Inhalte anzeigen", "Remove the file": "Entfernen Sie die Datei", "Unique to this chat": "Einzigartig in diesem Chat", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "Prüfpunkte erben die Notiz von ihrem übergeordneten Element und können danach einzeln geändert werden.", "Include in World Info Scanning": "In World Info Scanning einbeziehen", "Before Main Prompt / Story String": "Vor der Hauptaufforderung / Story-String", "After Main Prompt / Story String": "Nach der Hauptaufforderung / Story-String", "as": "als", "Insertion Frequency": "Einfügungsfrequenz", "(0 = Disable, 1 = Always)": "(0 = Deaktivieren, 1 = Immer)", "User inputs until next insertion:": "Benutzereingaben bis zur nächsten Einfügung:", "Character Author's Note (Private)": "Anmerkung des Charakterautors (privat)", "Won't be shared with the character card on export.": "Wird beim Export nicht mit der Charakterkarte geteilt.", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "Wird automatisch als Autorennotiz für diesen Charakter hinzugefügt. Wird in Gruppen verwendet, kann aber nicht geändert werden, wenn ein Gruppenchat geöffnet ist.", "Use character author's note": "Notiz des Charakterautors verwenden", "Replace Author's Note": "<PERSON><PERSON><PERSON><PERSON>", "Default Author's Note": "Standard-Autorennotiz", "Will be automatically added as the Author's Note for all new chats.": "Wird automatisch als Autorennotiz für alle neuen Chats hinzugefügt.", "Chat CFG": "Chat-CFG", "1 = disabled": "1 = deak<PERSON>viert", "write short replies, write replies using past tense": "kurze Antworten schreiben, Antworten in der Vergangenheitsform schreiben", "Positive Prompt": "Positive Aufforderung", "Use character CFG scales": "Verwenden Sie Zeichen-CFG-Skalen", "Character CFG": "Charakter-CFG", "Will be automatically added as the CFG for this character.": "Wird automatisch als CFG für diesen Charakter hinzugefügt.", "Global CFG": "Globale CFG", "Will be used as the default CFG options for every chat unless overridden.": "Wird als Standard-CFG-Option für jeden Chat verwendet, sofern sie nicht überschrieben wird.", "CFG Prompt Cascading": "CFG-Eingabeaufforderung kaskadierend", "Combine positive/negative prompts from other boxes.": "Kombinieren Sie positive/negative Eingabeaufforderungen aus anderen Feldern.", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "Wenn Sie beispielsweise die Kontrollkästchen „Chat“, „Global“ und „Zeichen“ aktivieren, werden alle negativen Eingabeaufforderungen in einer durch Kommas getrennten Zeichenfolge kombiniert.", "Always Include": "Immer e<PERSON>chließen", "Chat Negatives": "Chat-Negative", "Character Negatives": "Negative Charaktere", "Global Negatives": "Globale Negative", "Custom Separator:": "Benutzerdefiniertes Trennzeichen:", "Insertion Depth:": "Einstecktiefe:", "Token Probabilities": "Token-Wahrscheinlichkeiten", "Select a token to see alternatives considered by the AI.": "<PERSON><PERSON>hlen Si<PERSON> ein Token aus, um die von der KI in Betracht gezogenen Alternativen anzuzeigen.", "Not connected to API!": "Nicht mit der API verbunden!", "Type a message, or /? for help": "<PERSON><PERSON>en Sie eine Nachricht ein, oder /?, um Hilfe zu erhalten.", "Continue script execution": "Skriptausführung fortsetzen", "Pause script execution": "Skriptausführung anhalten", "Abort script execution": "Skriptausführung abbrechen", "Abort request": "<PERSON><PERSON><PERSON> abbrechen", "Continue the last message": "Mit der letzten Nachricht fortfahren", "Send a message": "Eine Nachricht senden", "Close chat": "<PERSON><PERSON> sch<PERSON>ßen", "Toggle Panels": "Bedienfelder umschalten", "Back to parent chat": "Zurück zum übergeordneten Chat", "Save checkpoint": "Prüfpunkt speichern", "Convert to group": "In Gruppe umwandeln", "Start new chat": "Neuen Chat starten", "Manage chat files": "Chat<PERSON><PERSON> verwalten", "Delete messages": "Nachrichten löschen", "Regenerate": "Regenerieren", "Ask AI to write your message for you": "Bitten Sie die KI, <PERSON>hre Nachricht für Sie zu schreiben", "Impersonate": "<PERSON><PERSON><PERSON><PERSON>", "Continue": "<PERSON><PERSON>", "Bind user name to that avatar": "Benutzernamen an diesen Avatar binden", "Change persona image": "Personenbild ändern", "Select this as default persona for the new chats.": "Dies als Standard-Persona für die neuen Chats auswählen.", "Delete persona": "<PERSON><PERSON>", "These characters are the winners of character design contests and have outstandable quality.": "Diese Charaktere sind die Gewinner von Charakterdesign-Wettbewerben und haben eine herausragende Qualität.", "Contest Winners": "Gewinner des Wettbewerbs", "These characters are the finalists of character design contests and have remarkable quality.": "Diese Charaktere sind die Finalisten von Charakterdesign-Wettbewerben und haben eine bemerkenswerte Qualität.", "Featured Characters": "Ausgewählte Charaktere", "Attach a File": "Eine Date<PERSON>ngen", "Open Data Bank": "Offene Datenbank", "Enter a URL or the ID of a Fandom wiki page to scrape:": "Geben Sie eine URL oder die ID einer Fandom-Wiki-Seite zum Scrapen ein:", "Examples:": "Beispiele:", "Example:": "Beispiel:", "Single file": "<PERSON><PERSON><PERSON>", "All articles will be concatenated into a single file.": "Alle Artikel werden zu einer einzigen Datei zusammengefasst.", "File per article": "Datei pro Artikel", "Each article will be saved as a separate file.": "Nicht empfohlen. Jeder Artikel wird als separate Datei gespeichert.", "Data Bank": "Datenbank", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "Diese Dateien sind für Erweiterungen verfügbar, die Anhänge unterstützen (z. B. Vector Storage).", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "Unterstützte Dateitypen: Nur Text, PDF, Markdown, HTML, EPUB.", "Drag and drop files here to upload.": "<PERSON><PERSON><PERSON> hi<PERSON>her, um sie hochzuladen.", "Date (Newest First)": "Datum (Neueste zu<PERSON>t)", "Date (Oldest First)": "Datum (Älteste zuerst)", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z–A)", "Size (Smallest First)": "Größe (Kleinste zuerst)", "Size (Largest First)": "Größe (Größte zuerst)", "Bulk Edit": "Bulk-Bearbeitung", "Select All": "Wählen Sie Alle", "Select None": "Nichts ausgewählt", "Global Attachments": "Globale Anhänge", "These files are available for all characters in all chats.": "Diese Dateien sind für alle Charaktere in allen Chats verfügbar.", "Character Attachments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "These files are available the current character in all chats they are in.": "Diese Dateien sind für den aktuellen Charakter in allen Chats verfügbar, in denen er sich befindet.", "Saved locally. Not exported.": "Lokal gespeichert. Nicht exportiert.", "Chat Attachments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "These files are available to all characters in the current chat.": "<PERSON><PERSON> stehen allen Charakteren im aktuellen Chat zur Verfügung.", "Enter a base URL of the MediaWiki to scrape.": "Geben Sie eine Basis-URL des zu scrapenden MediaWiki ein.", "Don't include the page name!": "Geben Sie den Seitennamen nicht an!", "Enter web URLs to scrape (one per line):": "Geben Sie die zu scrapenden Web-URLs ein (eine pro Zeile):", "Enter a video URL to download its transcript.": "<PERSON>eben Sie eine Video-URL oder -ID ein, um das Transkript herunterzuladen.", "Expression API": "Lokal\nExtras\nLLM", "ext_sum_with": "Zusammenfassen mit:", "ext_sum_main_api": "Haupt-API", "ext_sum_current_summary": "Aktuelle Zusammenfassung:", "ext_sum_restore_previous": "Vorheriges wiederherstellen", "ext_sum_memory_placeholder": "Eine Zusammenfassung wird hier generiert...", "Trigger a summary update right now.": "Jetzt zusammenfassen", "ext_sum_force_text": "Jetzt zusammenfassen", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "Deaktivieren Sie automatische Zusammenfassungsaktualisierungen. Während der Pause bleibt die Zusammenfassung unverändert. Sie können dennoch eine Aktualisierung erzwingen, indem Sie auf die Schaltfläche „Jetzt zusammenfassen“ klicken (nur mit der Haupt-API verfügbar).", "ext_sum_pause": "Pause", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "Weltinfo und Anmerkung des Autors aus dem zusammenzufassenden Text weglassen. Hat nur Auswirkungen bei Verwendung der Haupt-API. Die Extras-API lässt WI/AN immer weg.", "ext_sum_no_wi_an": "<PERSON><PERSON> W<PERSON>/AN", "ext_sum_settings_tip": "Bearbeiten Sie die Zusammenfassungsaufforderung, die Einfügeposition usw.", "ext_sum_settings": "Zusammenfassungseinstellungen", "ext_sum_prompt_builder": "Eingabeaufforderungsgenerator", "ext_sum_prompt_builder_1_desc": "Die Erweiterung erstellt ihre eigene Eingabeaufforderung mit Nachrichten, die noch nicht zusammengefasst wurden. Blockiert den Chat, bis die Zusammenfassung generiert wurde.", "ext_sum_prompt_builder_1": "<PERSON><PERSON>, blockierend", "ext_sum_prompt_builder_2_desc": "Die Erweiterung erstellt ihre eigene Eingabeaufforderung mit Nachrichten, die noch nicht zusammengefasst wurden. Blockiert den Cha<PERSON> nicht, während die Zusammenfassung generiert wird. Nicht alle Backends unterstützen diesen Modus.", "ext_sum_prompt_builder_2": "<PERSON><PERSON>, nicht blockierend", "ext_sum_prompt_builder_3_desc": "Die Erweiterung verwendet den regulären Haupt-Prompt-Builder und fügt ihm die Zusammenfassungsanforderung als letzte Systemnachricht hinzu.", "ext_sum_prompt_builder_3": "Klassisch, blockierend", "Summary Prompt": "Zusammenfassungseingabeaufforderung", "ext_sum_restore_default_prompt_tip": "Standardeingabeaufforderung wiederherstellen", "ext_sum_prompt_placeholder": "Diese Eingabeaufforderung wird an AI gesendet, um die Erstellung der Zusammenfassung anzufordern. {{words}} wird in den Parameter „Anzahl der Wörter“ aufgelöst.", "ext_sum_target_length_1": "Länge der Zielzusammenfassung", "ext_sum_target_length_2": null, "ext_sum_target_length_3": "Wörter)", "ext_sum_api_response_length_1": "Länge der API-Antwort", "ext_sum_api_response_length_2": null, "ext_sum_api_response_length_3": "Token)", "ext_sum_0_default": "0 = Standard", "ext_sum_raw_max_msg": "[Raw] <PERSON><PERSON> Nachrichten pro Anfrage", "ext_sum_0_unlimited": "0 = unbegrenzt", "Update frequency": "Aktualisierungsfrequenz", "ext_sum_update_every_messages_1": "Aktualisieren alle", "ext_sum_update_every_messages_2": "Mitteilungen", "ext_sum_0_disable": "0 = deaktivieren", "ext_sum_auto_adjust_desc": "Versuchen Sie, das Intervall basierend auf den Chat-Metriken automatisch anzupassen.", "ext_sum_update_every_words_1": "Aktualisieren alle", "ext_sum_update_every_words_2": "<PERSON><PERSON><PERSON>", "ext_sum_both_sliders": "<PERSON>n beide Schieberegler ungleich Null sind, l<PERSON><PERSON> beide in ihren jeweiligen Intervallen Zusammenfassungsaktualisierungen aus.", "ext_sum_injection_template": "Injektionsvorlage", "ext_sum_memory_template_placeholder": "{{summary}} wird zum aktuellen Zusammenfassungsinhalt aufgelöst.", "ext_sum_injection_position": "Injektionsposition", "How many messages before the current end of the chat.": "Wie viele Nachrichten bis zum aktuellen Ende des Chats.", "ext_regex_title": "Regulärer Ausdruck", "ext_regex_new_global_script": "+ Allgemein", "ext_regex_new_scoped_script": "+ Umfang", "ext_regex_import_script": "Importieren", "ext_regex_global_scripts": "Globale Skripte", "ext_regex_global_scripts_desc": "<PERSON>ür alle Charaktere verfügbar. In den lokalen Einstellungen gespeichert.", "ext_regex_scoped_scripts": "Bereichsbezogene Skripte", "ext_regex_scoped_scripts_desc": "Nur für diesen Charakter verfügbar. In den Kartendaten gespeichert.", "Regex Editor": "Regex-Editor", "Test Mode": "Testmodus", "ext_regex_desc": "Regex ist ein Tool zum Suchen/<PERSON><PERSON><PERSON><PERSON> von Zeichenfolgen mithilfe regulärer Ausdrücke. Wenn Sie mehr erfahren möchten, klicken Sie auf das ? neben dem Titel.", "Input": "Eingang", "ext_regex_test_input_placeholder": "Hier e<PERSON>ben...", "Output": "Ausgabe", "ext_regex_output_placeholder": "<PERSON><PERSON>", "Script Name": "Skriptname", "Find Regex": "Regex suchen", "Replace With": "Ersetzen mit", "ext_regex_replace_string_placeholder": "Verwenden Sie {{match}}, um den übereinstimmenden Text aus dem Such-Regex oder $1, $2 usw. für Erfassungsgruppen einzuschließen.", "Trim Out": "Ausschneiden", "ext_regex_trim_placeholder": "Entfernt global alle unerwünschten Teile aus einer Regex-Übereinstimmung vor dem Ersetzen. Trennen Sie jedes Element durch eine Eingabetaste.", "ext_regex_affects": "Auswirkungen", "ext_regex_user_input": "Benutzereingabe", "ext_regex_ai_output": "KI-Ausgabe", "Slash Commands": "Schrägstrichbefehle", "ext_regex_min_depth_desc": "Bei Anwendung auf Eingabeaufforderungen oder die Anzeige wirkt sich dies nur auf Nachrichten aus, die mindestens N Ebenen tief sind. 0 = letzte Nachricht, 1 = vorletzte Nachricht usw. Zählt nur WI-Einträge @Depth und verwendbare Nachrichten, d. h. nicht versteckte oder Systemnachrichten.", "Min Depth": "<PERSON><PERSON>", "ext_regex_min_depth_placeholder": "Unbegrenzt", "ext_regex_max_depth_desc": "Bei Anwendung auf Eingabeaufforderungen oder die Anzeige wirkt es sich nur auf Nachrichten aus, die nicht tiefer als N Ebenen liegen. 0 = letzte Nachricht, 1 = vorletzte Nachricht usw. Zählt nur WI-Einträge @Depth und verwendbare Nachrichten, d. h. nicht versteckte oder Systemnachrichten.", "ext_regex_other_options": "Andere Optionen", "Only Format Display": "Nur Formatanzeige", "ext_regex_only_format_prompt_desc": "Der Chatverlauf ä<PERSON>t sich nicht, nur die Eingabeaufforderung beim Senden der Anfrage (bei der Generierung).", "Only Format Prompt (?)": "Nur Formataufforderung", "Run On Edit": "Ausführen beim <PERSON>ten", "ext_regex_substitute_regex_desc": "<PERSON><PERSON><PERSON><PERSON> Sie {{macros}} in Find Regex, bevor <PERSON> es ausführen", "Substitute Regex": "Regex ersetzen", "ext_regex_import_target": "Importieren nach:", "ext_regex_disable_script": "Skript deaktivieren", "ext_regex_enable_script": "Skript aktivieren", "ext_regex_edit_script": "<PERSON><PERSON><PERSON>t bearbeiten", "ext_regex_move_to_global": "Wechseln Sie zu globalen Skripten", "ext_regex_move_to_scoped": "Wechseln Sie zu bereichsbezogenen Skripts", "ext_regex_export_script": "Skript exportieren", "ext_regex_delete_script": "Skript löschen", "Trigger Stable Diffusion": "Stabile Diffusion auslösen", "sd_Yourself": "Se<PERSON>bst", "sd_Your_Face": "<PERSON><PERSON>", "sd_Me": "<PERSON><PERSON>", "sd_The_Whole_Story": "Die ganze Geschichte", "sd_The_Last_Message": "Die letzte Nachricht", "sd_Raw_Last_Message": "<PERSON><PERSON>e letz<PERSON> Nachrich<PERSON>", "sd_Background": "Hi<PERSON>grund", "Image Generation": "Bildgenerierung", "sd_refine_mode": "Erl<PERSON>bt die manuelle Bearbeitung von Eingabeaufforderungen, bevor diese an die Generierungs-API gesendet werden", "sd_refine_mode_txt": "Eingabeaufforderungen vor der Generierung bearbeiten", "sd_interactive_mode": "<PERSON><PERSON> von Nachrichten wie „Schicken Sie mir ein Bild von einer Katze“ werden automatisch Bilder generiert.", "sd_interactive_mode_txt": "Interak<PERSON><PERSON>", "sd_multimodal_captioning": "Verwenden Sie multimodale Untertitel, um Eingabeaufforderungen für Benutzer- und Charakterporträts basierend auf ihren Avataren zu generieren.", "sd_multimodal_captioning_txt": "Verwenden Sie multimodale Untertitel für Porträts", "sd_expand": "Automatisches Erweitern von Eingabeaufforderungen mithilfe des Textgenerierungsmodells", "sd_expand_txt": "Eingabeaufforderungen automatisch verbessern", "sd_snap": "Snap-Generierungsanforderungen mit einem erzwungenen Seitenverhältnis (Porträts, Hintergründe) zur nächsten bekannten Auflösung, wobe<PERSON> versucht wird, die absolute Pixelanzahl beizubehalten (empfohlen für SDXL).", "sd_snap_txt": "Snap automatisch angepasste Auflösungen", "Source": "<PERSON><PERSON>", "sd_auto_url": "Beispiel: {{auto_url}}", "Authentication (optional)": "Authentifizierung (optional)", "Example: username:password": "Beispiel: Benutzername:Passwort", "Important:": "Wichtig:", "sd_auto_auth_warning_1": "Führen Sie die SD Web UI mit dem", "sd_auto_auth_warning_2": "Flagge! Der Server muss vom SillyTavern-Hostcomputer aus erreichbar sein.", "sd_drawthings_url": "Beispiel: {{drawthings_url}}", "sd_drawthings_auth_txt": "Führen Sie die DrawThings-App mit aktiviertem HTTP-API-Schalter in der Benutzeroberfläche aus! Der Server muss vom SillyTavern-Hostcomputer aus zugänglich sein.", "sd_vlad_url": "Beispiel: {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "Der Server muss vom SillyTavern-Hostcomputer aus zugänglich sein.", "Hint: Save an API key in AI Horde API settings to use it here.": "Tipp: Speichern Sie einen API-Schlüssel in den AI Horde API-Einstellungen, um ihn hier zu verwenden.", "Allow NSFW images from Horde": "NSFW-<PERSON><PERSON><PERSON> <PERSON>", "Sanitize prompts (recommended)": "Eingabeaufforderungen bereinigen (empfohlen)", "Automatically adjust generation parameters to ensure free image generations.": "Passen Sie die Generierungsparameter automatisch an, um eine freie Bildgenerierung zu gewährleisten.", "Avoid spending Anlas": "Vermeiden Sie die Ausgabe von Anlas", "Opus tier": "(Opus-Stufe)", "View my Anlas": "<PERSON><PERSON>", "These settings only apply to DALL-E 3": "Diese Einstellungen gelten nur für DALL-E 3", "Image Style": "<PERSON><PERSON><PERSON><PERSON>", "Image Quality": "Bildqualität", "Standard": "Standard", "HD": "HD", "sd_comfy_url": "Beispiel: {{comfy_url}}", "Open workflow editor": "Workflow-Editor <PERSON><PERSON><PERSON>", "Create new workflow": "Neuen Workflow erstellen", "Delete workflow": "Workflow löschen", "Enhance": "<PERSON><PERSON><PERSON><PERSON>", "Refine": "Verfeinern", "Decrisper": "Entkrisper", "Sampling steps": "Schritte zur Probenahme ()", "Width": "Breite ()", "Height": "Höhe ()", "Resolution": "Auflösung", "Model": "<PERSON><PERSON>", "Sampling method": "Probenahmeverfahren", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON> (nicht alle Sampler werden unterstützt)", "SMEA versions of samplers are modified to perform better at high resolution.": "SMEA-<PERSON><PERSON> von Samplern werden modifiziert, um bei hoher Auflösung eine bessere Leistung zu erzielen.", "SMEA": "KMU", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "DYN-<PERSON><PERSON><PERSON> von SMEA-Samplern führen häufig zu einer vielfältigeren Ausgabe, können aber bei sehr hohen Auflösungen versagen.", "DYN": "DYN", "Scheduler": "Planer", "Restore Faces": "G<PERSON>chter wiederherstellen", "Hires. Fix": "Einstellungen. Fix", "Upscaler": "Upscaler", "Upscale by": "Gehoben durch", "Denoising strength": "Rauschunterdrückungsstärke", "Hires steps (2nd pass)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (2. <PERSON><PERSON><PERSON><PERSON>)", "Preset for prompt prefix and negative prompt": "Vorgabe für Prompt-Präfix und Negativ-Prompt", "Style": "Stil", "Save style": "Stil speichern", "Delete style": "<PERSON>il löschen", "Common prompt prefix": "Allgemeines Eingabeaufforderungspräfix", "sd_prompt_prefix_placeholder": "Verwenden Sie {prompt}, um anzugeben, wo die generierte Eingabeaufforderung eingefügt wird", "Negative common prompt prefix": "Negatives allgemeines Eingabeaufforderungspräfix", "Character-specific prompt prefix": "Zeichenspezifisches Eingabeaufforderungspräfix", "Won't be used in groups.": "Wird nicht in Gruppen verwendet.", "sd_character_prompt_placeholder": "Alle Merkmale, die den aktuell ausgewählten Charakter beschreiben. Werden nach einem gemeinsamen Eingabeaufforderungspräfix hinzugefügt.\nBeispiel: <PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON>, <PERSON><PERSON>, rosa <PERSON><PERSON>", "Character-specific negative prompt prefix": "Zeichenspezifisches negatives Eingabeaufforderungspräfix", "sd_character_negative_prompt_placeholder": "Alle Merkmale, die für das ausgewählte Zeichen nicht erscheinen sollen. Werden nach einem negativen allgemeinen Eingabeaufforderungspräfix hinzugefügt.\nBeispiel: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "Shareable": "<PERSON><PERSON><PERSON>", "Image Prompt Templates": "Vorlagen für Bildaufforderungen", "Vectors Model Warning": "<PERSON><PERSON> wird em<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> zu löschen, wenn das Modell während des Chats geändert wird. Andernfalls führt dies zu unterdurchschnittlichen Ergebnissen.", "Translate files into English before processing": "Übersetzen Sie die Dateien vor der Verarbeitung ins Englische", "Manager Users": "<PERSON><PERSON><PERSON> ver<PERSON><PERSON>", "New User": "<PERSON><PERSON><PERSON>", "Status:": "Status:", "Created:": "Erstellt:", "Display Name:": "Anzeigename:", "User Handle:": "Benutzername:", "Password:": "Passwort:", "Confirm Password:": "Bestätige das Passwort:", "This will create a new subfolder...": "Dad<PERSON><PERSON> wird im Verzeichnis /data/ ein neuer Unterordner mit dem Benutzerhandle als Ordnername erstellt.", "Current Password:": "Aktuelles Passwort:", "New Password:": "Neues Kennwort:", "Confirm New Password:": "Bestätige neues Passwort:", "Debug Warning": "Die Funktionen in dieser Kategorie sind nur für fortgeschrittene Benutzer. Klicken Si<PERSON> nichts an, wenn Sie sich über die Folgen nicht im Klaren sind.", "Execute": "Ausführen", "Are you sure you want to delete this user?": "Möchten Sie diesen Benutzer wirklich löschen?", "Deleting:": "Löschen:", "Also wipe user data.": "Löschen Sie auch Benutzerdaten.", "Warning:": "Warnung:", "This action is irreversible.": "Diese Aktion ist irreversibel.", "Type the user's handle below to confirm:": "Geben Sie zur Bestätigung unten den Benutzernamen ein:", "Import Characters": "Zeichen importieren", "Enter the URL of the content to import": "Geben Sie die URL des zu importierenden Inhalts ein", "Supported sources:": "Unterstützte Quellen:", "char_import_1": "Chu<PERSON><PERSON><PERSON><PERSON><PERSON> (Direktlink oder ID)", "char_import_example": "Beispiel:", "char_import_2": "<PERSON><PERSON> Lorebook (Direktlink oder ID)", "char_import_3": "JanitorAI-Charakter (Direktlink oder UUID)", "char_import_4": "Pygmalion.chat-<PERSON><PERSON><PERSON> (Direktlink oder UUID)", "char_import_5": "AICharacterCards.com-Cha<PERSON>ter (Direktlink oder ID)", "char_import_6": "Direkter PNG-Link (siehe", "char_import_7": "für erlaubte Hosts)", "char_import_8": "RisuRealm-Charakter (Direktlink)", "char_import_9": "Soulkyn<PERSON><PERSON><PERSON><PERSON> (Direktlink)", "Supports importing multiple characters.": "Unterstützt den Import mehrerer Zeichen.", "Write each URL or ID into a new line.": "Schreiben Sie jede URL oder ID in eine neue Zeile.", "Export for character": "Export für Zeichen", "Export prompts for this character, including their order.": "Exportieren Sie Eingabeaufforderungen für dieses Zeichen, einschließlich ihrer Reihenfolge.", "Export all": "Alles exportieren", "Export all your prompts to a file": "Exportieren Sie alle Eingabeaufforderungen in eine Datei", "Insert prompt": "Aufforderung einfügen", "Delete prompt": "Aufforderung löschen", "Import a prompt list": "Eine Aufforderungsliste importieren", "Export this prompt list": "Diese Aufforderungsliste exportieren", "Reset current character": "Aktuellen Charakter zurücksetzen", "New prompt": "Neue Aufforderung", "Prompts": "Aufforderungen", "Total Tokens:": "Gesamt-Token:", "prompt_manager_tokens": "Token", "Are you sure you want to reset your settings to factory defaults?": "Möchten Sie Ihre Einstellungen wirklich auf die Werkseinstellungen zurücksetzen?", "Don't forget to save a snapshot of your settings before proceeding.": "<PERSON><PERSON><PERSON><PERSON>, einen Schnappschuss Ihrer Einstellungen zu speichern, bevor <PERSON>hren.", "Settings Snapshots": "Einstellungs-Schnappschüsse", "Record a snapshot of your current settings.": "Zeichnen Sie einen Schnappschuss Ihrer aktuellen Einstellungen auf.", "Make a Snapshot": "<PERSON><PERSON> einen Schnappschuss", "Restore this snapshot": "<PERSON><PERSON><PERSON> diesen Snap<PERSON> wieder her", "Hi,": "Hallo,", "To enable multi-account features, restart the SillyTavern server with": "Um die Multi-Account-Funk<PERSON> zu aktivieren, starten Sie den SillyTavern-Server neu mit", "set to true in the config.yaml file.": "in der Datei config.yaml auf „true“ gesetzt.", "Account Info": "Kontoinformation", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "Um Ihren Benutzeravatar zu ändern, verwenden Sie die Schaltflächen unten oder wählen Sie eine Standardpersona im Menü „Persona-Verwaltung“ aus.", "Set your custom avatar.": "Legen Sie Ihren benutzerdefinierten Avatar fest.", "Remove your custom avatar.": "Entfernen Sie Ihren benutzerdefinierten Avatar.", "Handle:": "Handhaben:", "This account is password protected.": "Dieses Ko<PERSON> ist passwortgeschützt.", "This account is not password protected.": "Dieses <PERSON> ist nicht passwortgeschützt.", "Account Actions": "Kontoaktionen", "Change Password": "Kennwort ändern", "Manage your settings snapshots.": "Verwalten Sie Ihre Einstellungs-Snapshots.", "Download a complete backup of your user data.": "Laden Sie ein vollständiges Backup Ihrer Benutzerdaten herunter.", "Download Backup": "Backup herunt<PERSON><PERSON>n", "Danger Zone": "Gefahrenzone", "Reset your settings to factory defaults.": "Setzen Sie Ihre Einstellungen auf die Werkseinstellungen zurück.", "Reset Settings": "Einstellungen zurücksetzen", "Wipe all user data and reset your account to factory settings.": "Löschen Sie alle Benutzerdaten und setzen Sie Ihr Konto auf die Werkseinstellungen zurück.", "Reset Everything": "<PERSON>es zur<PERSON>", "Reset Code:": "Code zurücksetzen:", "Want to update?": "Möchten Sie aktualisieren?", "How to start chatting?": "Wie fange ich an zu chatten?", "Click _space": "<PERSON><PERSON><PERSON>", "and select a": "und wähle eine", "Chat API": "Chat-API", "and pick a character.": "und wähle einen Charakter aus.", "You can browse a list of bundled characters in the": "Sie können eine Liste der gebündelten Charaktere im", "Download Extensions & Assets": "Erweiterungen und Assets herunterladen", "menu within": "<PERSON><PERSON>", "Confused or lost?": "Verwirrt oder verloren?", "click these icons!": "klicke auf diese Symbole!", "in the chat bar": "in der Chatleiste", "SillyTavern Documentation Site": "SillyTavern-Dokumentationsseite", "Extras Installation Guide": "Zusätzlicher Installationsleitfaden", "Still have questions?": "Hast du immer noch Fragen?", "Join the SillyTavern Discord": "Trete dem SillyTavern Discord bei", "Post a GitHub issue": "Veröffentliche ein GitHub-Problem", "Contact the developers": "Kontaktiere die Entwickler"}