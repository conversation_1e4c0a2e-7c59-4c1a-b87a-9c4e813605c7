作用：
①一键小总结，所有模型通用。
②小总结内容累积过多时，还有总结的总结，称为大总结。
③一键取消隐藏10层楼，便于重新总结。

该方法基于【总结姬（作者luluz)】修改，
①最大亮点是**gemini可用**，
②新增了大总结的概念，
③总结失败时不会隐藏楼层，简化了操作。

原作者： <@1180888698066325594>  
                    <@1018750585790550086> 
 原贴地址：https://discord.com/channels/1134557553011998840/1323289056351883368
 

**小总结**
**下载与安装**
1、请到原贴中下载，原贴可能随时更新说明和文件。（QR文件是通用的，在第一个链接里下载。克劳德版有两个v**文件，小写v是正则，大写V是预设。）
2、安装以gemini举例：
2.1、导入预设。
①左上角菜单
②点击开始导入
③导入预设“Gemini Summary”



2.2、导入快速回复。
（快速回复=quick reply，简称QR）
①进去三方块的项里
②点开快速回复
③导入快速回复文件“总结（测试） (4).json”
④选中“总结（测试） (4)”，把图里的三个打钩✓的框都关闭开启一遍。




2.3、设置快速回复
①点开这三个点进入编辑页面（步骤图点这里→https://discord.com/channels/1134557553011998840/1334024498764320881/1334053855234621533）
②填写总结破限预设名的橙色部分为“Gemini Summary”。模型名称可以不填，该代码区域有中文说明，自行观看。


③填写正则矩阵。格式要求在代码区也有中文说明。
注意使用英文的标点才行，注意最后不要删除竖线之类的东西，
注意最后一个正则填完不要有逗号。
填写完大概长这样：


④正则在“三方块-正则”项里，建议用复制粘贴的形式去填写第③步里的正则内容，或者如图把正则名称简化后进行填写。
如果你不知道要填哪一些，就把“全局正则”里面的正则全部填入第③步里。


3、使用。
3.1、打开消息楼层显示。


3.2、
小总结使用时处于什么预设都不影响。
回到正常聊天界面，点击“测试用总结”，便会开始总结。
①正常聊天界面。
②第一次总结的黄色弹窗，是必然的，因为意思是“该聊天还没有聊天记录世界书，正在创建”，以后总结内容便会保存在里面，ai能够正常读取。
③总结成功的蓝色弹窗，并且对应楼层会有幽灵图标，代表该层楼被隐藏起来了。隐藏楼层的意义就是不再将这层楼的信息发送给ai，减少了上传的字数，避免了字数上限的限制。


④检查。在“书本图标-箭头所指的框”里，会出现一个“chat-****”的选项，那就是保存该总结的聊天世界书。
⑤选中以后，点开箭头，就能看到总结的内容，按需要可自行修改。


以上就是一次正常使用的小总结。建议在二三十层的时候总结前10层，以此类推。留一部分聊天记录，有助于ai保持格式、文风等，不要太急着总结。



以下是特别提醒。
①**qr第一行预设没填写。**
本质上这一行决定了这个总结的使用逻辑，没有将总结预设正确填入该行，会导致总结内容变成普通对话，或者其他错误情况。
②如果出现**从头开始总结**的情况，请手动删改Gemini Summary预设。

“前情提要”项，这块删掉。意思是不再把总结内容发给ai了，ai就不会从头总结了。但是这样ai的故事理解肯定会有缺失，所以总结内容要多注意修正。



**大总结**
下载、安装等方法同上，记得跟原贴步骤来，文件不一样而已。
注意，大总结使用的时候需要切换至大总结预设。
①大总结qr设置时点这里切换。
②使用时点这里把按钮换出来。
③用完后会出现一本新的世界书，里面是大总结的内容，将内容修改完后，覆盖原小总结累积的内容，只保留一本聊天记录世界书。

番外，一些机制讲解。
一、更改聊天记录世界书名称。
①在此处修改名称，我改成了默认卡总结。
②在此处重新关联这本聊天记录世界书，ai才能识别。


二、修改总结范围。默认是一次总结10层，可在qr界面改这一项数字。


