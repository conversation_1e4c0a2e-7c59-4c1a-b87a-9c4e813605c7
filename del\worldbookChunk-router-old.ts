import { z } from 'zod';

import { WorldbookModel } from '@/database/models/worldbook';
import { WorldbookChunkModel } from '@/database/models/worldbookChunk';
import { insertWorldbookChunksSchema } from '@/database/schemas';
import { authedProcedure, router } from '@/libs/trpc/lambda';
import { serverDatabase } from '@/libs/trpc/lambda/middleware';
import { CreateEntryData, SelectiveLogic, WorldbookEntry } from '@/types/worldbook';

// 搜索参数 schema
const searchParamsSchema = z.object({
  page: z.number().min(1).optional().default(1),
  pageSize: z.number().min(1).max(100).optional().default(20),
  query: z.string().optional(),
  enabled: z.boolean().optional(),
  constant: z.boolean().optional(),
  hasKeys: z.boolean().optional(),
  hasSecondaryKeys: z.boolean().optional(),
  groupName: z.string().optional(),
  selectiveLogic: z.string().optional(),
  orderMin: z.number().optional(),
  orderMax: z.number().optional(),
  probabilityMin: z.number().min(0).max(100).optional(),
  probabilityMax: z.number().min(0).max(100).optional(),
  sortBy: z.enum(['name', 'date', 'order', 'probability']).optional().default('order'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('asc'),
});

// 分页响应类型
interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

const worldbookChunkProcedure = authedProcedure.use(serverDatabase).use(async (opts) => {
  const { ctx } = opts;

  return opts.next({
    ctx: {
      worldbookChunkModel: new WorldbookChunkModel(ctx.serverDB, ctx.userId),
      worldbookModel: new WorldbookModel(ctx.serverDB, ctx.userId),
    },
  });
});

export const worldbookChunkRouter = router({
  // 获取世界书的所有条目（支持搜索和分页）
  getChunksByWorldbookId: worldbookChunkProcedure
    .input(z.object({
      worldbookId: z.string(),
      params: searchParamsSchema.optional()
    }))
    .query(async ({ ctx, input }): Promise<PaginatedResponse<WorldbookEntry>> => {
      const params = input.params || {};
      return ctx.worldbookChunkModel.findByWorldbookIdWithParams(input.worldbookId, params);
    }),

  // 根据ID获取单个条目
  getChunkById: worldbookChunkProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.worldbookChunkModel.findById(input.id);
    }),

  // 创建条目
  createChunk: worldbookChunkProcedure
    .input(
      z.object({
        worldbookId: z.string(),
        title: z.string(),
        content: z.string(),
        keys: z.array(z.string()),
        keysSecondary: z.array(z.string()).optional(),
        selectiveLogic: z.nativeEnum(SelectiveLogic).optional(),
        order: z.number().optional(),
        probability: z.number().min(0).max(100).optional(),
        constant: z.boolean().optional(),
        scanDepth: z.number().optional(),
        position: z.number().optional(),
        groupName: z.string().optional(),
        sticky: z.number().optional(),
        cooldown: z.number().optional(),
        delay: z.number().optional(),
        excludeRecursion: z.boolean().optional(),
        delayUntilRecursion: z.number().optional(),
        groupWeight: z.number().optional(),
        caseSensitive: z.boolean().optional(),
        matchWholeWords: z.boolean().optional(),
        useRegex: z.boolean().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return ctx.worldbookChunkModel.createWithContent(input);
    }),

  // 更新条目（支持更新元数据和内容）
  updateChunk: worldbookChunkProcedure
    .input(
      z.object({
        id: z.string(),
        value: insertWorldbookChunksSchema.partial().extend({
          content: z.string().optional(), // 支持更新内容
        }),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return ctx.worldbookChunkModel.update(input.id, input.value);
    }),

  // 更新条目内容（保持向后兼容）
  updateChunkContent: worldbookChunkProcedure
    .input(
      z.object({
        id: z.string(),
        content: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return ctx.worldbookChunkModel.update(input.id, { content: input.content });
    }),

  // 删除条目
  removeChunk: worldbookChunkProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      return ctx.worldbookChunkModel.delete(input.id);
    }),

  // 批量创建条目
  bulkCreateChunks: worldbookChunkProcedure
    .input(
      z.object({
        worldbookId: z.string(),
        chunks: z.array(
          z.object({
            title: z.string(),
            content: z.string(),
            keys: z.array(z.string()),
            keysSecondary: z.array(z.string()).optional(),
            selectiveLogic: z.nativeEnum(SelectiveLogic).optional(),
            order: z.number().optional(),
            probability: z.number().min(0).max(100).optional(),
            constant: z.boolean().optional(),
            scanDepth: z.number().optional(),
            position: z.number().optional(),
            groupName: z.string().optional(),
            sticky: z.number().optional(),
            cooldown: z.number().optional(),
            delay: z.number().optional(),
            excludeRecursion: z.boolean().optional(),
            delayUntilRecursion: z.number().optional(),
            groupWeight: z.number().optional(),
            caseSensitive: z.boolean().optional(),
            matchWholeWords: z.boolean().optional(),
            useRegex: z.boolean().optional(),
          }),
        ),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const chunksWithWorldbookId = input.chunks.map(chunk => ({
        ...chunk,
        worldbookId: input.worldbookId,
      }));
      return ctx.worldbookChunkModel.bulkCreate(chunksWithWorldbookId);
    }),

  // 获取可激活的条目
  getActivatableEntries: worldbookChunkProcedure
    .input(z.object({ worldbookIds: z.array(z.string()) }))
    .query(async ({ ctx, input }) => {
      return ctx.worldbookChunkModel.findActivatableEntries(input.worldbookIds);
    }),





  // 批量操作
  bulkUpdateChunks: worldbookChunkProcedure
    .input(
      z.object({
        ids: z.array(z.string()),
        value: insertWorldbookChunksSchema.partial(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // 逐个更新（可以后续优化为真正的批量操作）
      await Promise.all(
        input.ids.map(id => ctx.worldbookChunkModel.update(id, input.value))
      );
    }),

  bulkDeleteChunks: worldbookChunkProcedure
    .input(z.object({ ids: z.array(z.string()) }))
    .mutation(async ({ input, ctx }) => {
      // 逐个删除（可以后续优化为真正的批量操作）
      await Promise.all(
        input.ids.map(id => ctx.worldbookChunkModel.delete(id))
      );
    }),
});
