# SillyTavern 记忆方案全览

## 概述

SillyTavern 提供了三种主要的记忆管理方案，每种都有不同的技术原理和适用场景。本文档总结了所有实际可用的记忆解决方案。

---

## 1. 自动摘要记忆系统

### 1.1 原生记忆系统 (Memory Extension)

#### 技术原理
- **触发机制**: 每N条消息或累积字数达到阈值时自动触发
- **内容收集**: 从上次摘要点开始收集新消息，格式化为"角色名:\n消息内容"
- **AI生成**: 调用AI模型将收集的内容压缩为指定长度的摘要
- **存储方式**: 摘要保存在消息对象的`extra.memory`字段中
- **注入机制**: 通过`setExtensionPrompt`将摘要注入到AI上下文的指定位置

#### 工作流程
1. **触发检测**: 检查消息间隔和字数阈值
2. **内容收集**: 收集自上次摘要后的所有新消息
3. **Token控制**: 确保内容不超过模型上下文限制
4. **摘要生成**: 调用AI生成压缩摘要
5. **格式化存储**: 应用模板格式化并保存到消息
6. **上下文注入**: 将摘要注入到后续对话的系统提示中

#### 核心特性
- **智能管理**: 消息删除/编辑时自动调整记忆
- **多源支持**: 支持Extras API、主模型、WebLLM三种摘要源
- **Token预算**: 精确的Token计算和预算管理
- **错误恢复**: 摘要失败时保持系统稳定

### 1.2 社区增强版本

#### 增强原理
- **双层架构**: 小总结→大总结的递进式压缩
- **楼层管理**: 自动隐藏已总结的消息楼层
- **正则处理**: 多层正则表达式清理和格式化
- **世界书集成**: 总结内容自动存储到聊天记录世界书

#### 技术创新
- **失败保护**: 总结失败时不隐藏楼层，便于重试
- **范围检测**: 智能检测未隐藏楼层范围
- **预设切换**: 专用总结预设，优化摘要质量
- **批量操作**: 支持批量楼层隐藏和显示

#### 实现细节
- **QR脚本**: 通过快速回复脚本实现一键操作
- **楼层算法**: 从最新消息向前扫描未隐藏楼层
- **正则管道**: 多层正则表达式处理总结内容
- **世界书API**: 通过`/api/worldinfo/*`接口操作聊天记录世界书

---

## 2. 向量化记忆系统

### 2.1 vectors-enhanced 插件

#### 技术原理
- **向量化**: 将聊天消息转换为高维向量表示
- **语义检索**: 基于余弦相似度进行内容匹配
- **分块处理**: 长消息自动分块，提高检索精度
- **多任务管理**: 同一聊天可创建多个独立的向量化任务

#### 核心算法
- **内容提取**: 支持标签筛选、嵌套排除、正则过滤
- **重复检测**: 基于内容哈希的智能重复检测
- **相似度计算**: 实时计算查询向量与历史向量的相似度
- **阈值过滤**: 过滤低相似度内容，确保检索质量

#### 高级特性
- **标签筛选**: `content,thinking`简单提取，`content - thinking`排除模式
- **条件筛选**: `<details><summary>摘要</summary>,</details>`复杂结构提取
- **正则排除**: `/Step \d+:/gi`正则表达式过滤
- **黑名单机制**: 关键词黑名单过滤

#### 存储机制
- **批量处理**: 支持批量向量化和存储
- **增量更新**: 只处理新增内容，避免重复计算
- **持久化**: 向量数据持久化存储，跨会话保持
- **多后端支持**: Transformers、vLLM、Ollama多种后端

#### 实现细节
- **任务管理**: 每个向量化任务独立配置和管理
- **API集成**: 通过`/api/vector/*`接口与后端通信
- **缓存策略**: 本地缓存向量结果，减少重复计算
- **并发控制**: 支持多任务并发处理，提高效率

---

## 3. 结构化记忆插件

### 3.1 st-memory-enhancement

#### 技术原理
- **表格结构**: 基于CSV格式的结构化数据存储
- **模板系统**: JSON配置文件定义表格结构和字段类型
- **动态注入**: 根据表格内容动态生成提示词并注入AI上下文
- **分步操作**: 支持AI分步读取、编辑、整理表格内容

#### 核心架构
- **表格管理器**: 负责表格的创建、编辑、删除操作
- **模板引擎**: 处理表格结构定义和内容格式化
- **注入系统**: 将表格数据转换为AI可理解的提示词格式
- **UI界面**: 提供直观的表格编辑和管理界面

#### 数据结构
```
表格定义: {
  tableName: "表格名称",
  columns: ["列1", "列2", "列3"],
  rows: [
    {id: "行ID", data: {"列1": "值1", "列2": "值2"}},
    ...
  ]
}
```

#### 注入机制
- **深度注入**: 在指定深度的聊天历史中注入表格数据
- **系统注入**: 作为系统消息注入到对话开始
- **全局宏**: 通过宏系统在任意位置引用表格内容
- **世界书集成**: 将表格内容写入世界书条目

#### 智能功能
- **AI读表**: AI自动读取和理解表格内容
- **AI写表**: AI根据对话内容自动更新表格
- **分步填表**: 分步骤引导AI完成复杂表格填写
- **表格整理**: AI自动整理和优化表格结构

#### 实现细节
- **事件监听**: 通过`GENERATE_BEFORE`事件拦截AI生成前的处理
- **深度注入**: 在聊天历史的指定深度插入表格数据
- **模板渲染**: 使用Handlebars模板引擎格式化表格内容
- **状态同步**: 表格修改后实时同步到UI界面和存储

---

## 4. 深度技术对比

### 4.1 核心架构差异

| 维度 | 自动摘要 | 向量化 | 结构化 |
|------|----------|--------|--------|
| **数据结构** | 线性文本摘要 | 高维向量空间 | 二维表格结构 |
| **存储位置** | 消息extra字段 | 独立向量数据库 | JSON配置文件 |
| **检索方式** | 顺序读取注入 | 相似度计算匹配 | 键值对直接访问 |
| **更新机制** | 增量追加摘要 | 向量重新计算 | 实时表格编辑 |

### 4.2 性能特征对比

#### 内存占用
- **自动摘要**: 低（仅存储文本）
- **向量化**: 高（向量数据 + 缓存）
- **结构化**: 中（表格数据 + UI状态）

#### 计算复杂度
- **自动摘要**: O(n) - 线性文本处理
- **向量化**: O(n×d) - 向量相似度计算
- **结构化**: O(1) - 直接表格访问

#### 响应延迟
- **自动摘要**: 中等（需要AI生成摘要）
- **向量化**: 低（预计算向量快速检索）
- **结构化**: 极低（直接数据访问）

### 4.3 功能深度对比

#### 信息保真度
- **自动摘要**: 60-80%（压缩损失）
- **向量化**: 85-95%（语义保留）
- **结构化**: 100%（完整保存）

#### 语义理解能力
- **自动摘要**: 高（AI理解生成）
- **向量化**: 中（向量空间映射）
- **结构化**: 低（结构化存储）

#### 扩展性
- **自动摘要**: 中（受摘要长度限制）
- **向量化**: 高（向量维度可扩展）
- **结构化**: 低（表格结构相对固定）

### 4.4 使用场景适配度

#### 长期对话管理
- **自动摘要**: ★★★★★（专为此设计）
- **向量化**: ★★★☆☆（检索历史内容）
- **结构化**: ★★☆☆☆（不适合流式对话）

#### 角色扮演
- **自动摘要**: ★★★☆☆（基础记忆支持）
- **向量化**: ★★★★☆（智能回忆相关情节）
- **结构化**: ★★★★★（精确角色设定管理）

#### 知识管理
- **自动摘要**: ★★☆☆☆（信息压缩损失）
- **向量化**: ★★★★★（语义检索优势）
- **结构化**: ★★★★☆（结构化知识存储）

### 4.5 技术成熟度分析

#### 开发难度
- **自动摘要**: 低（基于现有AI能力）
- **向量化**: 高（需要向量模型和算法）
- **结构化**: 中（UI和数据管理）

#### 维护成本
- **自动摘要**: 低（自动化程度高）
- **向量化**: 中（需要定期优化向量库）
- **结构化**: 高（需要手动维护表格）

#### 错误恢复
- **自动摘要**: 好（可重新生成摘要）
- **向量化**: 中（向量损坏需重建）
- **结构化**: 优（数据结构清晰易修复）

---

## 5. 实际应用建议

### 5.1 场景化选择策略

#### 日常聊天场景
**推荐**: 自动摘要系统
- 自动处理对话历史
- 无需手动维护
- 适合长期连续对话

#### 创作写作场景
**推荐**: 结构化系统 + 向量化系统
- 结构化管理角色设定
- 向量化检索相关情节
- 支持复杂故事线

#### 学习研究场景
**推荐**: 向量化系统
- 语义检索知识点
- 快速定位相关内容
- 支持大量资料管理

### 5.2 组合使用策略

#### 基础组合（新手）
- **自动摘要** 作为基础记忆
- 简单易用，覆盖基本需求

#### 进阶组合（中级用户）
- **自动摘要** + **结构化系统**
- 自动记忆 + 精确控制
- 适合角色扮演场景

#### 专业组合（高级用户）
- **向量化** + **结构化** + **自动摘要**
- 全方位记忆管理
- 适合复杂应用场景

### 5.3 性能优化建议

#### 自动摘要优化
- 调整摘要间隔（建议10-20条消息）
- 控制摘要长度（建议200-500字）
- 选择合适的摘要模型

#### 向量化优化
- 设置合理的相似度阈值（0.7-0.8）
- 控制检索数量（3-5条相关内容）
- 定期清理向量缓存

#### 结构化优化
- 设计简洁的表格结构
- 避免过于复杂的嵌套
- 定期整理表格内容

---

## 6. 总结

SillyTavern的三种记忆系统各有特色：

1. **自动摘要**: 实用的对话历史压缩方案
2. **向量化**: 智能的语义检索系统
3. **结构化**: 精确的数据管理工具

选择记忆系统需要考虑：
- **使用场景**: 不同场景有不同的最优选择
- **技术能力**: 复杂系统需要更多技术投入
- **维护成本**: 平衡功能需求与维护负担
- **组合效果**: 多系统组合往往效果更佳

建议从自动摘要系统开始，根据实际需求逐步引入其他方案，最终形成适合自己的记忆管理策略。
