# 聊天记录超级管理器

为 SillyTavern 设计的增强版向量数据库插件，提供多任务管理、智能内容筛选和精细化上下文控制功能。

## 主要功能

### 多任务向量化管理
- 为同一个聊天创建多个独立的向量化任务
- 每个任务可包含不同的内容组合（聊天记录、文件、世界信息）
- 支持任务的独立启用/禁用，实现灵活的上下文切换
- 任务持久化存储，聊天切换后自动恢复

### 高级标签筛选与排除
支持多种标签筛选方式，从简单提取到复杂条件匹配：

**简单标签提取**
```
配置：content,thinking
提取：<content>...</content> 和 <thinking>...</thinking> 内容
```

**条件性HTML标签**
```
配置：<details><summary>摘要</summary>,</details>
提取：只提取标题为"摘要"的details块内容
```

**嵌套标签排除**
```
配置：content - thinking,analysis
效果：提取content内容但移除其中的thinking和analysis标签
```

**正则表达式排除**
```
配置：content - /Step \d+:/gi
效果：移除"Step 1:", "Step 2:"等模式
```

**内容黑名单过滤**
- 设置关键词黑名单，跳过包含指定词汇的内容块
- 支持多行配置，每行一个关键词

**处理流水线**
1. 提取指定标签
2. 移除嵌套排除标签  
3. 应用黑名单过滤
4. 合并最终结果

### 智能重复内容检测
- 基于内容哈希的重复检测机制
- 分析新选择与现有任务的重叠情况
- 支持增量处理，只向量化新增内容
- 检测范围包括聊天消息、文件和世界信息

### 隐藏消息管理
- 批量隐藏/显示指定范围的聊天消息
- 实时显示隐藏消息数量和范围
- 查看隐藏消息内容的弹窗界面
- 向量化时可选择是否包含隐藏消息
- 与SillyTavern原生/hide、/unhide指令兼容

### 精细化内容选择

**聊天记录**
- 自定义消息范围（起始和结束位置）
- 按消息类型筛选（用户消息、AI消息、系统消息）
- 高级标签内容提取
- 隐藏消息包含选项

**文件支持**
- DataBank文件和聊天附件
- 支持.txt、.md、.json等文本格式
- 文件内容预览和选择

**世界信息**
- 按世界分组显示
- 独立选择每个条目
- 实时同步世界信息变更

### 查询结果通知
- 实时显示向量查询结果数量
- 详细模式：按来源类型显示统计分布
- 智能冷却机制防止重复通知
- 可完全关闭或自定义通知级别

### 多后端支持
- Transformers：SillyTavern内置向量化支持
- vLLM：外部vLLM服务器集成
- Ollama：支持keep_alive参数的Ollama集成

### 自定义注入控制
- 自定义注入模板，支持{{text}}占位符
- 为不同内容源设置XML风格标签
- 灵活的注入位置：主提示前/后、聊天记录任意深度
- 可配置注入深度和角色权限

## 安装和配置

1. 将插件文件放置在SillyTavern的第三方扩展目录
2. 在扩展管理界面启用"聊天记录超级管理器"
3. 在API设置中配置向量化后端（Transformers/vLLM/Ollama）
4. 在插件设置面板中配置基础参数

## 使用方法

### 基础使用流程
1. 打开右侧面板的"聊天记录超级管理器"设置
2. 在"向量化设置"中选择后端和模型
3. 在"内容选择"中配置要向量化的内容：
   - 聊天消息：设置范围、类型、标签提取规则
   - 文件：从列表中选择需要的文件
   - 世界信息：按世界选择相关条目
4. 配置"注入设置"：模板、标签、位置等
5. 使用预览功能检查选择的内容
6. 点击"向量化"按钮创建任务
7. 在"向量化任务"中启用需要的任务
8. 正常对话，系统自动查询和注入相关内容

### 高级标签筛选配置
在聊天消息的"标签内容提取"字段中配置：

**基础用法**
```
content,thinking
```

**条件筛选**
```
<details><summary>角色设定</summary>,</details>
```

**排除嵌套标签**
```
content - thinking,analysis,draft
```

**正则排除**
```
content - /Step \d+:/gi,/思考过程:/g
```

**组合使用**
```
content - thinking,<details><summary>摘要</summary>,</details>,memory
```

### 隐藏消息管理
1. 设置要隐藏的消息范围（起始和结束位置）
2. 点击"隐藏范围"或"显示范围"按钮
3. 使用"查看隐藏消息"查看已隐藏的内容
4. 在向量化时勾选"包含隐藏消息"决定是否处理隐藏内容

### 重复内容处理
- 系统自动检测内容重叠
- 出现重复时提示用户选择处理方式
- 支持增量处理，只向量化新增部分
- 保持数据一致性，避免重复存储

## 斜杠命令

- `/vec-preview` - 预览当前选择的内容
- `/vec-export` - 导出选择的内容为文本文件
- `/vec-process` - 执行向量化处理

## 技术细节

### 向量化流程
1. 内容提取和标签筛选
2. 文本分块（可配置块大小和重叠）
3. 重复内容检测
4. 批量向量化处理
5. 任务创建和元数据存储

### API集成
使用SillyTavern的向量API：
- `/api/vector/insert` - 批量插入向量数据
- `/api/vector/query` - 相似度搜索
- `/api/vector/list` - 获取已存储的哈希列表
- `/api/vector/purge` - 删除向量集合

### 数据结构
- 向量数据包含文本、哈希、来源、时间戳等元数据
- 任务配置持久化在扩展设置中
- 缓存机制优化查询性能

## 配置参数说明

### 向量化设置
- 向量源：选择后端类型
- 模型名称：指定使用的嵌入模型
- 文本块大小：单个向量的文本长度
- 重叠大小：相邻块的重叠字符数
- 批次大小：批量处理的向量数量

### 查询设置
- 查询消息数：用于查询的最近消息数量
- 最大结果数：返回的向量搜索结果数量
- 分数阈值：相似度匹配的最低分数

### 注入设置
- 注入模板：自定义内容格式，使用{{text}}占位符
- 内容标签：不同来源的XML标签名称
- 注入位置：在提示中的插入位置
- 注入深度：在聊天历史中的插入深度

## 版本历史

### v1.0.2
- 修复了已知问题

### v1.2.0
- 新增嵌套标签排除功能
- 添加内容过滤黑名单
- 实现智能重复内容检测
- 增加查询结果通知系统
- 完善隐藏消息管理功能

---

作者：RaphllA  
项目地址：https://github.com/RaphllA/vectors-enhanced